<?php

declare(strict_types=1);

namespace Cdn77\Api\PHPStan\ShipmonkDeadCode;

use Cdn77\Api\Core\Application\Payload\HasReference;
use ReflectionMethod;
use ShipMonk\PHPStan\DeadCode\Provider\ReflectionBasedMemberUsageProvider;
use ShipMonk\PHPStan\DeadCode\Provider\VirtualUsageData;

final class OpenApiSpecProvider extends ReflectionBasedMemberUsageProvider
{
    public function shouldMarkMethodAsUsed(ReflectionMethod $method): VirtualUsageData|null
    {
        $declaringClass = $method->getDeclaringClass();

        $isUsedMethod = $declaringClass->isInterface()
            && $method->getName() === 'reference'
            && $declaringClass->getName() === HasReference::class;

        return $isUsedMethod
            ? VirtualUsageData::withNote('OpenAPI spec reference method is called directly via implementation.')
            : null;
    }
}
