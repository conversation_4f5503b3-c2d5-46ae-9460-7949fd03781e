<?php

declare(strict_types=1);

namespace Cdn77\Api\PHPStan\ShipmonkDeadCode;

use Cdn77\Api\Core\Domain\Messaging\CommandHandler;
use Cdn77\Api\Core\Domain\Messaging\QueryHandler;
use ReflectionMethod;
use ShipMonk\PHPStan\DeadCode\Provider\ReflectionBasedMemberUsageProvider;
use ShipMonk\PHPStan\DeadCode\Provider\VirtualUsageData;

final class BusEntrypointProvider extends ReflectionBasedMemberUsageProvider
{
    public function shouldMarkMethodAsUsed(ReflectionMethod $method): VirtualUsageData|null
    {
        $declaringClass = $method->getDeclaringClass();

        $isUsedMethod = $method->getName() === 'handle'
            && (
                $declaringClass->implementsInterface(CommandHandler::class)
                || $declaringClass->implementsInterface(QueryHandler::class)
            );

        return $isUsedMethod
            ? VirtualUsageData::withNote(
                'Command bus command/query handlers are called indirectly via auto discovery.',
            )
            : null;
    }
}
