# CLAP - Client API

## Installation via Docker

### 1. Workspace

You need to download workspace (postgres/adminer) from `https://gitlab.cdn77.eu/cdn/docker-workspace`
and run `make` or `docker-compose up -d`

### 2. Project Setup
#### 2.1 Docker

In this repository run:

`docker-compose up -d`

and import all migrations to setup your local test db:

`docker-compose run php-cli bin/console doctrine:migrations:migrate -n -q --all-or-nothing`

#### 2.2 Requirements & Dependencies

* PHP 8.2 with ext-intl, ext-pdo, ext-pdo_pgsql, ext-openssl, ext-json, ext-filter & ext-ds installed
* Run `composer install` to install dependencies
* Run `yarn` to install `husky` for git commit message linter
    * CLAP uses conventional commits validation for more info about conventional commits go to: `https://www.conventionalcommits.org/en/v1.0.0/`

#### 2.3 Environment

You have to add `.env` and add local variable from `.env.dist` to run locally or with your dev server setup

Then go to http://clap.localhost/ or use `clap.localhost` as your base uri for any REST api calls.

### 3. Tests & QA

For tests add `.env.test` file and set your `ENV` variable to `ENV=test`

For local docker database use `DATABASE_URL="********************************************/api?serverVersion=10"`

CLAP also uses phpstan for static analysis use:

`./vendor/bin/phpstan`

To run coding standard check use:

`./vendor/bin/phpcs`

Optionally use autofix to resolve coding standard issues with:

`./vendor/bin/phpcbf`

### 4. Sailor

To generate GraphQL client operation endpoints run sailor `php vendor/bin/sailor introspect clap`

### 5. Rector

CLAP can use `rector/rector` for refactoring operations, find the configuration in `./rector.php` and optionally run with:

`./vendor/bin/rector`
