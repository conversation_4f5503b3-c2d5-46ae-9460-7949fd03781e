parameters:
    shipmonkRules:
        allowComparingOnlyComparableTypes:
            enabled: true
        backedEnumGenerics:
            enabled: true
        classSuffixNaming:
            enabled: false
        enforceClosureParamNativeTypehint:
            enabled: false
        enforceEnumMatch:
            enabled: true
        enforceIteratorToArrayPreserveKeys:
            enabled: false
        enforceListReturn:
            enabled: true
        enforceNativeReturnTypehint:
            enabled: false
        enforceReadonlyPublicProperty:
            enabled: false
        forbidArithmeticOperationOnNonNumber:
            enabled: true
            allowNumericString: true
        forbidCast:
            enabled: false
            blacklist: ['(array)', '(object)', '(unset)']
        forbidCheckedExceptionInCallable:
            enabled: false
        forbidCheckedExceptionInYieldingMethod:
            enabled: false
        forbidCustomFunctions:
            enabled: false
            list: []
        forbidEnumInFunctionArguments:
            enabled: true
        forbidIdenticalClassComparison:
            enabled: false
            blacklist: ['DateTimeInterface']
        forbidIncrementDecrementOnNonInteger:
            enabled: true
        forbidMatchDefaultArmForEnums:
            enabled: true
        forbidNotNormalizedType:
            enabled: false
            checkDisjunctiveNormalForm: true
        forbidNullInAssignOperations:
            enabled: true
            blacklist: ['??=']
        forbidNullInBinaryOperations:
            enabled: true
            blacklist: ['===', '!==', '??']
        forbidNullInInterpolatedString:
            enabled: true
        forbidPhpDocNullabilityMismatchWithNativeTypehint:
            enabled: true
        forbidProtectedEnumMethod:
            enabled: true
        forbidReturnValueInYieldingMethod:
            enabled: true
            reportRegardlessOfReturnType: true
        forbidVariableTypeOverwriting:
            enabled: false
        forbidUnsetClassField:
            enabled: true
        forbidUselessNullableReturn:
            enabled: true
        forbidUnusedException:
            enabled: true
        forbidUnusedMatchResult:
            enabled: false
        requirePreviousExceptionPass:
            enabled: false
            reportEvenIfExceptionIsNotAcceptableByRethrownOne: true
        uselessPrivatePropertyDefaultValue:
            enabled: false
