<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/10.1/phpunit.xsd"
         colors="true"
         beStrictAboutChangesToGlobalState="true"
         beStrictAboutOutputDuringTests="true"
         bootstrap="tests/bootstrap.php"
         cacheDirectory=".phpunit.cache"
         displayDetailsOnTestsThatTriggerDeprecations="true"
         displayDetailsOnTestsThatTriggerWarnings="true"
         executionOrder="random"
         failOnEmptyTestSuite="true"
         failOnIncomplete="true"
         failOnRisky="true"
         failOnWarning="true"
         requireCoverageMetadata="true"
>
  <coverage/>
  <extensions>
    <bootstrap class="DAMA\DoctrineTestBundle\PHPUnit\PHPUnitExtension"/>
    <bootstrap class="Ergebnis\PHPUnit\SlowTestDetector\Extension"/>
  </extensions>
  <testsuites>
    <testsuite name="CDN77 Client API v3 Test Suite">
      <directory>tests/</directory>
    </testsuite>
  </testsuites>
  <source>
    <include>
      <directory>src/</directory>
    </include>
  </source>
</phpunit>
