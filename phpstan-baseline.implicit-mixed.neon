parameters:
	ignoreErrors:
		-
			message: '#^Parameter \#1 \$headers of class Cdn77\\Api\\Cdn\\Domain\\Value\\Parameters\\CustomOriginHeaders constructor expects Ds\\Map\<string, string\>, Ds\\Map\<mixed, mixed\> given\.$#'
			identifier: argument.type
			count: 1
			path: src/Cdn/Domain/Value/Parameters/CustomOriginHeaders.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Infrastructure\\Finder\\DbalOriginIdFinder\:\:getForCdns\(\) should return Ds\\Map\<Cdn77\\Api\\Core\\Domain\\Entity\\Cdn\\CdnId, Cdn77\\Api\\Core\\Domain\\Entity\\Origin\\OriginId\> but returns Ds\\Map\<mixed, mixed\>\.$#'
			identifier: return.type
			count: 1
			path: src/Cdn/Infrastructure/Finder/DbalOriginIdFinder.php

		-
			message: '#^Method Cdn77\\Api\\Cdn\\Infrastructure\\Finder\\DbalStreamingPlaylistBypassEnabledStatusFinder\:\:findForCdns\(\) should return Ds\\Map\<Cdn77\\Api\\Core\\Domain\\Entity\\Cdn\\CdnId, bool\> but returns Ds\\Map\<mixed, mixed\>\.$#'
			identifier: return.type
			count: 1
			path: src/Cdn/Infrastructure/Finder/DbalStreamingPlaylistBypassEnabledStatusFinder.php

		-
			message: '#^Parameter \#1 \$callback of function array_map expects \(callable\(mixed\)\: mixed\)\|null, Closure\(string\)\: non\-falsy\-string given\.$#'
			identifier: argument.type
			count: 1
			path: src/Core/Application/OpenApi/PathGenerator.php

		-
			message: '#^Parameter \#3 \$attachment of class Cdn77\\Api\\Core\\Domain\\Postmark\\Value\\MailTemplates\\CustomPlanInvoice constructor expects Postmark\\Models\\PostmarkAttachment\|null, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: src/Core/Domain/Postmark/Value/MailTemplates/CustomPlanInvoice.php

		-
			message: '#^Parameter \#3 \$attachment of class Cdn77\\Api\\Core\\Domain\\Postmark\\Value\\MailTemplates\\CustomPlanInvoiceWithPaymentLink constructor expects Postmark\\Models\\PostmarkAttachment\|null, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: src/Core/Domain/Postmark/Value/MailTemplates/CustomPlanInvoiceWithPaymentLink.php

		-
			message: '#^Cannot call method (andWhere|execute|getIterator|where|page|pageSize)\(\) on mixed\.$#'
			identifier: method.nonObject
			count: 10
			path: src/Core/Infrastructure/Billing/UsdXeroBiller.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Ceph\\FakeRgwAdminApi\:\:getStats\(\) should return Ds\\Map\<string, Cdn77\\Api\\Core\\Domain\\Value\\ObjectStorage\\ObjectStorageUsage\> but returns Ds\\Map\<mixed, mixed\>\.$#'
			identifier: return.type
			count: 1
			path: src/Core/Infrastructure/Ceph/FakeRgwAdminApi.php

		-
			message: '#^Cannot call method ip\(\) on mixed\.$#'
			identifier: method.nonObject
			count: 1
			path: src/Core/Infrastructure/ClickHouse/HttpClientFactory.php

		-
			message: '#^Parameter \#1 \$ipAddress of static method Cdn77\\Api\\Core\\Domain\\Value\\IpAddress\:\:ipv4\(\) expects string, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: src/Core/Infrastructure/ClickHouse/HttpClientFactory.php

		-
			message: '#^Property Cdn77\\Api\\Core\\Infrastructure\\Currency\\BrickCurrencyConverter\:\:\$converters \(Ds\\Map\<string, Brick\\Money\\CurrencyConverter\>\) does not accept Ds\\Map\<mixed, mixed\>\.$#'
			identifier: assign.propertyType
			count: 1
			path: src/Core/Infrastructure/Currency/BrickCurrencyConverter.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\NxgApi\\FakeNxgApi\:\:listCertificatesForResources\(\) should return Ds\\Map\<Cdn77\\Api\\Core\\Domain\\Entity\\Cdn\\CdnId, Cdn77\\Api\\Core\\Domain\\NxgApi\\Dto\\SslCertificate\> but returns Ds\\Map\<mixed, mixed\>\.$#'
			identifier: return.type
			count: 1
			path: src/Core/Infrastructure/NxgApi/FakeNxgApi.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\NxgApi\\HttpNxgApi\:\:listCertificatesForResources\(\) should return Ds\\Map\<Cdn77\\Api\\Core\\Domain\\Entity\\Cdn\\CdnId, Cdn77\\Api\\Core\\Domain\\NxgApi\\Dto\\SslCertificate\> but returns Ds\\Map\<mixed, mixed\>\.$#'
			identifier: return.type
			count: 1
			path: src/Core/Infrastructure/NxgApi/HttpNxgApi.php

		-
			message: '#^Method Cdn77\\Api\\Core\\Infrastructure\\Repository\\Cname\\DoctrineCnameRepository\:\:findForCdns\(\) should return Ds\\Map\<Cdn77\\Api\\Core\\Domain\\Entity\\Cdn\\CdnId, Ds\\Set\<Cdn77\\Api\\Core\\Domain\\Entity\\Cname\\Cname\>\> but returns Ds\\Map\<mixed, mixed\>\.$#'
			identifier: return.type
			count: 1
			path: src/Core/Infrastructure/Repository/Cname/DoctrineCnameRepository.php

		-
			message: '#^Method Cdn77\\Api\\CustomPlan\\Infrastructure\\Finder\\DoctrineAlternativeMailsFinder\:\:findForCustomers\(\) should return Ds\\Map\<Cdn77\\Api\\Core\\Domain\\Entity\\Customer\\CustomerId, Cdn77\\Api\\CustomPlan\\Domain\\Dto\\AlternativeMails\> but returns Ds\\Map\<mixed, mixed\>\.$#'
			identifier: return.type
			count: 2
			path: src/CustomPlan/Infrastructure/Finder/DoctrineAlternativeMailsFinder.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Infrastructure\\Repository\\DoctrineCustomerPaymentSettingsRepository\:\:getForCustomers\(\) should return Ds\\Map\<Cdn77\\Api\\Core\\Domain\\Entity\\Customer\\CustomerId, Cdn77\\Api\\Core\\Domain\\Entity\\Customer\\CustomerPaymentSettings\> but returns Ds\\Map\<mixed, mixed\>\.$#'
			identifier: return.type
			count: 1
			path: src/Customer/Infrastructure/Repository/DoctrineCustomerPaymentSettingsRepository.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Infrastructure\\Repository\\DoctrineCustomerTrafficRepository\:\:getForCustomers\(\) should return Ds\\Map\<Cdn77\\Api\\Core\\Domain\\Entity\\Customer\\CustomerId, Cdn77\\Api\\Core\\Domain\\Entity\\Customer\\CustomerTraffic\> but returns Ds\\Map\<mixed, mixed\>\.$#'
			identifier: return.type
			count: 1
			path: src/Customer/Infrastructure/Repository/DoctrineCustomerTrafficRepository.php

		-
			message: '#^Parameter \#1 \$string1 of function strcmp expects string, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: src/GraphQL/Application/Runtime/SchemaFactory.php

		-
			message: '#^Parameter \#2 \$string2 of function strcmp expects string, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: src/GraphQL/Application/Runtime/SchemaFactory.php

		-
			message: '#^Property Cdn77\\Api\\GraphQL\\Domain\\DataResolution\\DataLoader\\DataLoaderFactory\:\:\$dataProvidersByType \(Ds\\Map\<string, Cdn77\\Api\\GraphQL\\Domain\\DataResolution\\DataProvider\<Cdn77\\Api\\GraphQL\\Domain\\Schema\\Type\\Dto\\DataTransferObject\>\>\) does not accept Ds\\Map\<mixed, mixed\>\.$#'
			identifier: assign.propertyType
			count: 1
			path: src/GraphQL/Domain/DataResolution/DataLoader/DataLoaderFactory.php

		-
			message: '#^Anonymous function should return Cdn77\\Api\\GraphQL\\Domain\\Schema\\Type\\Billing\\Dto\\BillingOverview\|null but returns mixed\.$#'
			identifier: return.type
			count: 1
			path: src/GraphQL/Domain/DataResolution/Type/Query/Billing/BillingOverviewFieldFactory.php

		-
			message: '#^Property Cdn77\\Api\\GraphQL\\Domain\\Schema\\Type\\TypeBag\:\:\$types \(Ds\\Map\<string, GraphQL\\Type\\Definition\\NamedType&GraphQL\\Type\\Definition\\Type\>\) does not accept Ds\\Map\<mixed, mixed\>\.$#'
			identifier: assign.propertyType
			count: 1
			path: src/GraphQL/Domain/Schema/Type/TypeBag.php

		-
			message: '#^Method Cdn77\\Api\\Monitoring\\Overkill\\Infrastructure\\Finder\\ClickhouseCephObjectStorageEgressTrafficFinder\:\:getTrafficBps\(\) should return Ds\\Map\<Cdn77\\Api\\Core\\Domain\\Entity\\Customer\\CustomerUuid, Cdn77\\Api\\Core\\Domain\\Value\\BitsPerSecond\> but returns Ds\\Map\<mixed, mixed\>\.$#'
			identifier: return.type
			count: 1
			path: src/Monitoring/Overkill/Infrastructure/Finder/ClickhouseCephObjectStorageEgressTrafficFinder.php

		-
			message: '#^Parameter \#1 \$certificate of function openssl_x509_parse expects OpenSSLCertificate\|string, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: src/Origin/Domain/Ssl/SslCertificateValidator.php

		-
			message: '#^Parameter \#2 \$timestamp of function Safe\\date expects int\|null, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: src/Origin/Domain/Ssl/SslCertificateValidator.php

		-
			message: '#^Method Cdn77\\Api\\Plan\\Infrastructure\\Finder\\DbalPlansLocationsFinder\:\:findForPlans\(\) should return Ds\\Map\<Cdn77\\Api\\Core\\Domain\\Entity\\Plan\\PlanId, Ds\\Set\<Cdn77\\Api\\Core\\Domain\\Entity\\Datacenter\\LocationId\>\> but returns Ds\\Map\<mixed, mixed\>\.$#'
			identifier: return.type
			count: 1
			path: src/Plan/Infrastructure/Finder/DbalPlansLocationsFinder.php

		-
			message: '#^Method Cdn77\\Api\\Plan\\Infrastructure\\Repository\\DoctrinePlanRepository\:\:getForIds\(\) should return Ds\\Map\<Cdn77\\Api\\Core\\Domain\\Entity\\Plan\\PlanId, Cdn77\\Api\\Core\\Domain\\Entity\\Plan\\Plan\> but returns Ds\\Map\<mixed, mixed\>\.$#'
			identifier: return.type
			count: 1
			path: src/Plan/Infrastructure/Repository/DoctrinePlanRepository.php

		-
			message: '#^Method Cdn77\\Api\\RawLog\\Domain\\Query\\FindSampleLogsHandler\:\:handle\(\) should return Ds\\Map\<DateTimeImmutable, Cdn77\\Api\\RawLog\\Domain\\Dto\\LogLine\> but returns Ds\\Map\<mixed, mixed\>\.$#'
			identifier: return.type
			count: 2
			path: src/RawLog/Domain/Query/FindSampleLogsHandler.php

		-
			message: '#^Parameter \#1 \$ipAddress of static method Cdn77\\Api\\Core\\Domain\\Value\\IpAddress\:\:fromString\(\) expects string, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: src/Server/Domain/Dto/Server.php

		-
			message: '#^Parameter \#2 \$mapper of function Cdn77\\Functions\\setFromIterable expects callable\(mixed, mixed\)\: Cdn77\\Api\\Core\\Domain\\Value\\IpAddress, Closure\(mixed, array\)\: Cdn77\\Api\\Core\\Domain\\Value\\IpAddress given\.$#'
			identifier: argument.type
			count: 1
			path: src/Server/Domain/Dto/Server.php

		-
			message: '#^Method Cdn77\\Api\\Server\\Infrastructure\\Finder\\DoctrineServerIdsFinder\:\:findForLocationIds\(\) should return Ds\\Map\<Cdn77\\Api\\Core\\Domain\\Entity\\Datacenter\\LocationId, Ds\\Set\<Cdn77\\Api\\Core\\Domain\\Entity\\Datacenter\\ServerId\>\> but returns Ds\\Map\<mixed, mixed\>\.$#'
			identifier: return.type
			count: 1
			path: src/Server/Infrastructure/Finder/DoctrineServerIdsFinder.php

		-
			message: '#^Parameter \#2 \.\.\.\$values of function sprintf expects bool\|float\|int\|string\|null, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: src/Service/PushZone/Api/ApiClient.php

		-
			message: '#^Parameter \#1 \$haystack of function str_ends_with expects string, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: src/Ssl/Domain/Parser/PublicCertificateParser.php

		-
			message: '#^Parameter \#1 \$haystack of function str_starts_with expects string, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: src/Ssl/Domain/Parser/PublicCertificateParser.php

		-
			message: '#^Parameter \#1 \$haystack of function substr_count expects string, mixed given\.$#'
			identifier: argument.type
			count: 2
			path: src/Ssl/Domain/Parser/PublicCertificateParser.php

		-
			message: '#^Parameter \#1 \$sumByDataCenter of class Cdn77\\Api\\Stats\\Domain\\Value\\DailySumByDataCenter constructor expects Ds\\Map\<Cdn77\\Api\\Core\\Domain\\Entity\\Datacenter\\LocationUuid, Brick\\Math\\BigDecimal\>, Ds\\Map\<mixed, mixed\> given\.$#'
			identifier: argument.type
			count: 1
			path: src/Stats/Domain/Value/DailySumByDataCenter.php

		-
			message: '#^Method Cdn77\\Api\\Ticket\\Infrastructure\\Finder\\DbalCustomerIdFinder\:\:findForEmails\(\) should return Ds\\Map\<Cdn77\\Api\\Core\\Domain\\Value\\Customer\\EmailAddress, Cdn77\\Api\\Core\\Domain\\Entity\\Customer\\CustomerUuid\> but returns Ds\\Map\<mixed, mixed\>\.$#'
			identifier: return.type
			count: 1
			path: src/Ticket/Infrastructure/Finder/DbalCustomerIdFinder.php

		-
			message: '#^Parameter \#1 \$string1 of function strcmp expects string, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: tests/Cdn/Application/Controller/ListControllerTest.php

		-
			message: '#^Parameter \#2 \$string2 of function strcmp expects string, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: tests/Cdn/Application/Controller/ListControllerTest.php

		-
			message: '#^Parameter \#1 \$string1 of function strcmp expects string, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: tests/Cname/Application/Controller/ListControllerTest.php

		-
			message: '#^Parameter \#2 \$string2 of function strcmp expects string, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: tests/Cname/Application/Controller/ListControllerTest.php

		-
			message: '#^Parameter \#1 \$array of function usort expects TArray of array\<array\>, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: tests/Storage/Console/UpdateRemoteServerEndpointTest.php

		-
			message: '#^Parameter \#1 \$string1 of function strcmp expects string, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: tests/Storage/Console/UpdateRemoteServerEndpointTest.php

		-
			message: '#^Parameter \#2 \$string2 of function strcmp expects string, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: tests/Storage/Console/UpdateRemoteServerEndpointTest.php
