include:
    -   project: simPod/gitlab-templates
        file: "Workflows/Push-Pipelines.gitlab-ci.yaml"
    -   project: simPod/gitlab-templates
        file: "Docker/build-docker-image.yaml"
    -   project: simPod/gitlab-templates
        file: "Php/composer-install.yaml"
    -   project: simPod/gitlab-templates
        file: "Js/yarn-install.yaml"
    -   project: simPod/gitlab-templates
        file: "Misc/deploy-swap-links.yaml"

default:
    image: registry.gitlab.cdn77.eu/purple-team/ci-cd/images/php-node

stages:
    - Quality Assurance
    - Release
    - Deploy

.localJob:
    stage: Quality Assurance

.localComposerJob:
    extends:
        - .localJob
        - .composer-install
    before_script:
        - !reference [ .composer-install-before-script, before_script ]
    variables:
        GIT_CLONE_PATH: $CI_BUILDS_DIR/$CI_CONCURRENT_ID/$CI_PROJECT_PATH

.localYarnJob:
    extends: .localJob
    before_script:
        - !reference [.yarn-install-before-script, before_script]

Composer Normalize:
    extends: .localComposerJob
    script: composer normalize --dry-run

Coding Standard:
    extends: .localComposerJob
    script: make cs

Layer Architecture Analysis:
    extends: .localComposerJob
    script: make analyze-dependencies

Composer Dependency Analysis:
    extends: .localComposerJob
    script: vendor/bin/composer-dependency-analyser

Static Analysis with Phpstan:
    extends: .localComposerJob
    script:
        - bin/console cache:warmup -n --env=test
        - vendor/bin/phpstan analyse -v --no-progress
    variables:
        ENV: ci
        SYMFONY_ENV: test
        SYMFONY_DEBUG: 1
        DATABASE_URL: *****************************************************************
        DATABASE_MIGRATION_URL: *****************************************************************
        CDN_RESTRICTION_ACCOUNT_ID_FROM: 1000000
        TRUSTED_PROXIES: 127.0.0.1

Commit message lint:
    extends: .localYarnJob
    script: yarn run commitlint --from origin/$CI_DEFAULT_BRANCH --to HEAD -V
    rules:
        - if: $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH

Rector:
    extends: .localComposerJob
    script: make rector

Unit Tests:
    extends: .localComposerJob
    script: make test-unit
    coverage: /^\s*Lines:\s*\d+.\d+\%/
    artifacts:
        when: always
        reports:
            coverage_report:
                coverage_format: cobertura
                path: php-tests-unit-cobertura.xml
        paths:
            - php-tests-unit-cobertura.xml

Integration Tests:
    extends: .localComposerJob
    services:
        -   name: postgres:17
            command: ['postgres', '-c', 'fsync=off', '-c', 'full_page_writes=off']
    script: make test-integration
    variables:
        ENV: ci
        SYMFONY_ENV: test
        SYMFONY_SECRET: abcdefgh12345
        SYMFONY_DEBUG: 1
        ARA_CLIENT_URL: https://stats-X.cdn77.eu
        CDN_COMMON_ENCRYPTION_KEY: def000003f77f1d55ffd61ad37441c4d069db3a8961d39e159b26004e3944296ed67984e67ad15fa6dbfa393f85fc132aac3b39689ee9446a4e144c7d0c03ce7e7245aa1
        CEPH_CREDENTIALS_ENCRYPTION_KEY: def000003f77f1d55ffd61ad37441c4d069db3a8961d39e159b26004e3944296ed67984e67ad15fa6dbfa393f85fc132aac3b39689ee9446a4e144c7d0c03ce7e7245aa1
        CEPH_CREDENTIALS_SODIUM_ENCRYPTION_KEY: a28c0818e4b63a65a0ca1a67f5251cc7eef81826c79332978736d962f2137a48
        CDN_RESTRICTION_ACCOUNT_ID_FROM: 1000000
        CDN_NOTIFICATION_ACCOUNT_ID_FROM: 1000000
        CLIENT_BASE_URL: https://client.cdn77.com
        CLICKHOUSE_ENDPOINT: http://clickhouse:8123
        CLICKHOUSE_DATABASE: clap
        CLICKHOUSE_USERNAME: default
        CLICKHOUSE_PASSWORD: p4ssw0rd
        DATABASE_URL: *****************************************************************
        DATABASE_MIGRATION_URL: *****************************************************************
        GEOIP_DB_PATH: ''
        IS_PAYGATE_SANDBOX: 1
        NXG_API_URL: https://nxg-api.example/
        OPEN_API_SPEC_HOST: api.docs.example
        OPEN_API_SPEC_SCHEME: https
        PAYGATE_CLIENT_ID: ********-1111-1111-1111-********1111
        PAYGATE_CLIENT_SECRET: aaaaaaaaaabbbbbbbbbbbbbbbbbbbbbb
        PASSWORD_ENCRYPTION_KEY: abcdefgh12345
        POSTGRES_PASSWORD: postgres
        POSTMARK_SERVER_TOKEN: aaa-serverToken-bbb
        PUBLIC_URL_HASH_SECRET: 12345abcdef
        REDIS_DSN: 127.0.0.1:6379
        SIGN_IN_TOKEN_ISSUED_BY: clap-api
        SIGN_IN_TOKEN_SECRET: abcdefgh1234ABCDEFGH5678!=aBcDeF
        SLACK_API_TOKEN: token
        SLACK_API_URL: url
        STATS_AVOID_CLICK_HOUSE: 0
        TRUSTED_PROXIES: 127.0.0.1
        RATE_LIMIT: 5000
        RATE_LIMIT_INTERVAL: "1 minute"
        RATE_LIMIT_DISABLED_TOKEN_IDS_FILE: ''
        RATE_LIMIT_DISABLED_CUSTOMER_IDS_FILE: ''
        CLICKHOUSE_CEPH_DATABASE: default
        CLICKHOUSE_CEPH_ENDPOINT: http://127.0.0.1:8123
        CLICKHOUSE_CEPH_PASSWORD: ''
        CLICKHOUSE_CEPH_USERNAME: default
        CLICKHOUSE_REAL_TIME_LOG_DATABASE: default
        CLICKHOUSE_REAL_TIME_LOG_ENDPOINT: http://127.0.0.1:8123
        CLICKHOUSE_REAL_TIME_LOG_PASSWORD: ''
        CLICKHOUSE_REAL_TIME_LOG_USERNAME: user
        MC_API_HOST: mc-example.cdn77.eu
        MC_API_USERNAME: user
        MC_API_PASSWORD: password

Validate Entity Mapping:
    extends: .localComposerJob
    script: make validate-entity-mapping
    variables:
        ENV: ci
        SYMFONY_ENV: test
        DATABASE_URL: *****************************************************************
        DATABASE_MIGRATION_URL: *****************************************************************
        TRUSTED_PROXIES: 127.0.0.1
        CDN_RESTRICTION_ACCOUNT_ID_FROM: 1000000

Compile DI Container:
    extends: .localComposerJob
    variables:
        ENV: ci
        SYMFONY_ENV: test
        SYMFONY_DEBUG: 1
        DATABASE_URL: *****************************************************************
        DATABASE_MIGRATION_URL: *****************************************************************
        TRUSTED_PROXIES: 127.0.0.1
        CDN_RESTRICTION_ACCOUNT_ID_FROM: 1000000
    script:
        - bin/console cache:warmup -n --env=dev
        - bin/console cache:warmup -n --env=prod
        - bin/console cache:warmup -n --env=test
        - bin/console cache:warmup -n --env=dev --no-debug
        - bin/console cache:warmup -n --env=prod --no-debug
        - bin/console cache:warmup -n --env=test --no-debug

Build & Release:
    extends: .localComposerJob
    stage: Release
    before_script:
        - !reference [ .localComposerJob, before_script ]
        - rm -rf $releaseDir
        - mkdir $releaseDir
    script:
        - eval "$publicReleaseExpr"
        - export PUBLIC_RELEASE=$publicRelease
        - mv -t "$releaseDir"
            .ci
            bin
            bootstrap.php
            graphql
            migrations
            public
            src
            vendor
            composer.json
            sailor.php
    variables:
        COMPOSER_ARGS: --no-dev --classmap-authoritative
        releaseDir: release-$CI_JOB_ID
    artifacts:
        paths:
            - $releaseDir

Deploy Staging:
    extends: .deploy-swap-links
    resource_group: Staging
    environment:
        name: Staging
    variables:
        DEPLOY_HOSTS: shcdn-clap-stage-1.superhosting.cz
        DEPLOY_USERS: cst-stage.clap.cdn77.dev
        DEPLOY_PATHS: /home/<USER>/stage.clap.cdn77.dev/subdomains/www
        DEPLOY_SHARED_PATHS: .env logs:var/log run:var/run
        DEPLOY_HTTP_CHECK_APP_URLS: https://stage.clap.cdn77.dev/v3/ping
    rules:
        -  when: manual
    allow_failure: true

Deploy Canary:
    extends: .deploy-swap-links
    variables:
        DEPLOY_HOSTS: shcdn-clap-canary-1.superhosting.cz
        DEPLOY_USERS: cst-api.cdn77.com
        DEPLOY_PATHS: /home/<USER>/api.cdn77.com/subdomains/www
        DEPLOY_SHARED_PATHS: .env logs:var/log run:var/run
        DEPLOY_HTTP_CHECK_APP_URLS: https://api.cdn77.com/v3/ping
    rules:
        -   when: manual
    allow_failure: true

Deploy Production:
    extends: .deploy-swap-links
    variables:
        DEPLOY_HOSTS: shcdn-clap-1.superhosting.cz shcdn-clap-2.superhosting.cz
        DEPLOY_USERS: cst-api.cdn77.com
        DEPLOY_PATHS: /home/<USER>/api.cdn77.com/subdomains/www
        DEPLOY_SHARED_PATHS: .env logs:var/log run:var/run
        DEPLOY_HTTP_CHECK_APP_URLS: https://api.cdn77.com/v3/ping
