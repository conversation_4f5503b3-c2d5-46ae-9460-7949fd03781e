interface Connection {
  edges: [Edge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

interface Edge {
  cursor: String!
  node: Node!
}

interface Node {
  id: ID!
}

union Stats = CdnStats | ContinentStats | CustomerStats | TimeSeries

type AddConfigurationPayload {
  configuration: Configuration!
}

type BillingOverview {
  currentCredit: Money
  pricingType: PricingPlan!
  creditValidTo: DateTime
  xeroContactId: String
}

type Cdn {
  id: Int!
  cdnUrl: String!
  cdnSettings: CdnSettings!
  createdAt: DateTime!
  customer: Customer!
  label: String!
  origin: Origin
  isFirstResource: Boolean!
  removedAt: DateTime
  totalTraffic: Float!
}

type CdnEdge {
  cursor: String!
  node: Cdn!
}

type CdnSettings {
  cdnId: Int!
  cacheContentLengthLimit: BigInt
  cacheLockAge: Int
  cacheLockTimeout: Int
  streamingPlaylistBypassEnabled: Boolean!
}

type CdnStats {
  resources: [Cdn!]!
  resourcesData: [TimeSeries]!
  total: BigInt!
}

type CdnsConnection {
  edges: [CdnEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type ChartData {
  series: [ChartSeries!]!
  peak: Float
  peakTime: DateTime
  percentile: Float
}

type ChartSeries {
  data: [Array!]!
  name: String!
  total: Float!
}

type Configuration {
  id: ID!
  type: AccessType!
  restrictions: Restriction!
}

type ContinentStats {
  continents: [String!]!
  continentsData: [DataCenterStats!]!
}

type Country {
  id: ID!
  name: String!
  shortName: String!
  iso: String!
  vat: Float!
}

type Customer {
  id: ID!
  countryIso: String
  country: Country
  createdAt: DateTime!
  email: String!
  fullName: String!
  totalCdnCount: Int!
  totalPaid: Money!
  phone: String
  isTestAccount: Boolean!
  isSponsoredAccount: Boolean!
  currentMonthTrafficBytes: BigInt!
  slackChannel: String
  suspendedAt: DateTime
  parent: Customer
  oldId: Int!
  lastLogin: LoginLog
  hasTicket: Boolean!
  contactEmails: [CustomerEmail!]
  flags: CustomerFlags
  settings: CustomerSettings!
  paymentSettings: CustomerPaymentSettings
}

type CustomerCdnSettings {
  id: ID!
  activeCdnsCountQuota: Int!
  activeTrialCdnsCountQuota: Int!
  customerId: Int!
  dailyDeletedCdnsQuota: Int!
  dataCentersEditEnabled: Boolean!
  maxCnames: Int!
  maxGeoProtectionCountries: Int!
  maxHotlinkProtectionDomains: Int!
  maxIgnoredQueryParameters: Int!
  maxIpProtectionAddresses: Int!
  minDataCenters: Int!
  purgeAllDisabled: Boolean!
  prefetchMode: PrefetchMode!
}

type CustomerConnection {
  edges: [CustomerEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type CustomerEdge {
  cursor: String!
  node: Customer!
}

type CustomerEmail {
  id: ID!
  email: String!
  type: MailType!
  greetingName: String
}

type CustomerFlags {
  rating: CustomerRating!
}

type CustomerMailPayload {
  customerMail: CustomerEmail!
}

type CustomerPaymentSettings {
  creditCardCurrency: Currency
  customPlanAutoRenewalEnabled: Boolean!
  monthlyPlanAutoRechargeEnabled: Boolean!
  monthlyTrafficPlanGroup: MonthlyPlanGroup
  hasAutoRechargeEnabled: Boolean!
  autoRechargeAmount: Float
}

type CustomerSettings {
  hasFreeRawLogs: Boolean!
  streamingPortalAllowed: Boolean!
  preferredLocationGroup: Int
  prefetchRequestsLimit: Int!
  purgeRequestsLimit: Int!
  hasObjectStorageEnabled: Boolean!
  tsunamiEnabled: Boolean!
}

type CustomerSettingsPayload {
  customerSettings: CustomerSettings!
}

type CustomerStats {
  customers: [Customer!]!
  customersData: [TimeSeries]!
  total: BigInt!
}

type DataCenter {
  id: ID!
  legacyId: String!
  city: String!
  continentCode: String!
  countryIso: String!
  countryName: String!
}

type DataCenterStats {
  dataCenters: [String!]!
  dataCentersData: [TimeSeries!]!
}

type EditCdnPayload {
  cdn: Cdn!
}

type EditCdnSettingsPayload {
  cdnSettings: CdnSettings!
}

type EditConfigurationPayload {
  configuration: Configuration!
}

type EditCustomerCdnSettingsPayload {
  customerCdnSettings: CustomerCdnSettings!
}

type EditCustomerPayload {
  customer: Customer!
}

type EditCustomerPaymentSettingsPayload {
  customerPaymentSettings: CustomerPaymentSettings!
}

type EditStreamCdnPayload {
  streamCdn: StreamCdn!
}

type Income {
  new: Money!
  recharge: Money!
  total: Money!
}

type KayakoTicket {
  id: String!
  url: String!
  subject: String!
  status: KayakoStatus!
  department: KayakoDepartment!
  priority: Int!
  owner: ID!
  lastUpdatedAt: DateTime
}

type LogCdn {
  id: ID!
  label: String!
}

type LogPath {
  root: String!
  directory: String!
}

type LoggingField {
  name: String!
  outputName: String
}

type LoginLog {
  id: ID!
  ipAddress: String!
  loggedAt: DateTimeTz!
}

type MailLog {
  id: ID!
  body: String
  deliveryStatus: String
  recipient: String!
  sentAt: DateTime!
  sender: String
  subject: String
  templateAlias: String
}

type MailLogConnection {
  edges: [MailLogEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type MailLogEdge {
  cursor: String!
  node: MailLog!
}

type Money {
  amount: Float!
  currency: String!
}

type Mutation {
  addConfiguration(input: AddConfigurationInput!): AddConfigurationPayload!
  addCustomerMail(input: AddCustomerMailInput!): CustomerMailPayload!
  addNote(input: AddNoteInput!): NotePayload!
  deleteConfiguration(configurationId: ID!): ID!
  editCdn(input: EditCdnInput!, id: ID!): EditCdnPayload!
  editCdnSettings(cdnId: ID!, input: EditCdnSettingsInput!): EditCdnSettingsPayload!
  editConfiguration(configurationId: ID!, input: EditConfigurationInput!): EditConfigurationPayload!
  editCredit(customerId: ID!, input: EditCreditInput!): Boolean!
  editCustomer(customerId: ID!, input: EditCustomerInput!): EditCustomerPayload!
  editCustomerCdnSettings(customerId: ID!, input: EditCustomerCdnSettingsInput!): EditCustomerCdnSettingsPayload!
  editCustomerPaymentSettings(customerId: ID!, input: EditCustomerPaymentSettingsInput!): EditCustomerPaymentSettingsPayload!
  editCustomerSettings(customerId: ID!, input: EditCustomerSettingsInput!): CustomerSettingsPayload!
  editNote(id: ID!, input: EditNoteInput!): NotePayload!
  editStreamCdn(input: EditStreamCdnInput!, cdnId: ID!): EditStreamCdnPayload!
  recountCredit(customerId: ID!): Boolean!
  removeCustomerMail(customerMailId: ID!): ID!
  removeNote(noteId: ID!): ID!
  scheduleCustomerSuspension(customerId: ID!, input: ScheduleCustomerSuspensionInput!): Boolean!
  sendSignUpInvite(input: SendSignUpInviteInput!): ID!
}

type Note {
  createdAt: DateTime!
  customer: Customer!
  editor: Customer
  id: ID!
  text: String!
  isPinned: Boolean!
}

type NotePayload {
  note: Note!
}

type Origin {
  customer: Customer!
  host: String!
  id: ID!
  label: String!
  type: String!
  createdAt: DateTime!
  removedAt: DateTime
  scheme: OriginScheme!
}

type OriginConnection {
  edges: [OriginEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type OriginEdge {
  cursor: String!
  node: Origin!
}

type PageInfo {
  endCursor: String!
}

type Payment {
  amount: Float!
  createdAt: DateTime!
  currency: String!
  customer: Customer!
  description: String!
  id: ID!
  source: RequestSource!
  method: String!
  type: String!
  usdAmount: Float!
}

type Plan {
  id: ID!
}

type PlanConnection {
  edges: [PlanEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type PlanEdge {
  cursor: String!
  node: Plan!
}

type Query {
  billingOverview(id: ID!): BillingOverview
  cdn(id: ID!): Cdn!
  cdnSettings(cdnId: Int!): CdnSettings
  cdns(limit: Int, offset: Int, filter: CdnsFilterInput, orderBy: CdnOrdering): CdnsConnection!
  customer(id: ID, oldId: ID): Customer!
  customerCdnSettings(customerId: Int!): CustomerCdnSettings
  customers(filter: CustomersFilterInput!, orderBy: CustomerOrdering, limit: Int, offset: Int): CustomerConnection!
  dailyIncome: Income!
  dataCenters: [DataCenter!]!
  kayakoTickets(customerId: ID!): [KayakoTicket!]
  mailLogs(limit: Int, offset: Int, filter: MailLogsFilterInput!): MailLogConnection!
  notes(customerId: ID!): [Note!]
  origins(limit: Int, offset: Int, filter: OriginFilterInput!, orderBy: OriginOrdering): OriginConnection!
  overviewChartStats(filter: OverviewStatsFilterInput!): ChartData!
  overviewStats(filter: OverviewStatsFilterInput!, groupBy: StatsGrouping!, sortBy: StatsSortBy, limit: Int): Stats!
  payments(limit: Int, offset: Int, filter: PaymentsFilterInput!): [Payment]!
  plans(limit: Int, offset: Int, filter: PlansFilterInput!): PlanConnection!
  realTimeLog(id: ID!): RealTimeLog
  realTimeLogs(customerId: ID!): [RealTimeLog!]!
  revenue(from: DateTime!, to: DateTime!): [Revenue!]!
  streamCdn(cdnId: ID!): StreamCdn
  streamOrigins: [StreamOrigin!]!
  teamMemberAccessConfiguration(id: ID!): Configuration!
  teamMemberAccessConfigurations(teamMemberId: ID!): [Configuration!]!
}

type RealTimeLog {
  id: ID!
  scope: LogScope!
  outputFormat: OutputFormat!
  activeUntil: DateTime
  isActive: Boolean!
  originId: ID
  bucketName: String
  ttlConfig: TtlConfig
  cdns: [LogCdn!]
  logPath: LogPath
  loggingFields: [LoggingField!]
}

type Restriction {
  restrictionType: RestrictionType!
  ids: [ID!]!
}

type Revenue {
  month: String!
  lost: Money!
  new: Money!
  netChange: Money!
  upsell: Money!
}

type StreamCdn {
  id: ID!
  key: String!
  originId: ID!
  password: String
  path: String
  port: Int!
  protocol: StreamProtocol!
  customer: Customer!
}

type StreamOrigin {
  id: ID!
  legacyId: ID!
  origin: String!
}

type TimeSeries {
  timestamps: [Int!]!
  fields: [Array!]!
}

type TtlConfig {
  days: Int
}

enum AccessType {
  AccountEdit
  AccountRead
  BillingRead
  CdnCreate
  CdnDelete
  CdnEdit
  CdnRead
  JobPrefetch
  JobPurge
  JobRead
  LogsRead
  LogsToggle
  ObjectStorageCreate
  ObjectStorageDelete
  ObjectStorageEdit
  ObjectStorageRead
  OriginCreate
  OriginDelete
  OriginEdit
  OriginRead
  SslCreate
  SslDelete
  SslEdit
  SslRead
  StatsRead
  TikTokChangeLogsRead
}

enum CdnSort {
  CreatedAt
  Id
  Label
}

enum Currency {
  BRL
  CZK
  EUR
  GBP
  USD
}

enum CustomerRating {
  Default
  Top
  Vip
}

enum CustomerSort {
  CountryIso
  CreatedAt
  Email
  FullName
  Id
  ParentId
}

enum DataSource {
  Ara
  ClickHouse
  ClickHouseLiveStreaming
}

enum Direction {
  Asc
  Desc
}

enum KayakoDepartment {
  Abuse
  Admins
  Bugs
  DevInternal
  Sales
  Tech
}

enum KayakoStatus {
  Abuse
  Closed
  Open
  Tomorrow
  TsHw
  Waiting
}

enum LogScope {
  Main
  SelectedCdns
}

enum MailType {
  Abuse
  CustomPlan
  History
  Invoice
  Secondary
}

enum MonthlyPlanGroup {
  Alfa
  Beta
  Delta
  Gamma
}

enum OriginScheme {
  Http
  Https
}

enum OriginSort {
  CreatedAt
  Label
}

enum OutputFormat {
  CSV
  JSON
  TSV
}

enum OverviewType {
  LiveStreaming
  Public
  Total
  Trial
}

enum PlanType {
  CustomPlan
  MonthlyPlan
}

enum PrefetchMode {
  Disabled
  Enabled
  Hidden
}

enum PricingPlan {
  CustomPlan
  MonthlyPlan
  None
  PayAsYouGo
}

enum RequestSource {
  Clap
  Crop
  Kox
  Publicweb
}

enum RestrictionType {
  AccessKey
  Cdn
  Datacenter
  Job
  None
  Origin
  Ssl
  Storage
}

enum StatsGrouping {
  Cdn
  Customer
  None
  Server
}

enum StatsSortBy {
  Files
  Size
  SizeCached
  SizeNonCached
}

enum StatsType {
  Bandwidth
  CacheStats
  Costs
  Headers
  HitMiss
  Traffic
}

enum StreamProtocol {
  Http
  Https
  Rtmp
  Rtsp
}

scalar Array

"An arbitrarily long sequence of digits that represents a big integer."
scalar BigInt

"A datetime string with format `Y-m-d H:i:s`, e.g. `2018-05-23 13:43:32`."
scalar DateTime

"A datetime string with format `Y-m-d\\TH:i:s.uP`, e.g. `2020-04-20T16:20:04.000000+04:00`."
scalar DateTimeTz

input AddConfigurationInput {
  restrictions: [ID!]!
  accessType: AccessType!
  customerId: ID!
  restrictionType: RestrictionType!
}

input AddCustomerMailInput {
  email: String!
  type: MailType!
  greetingName: String
  customerId: ID!
}

input AddNoteInput {
  customerId: ID!
  editorId: ID
  text: String!
  isPinned: Boolean!
}

input CacheInput {
  cacheContentLength: BigInt
  cacheLockAge: Int
  cacheLockTimeout: Int
}

input CdnOrdering {
  direction: Direction!
  sort: CdnSort!
}

input CdnsFilterInput {
  query: String
  id: [ID!]
}

input CustomerOrdering {
  direction: Direction!
  sort: CustomerSort!
}

input CustomersFilterInput {
  createdFrom: DateTimeTz
  createdTo: DateTimeTz
  query: String
  hasParent: Boolean
  excludeInternal: Boolean
  suspended: Boolean
  trial: Boolean
  rating: CustomerRating
}

input EditCdnInput {
  cache: CacheInput!
  streamingPlaylistBypassEnabled: Boolean!
}

input EditCdnSettingsInput {
  streamingPlaylistBypassEnabled: Boolean!
}

input EditConfigurationInput {
  restrictions: [ID!]!
  restrictionType: RestrictionType!
}

input EditCreditInput {
  creditAmount: Float
  expiresAt: DateTimeTz
  depositor: ID
}

input EditCustomerCdnSettingsInput {
  activeCdnsCountQuota: Int!
  activeTrialCdnsCountQuota: Int!
  dailyDeletedCdnsQuota: Int!
  dataCentersEditEnabled: Boolean!
  maxCnames: Int!
  maxGeoProtectionCountries: Int!
  maxHotlinkProtectionDomains: Int!
  maxIgnoredQueryParameters: Int!
  maxIpProtectionAddresses: Int!
  minDataCenters: Int!
  purgeAllDisabled: Boolean!
  prefetchMode: PrefetchMode!
}

input EditCustomerInput {
  trialEndDate: DateTime
  email: String!
  phone: String
  emailConfirmedAt: DateTime
  slackChannel: String
}

input EditCustomerPaymentSettingsInput {
  autoRechargeAmount: Float
  autoRechargeEnabled: Boolean!
  creditCardCurrency: Currency
  customPlanAutoRenewalEnabled: Boolean!
  monthlyPlanAutoRechargeEnabled: Boolean!
  monthlyTrafficPlanGroup: MonthlyPlanGroup
}

input EditCustomerSettingsInput {
  preferredLocationGroup: Int
  prefetchRequestsLimit: Int!
  purgeRequestsLimit: Int!
  hasObjectStorageEnabled: Boolean!
  hasStreamingPortalEnabled: Boolean!
  hasTsunamiEnabled: Boolean!
}

input EditNoteInput {
  text: String!
  isPinned: Boolean!
}

input EditStreamCdnInput {
  key: String!
  password: String
  path: String
  port: Int!
  protocol: StreamProtocol!
  streamOriginId: ID!
}

input MailLogsFilterInput {
  customerId: Int!
}

input OriginFilterInput {
  query: String
  customerId: ID
}

input OriginOrdering {
  direction: Direction!
  sort: OriginSort!
}

input OverviewStatsFilterInput {
  customers: [ID!]
  dataSource: DataSource
  from: DateTimeTz!
  to: DateTimeTz!
  overviewType: OverviewType!
  overviewSubType: StatsType!
}

input PaymentsFilterInput {
  includeFirst: Boolean
  includeRecharge: Boolean
}

input PlansFilterInput {
  activeFrom: DateTime
  activeTo: DateTime
  type: PlanType
}

input ScheduleCustomerSuspensionInput {
  suspendedById: String!
  suspensionDate: DateTimeTz!
  reason: String!
}

input SendSignUpInviteInput {
  email: String!
  fullName: String!
}
