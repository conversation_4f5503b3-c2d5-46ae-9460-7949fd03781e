<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220722133000 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
            ALTER TABLE account_settings
            ADD COLUMN cdn_datacenters_edit_enabled boolean NOT NULL DEFAULT true
        PSQL,
        );

        $this->addSql(
            <<<'PSQL'
            ALTER TABLE cdn_http
            ALTER COLUMN can_edit_locations SET NOT NULL
        PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException();
    }
}
