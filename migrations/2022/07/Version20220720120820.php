<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20220720120820 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
            ALTER TABLE account_payment_settings
            ALTER COLUMN monthly_plan_group DROP DEFAULT,
            ALTER COLUMN monthly_plan_group DROP NOT NULL
            PSQL,
        );

        $this->addSql(
            <<<'PSQL'
            ALTER TABLE monthly_plan_group
            ADD COLUMN valid_from timestamp
            PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
