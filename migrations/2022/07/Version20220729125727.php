<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20220729125727 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(<<<'PSQL'
            CREATE INDEX idx_account_sign_up_invites_email_address ON account_sign_up_invites(email_address)
        PSQL);
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
