<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20221212170717 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
            ALTER TABLE pricing.custom_plans
            RENAME TO contracts
            PSQL,
        );
        $this->addSql(
            <<<'PSQL'
            ALTER TABLE pricing.contracts
            RENAME COLUMN custom_plan_billing_period_id TO custom_plan_id
            PSQL,
        );
        $this->addSql(
            <<<'PSQL'
            ALTER TABLE pricing.custom_plan_billing_periods
            RENAME TO custom_plans
            PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
