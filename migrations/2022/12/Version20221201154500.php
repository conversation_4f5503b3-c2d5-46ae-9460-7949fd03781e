<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20221201154500 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
CREATE TABLE storages.rgw_clusters (
    id uuid NOT NULL default uuid_generate_v4(),
    label character varying(255) NOT NULL,
    host character varying(255) NOT NULL,
    scheme character varying(5) NOT NULL,
    port integer DEFAULT NULL,
    published boolean NOT NULL DEFAULT false
)
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException();
    }
}
