<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20220807134304 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
            ALTER TABLE account_mails
            ALTER COLUMN account_id SET NOT NULL,
            ALTER COLUMN email SET NOT NULL,
            ADD FOREIGN KEY (account_id) REFERENCES accounts (id) ON DELETE CASCADE
            PSQL,
        );

        $this->addSql(
            <<<'PSQL'
            CREATE INDEX idx_account_mails_account_id ON account_mails (account_id)
            PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
