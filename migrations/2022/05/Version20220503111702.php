<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20220503111702 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
            ALTER TABLE pricing.custom_plan_billing_periods
            ALTER COLUMN plan_id SET NOT NULL,
            ALTER COLUMN billing_unit SET NOT NULL
            PSQL,
        );

        $this->addSql(
            <<<'PSQL'
            ALTER TABLE pricing.custom_plans
            DROP COLUMN customer_id,
            DROP COLUMN customer_legacy_id,
            DROP COLUMN billing_unit,
            ALTER COLUMN currency SET NOT NULL
            PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
