<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20220519060943 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
            ALTER TABLE tik_tok_datacenter_traffic
            RENAME TO datacenter_traffic
            PSQL,
        );

        $this->addSql(
            <<<'PSQL'
            ALTER TABLE tik_tok_region_locations
            RENAME TO region_locations
            PSQL,
        );

        $this->addSql(
            <<<'PSQL'
            ALTER TABLE region_locations
            SET SCHEMA pricing
            PSQL,
        );

        $this->addSql(
            <<<'PSQL'
            ALTER TABLE tik_tok_regions
            RENAME TO regions
            PSQL,
        );

        $this->addSql(
            <<<'PSQL'
            ALTER TABLE regions
            SET SCHEMA pricing
            PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
