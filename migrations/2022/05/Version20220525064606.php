<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20220525064606 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
            ALTER TABLE datacenters.locations
            ALTER COLUMN city_code SET NOT NULL
            PSQL,
        );

        $this->addSql(
            <<<'PSQL'
            ALTER TABLE datacenters.locations
            RENAME COLUMN city_code TO iata_code
            PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
