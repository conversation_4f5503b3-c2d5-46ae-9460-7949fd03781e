<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20221028151626 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
            ALTER TABLE cdn_http_protection
            ALTER COLUMN hlp_deny_empty_referer SET NOT NULL
            PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
