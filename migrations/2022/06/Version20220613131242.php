<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20220613131242 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
            ALTER TABLE countries
            ALTER COLUMN eu_member DROP default,
            ALTER COLUMN eu_member TYPE bool USING eu_member::bool,
            ALTER COLUMN eu_member SET NOT NULL,
            ALTER COLUMN name SET NOT NULL
            PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
