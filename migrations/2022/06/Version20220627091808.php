<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20220627091808 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('DROP TABLE constants');
        $this->addSql('DROP TABLE netbanx_purchases');
        $this->addSql('DROP TABLE paypal');
        $this->addSql('DROP TABLE paypal_payments');
        $this->addSql('DROP TABLE permanent_logins');
        $this->addSql('DROP TABLE predefined_contact_mails');
        $this->addSql('DROP TABLE referers');
        $this->addSql('DROP TABLE xero_checksum');
        $this->addSql('DROP TABLE xero_report');
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
