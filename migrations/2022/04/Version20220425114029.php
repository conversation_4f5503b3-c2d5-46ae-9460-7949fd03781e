<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20220425114029 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('DROP VIEW queue_status');

        $this->addSql('DROP TABLE data_manipulation.legacy_request_mapping');
        $this->addSql('DROP TABLE queue_calls');
        $this->addSql('DROP TABLE queued_paths');
        $this->addSql('DROP TABLE queued_paths_requests');
        $this->addSql('DROP TABLE queued_requests');
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
