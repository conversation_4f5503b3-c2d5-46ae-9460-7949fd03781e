SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
-- This line must be commented to make migration work
-- SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;
CREATE SCHEMA api;
CREATE SCHEMA data_manipulation;
CREATE SCHEMA datacenters;
CREATE SCHEMA pricing;
CREATE SCHEMA storages;
CREATE EXTENSION IF NOT EXISTS plpgsql WITH SCHEMA pg_catalog;
COMMENT ON EXTENSION plpgsql IS 'PL/pgSQL procedural language';
CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;
COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';
CREATE TYPE data_manipulation.state AS ENUM (
    'queued',
    'processing',
    'done'
);
CREATE TYPE data_manipulation.type AS ENUM (
    'purge',
    'prefetch',
    'purge-all'
);
CREATE TYPE public.table_count AS (
	table_name text,
	num_rows integer
);
CREATE TYPE storages.drive_type AS ENUM (
    'SSD',
    'HDD'
);
SET default_tablespace = '';
SET default_with_oids = false;
CREATE TABLE api.access_list (
    id integer NOT NULL,
    account_id integer NOT NULL,
    category_id integer,
    type smallint,
    method_id integer,
    item_id character varying(50),
    ts timestamp(0) with time zone DEFAULT now() NOT NULL
);
CREATE SEQUENCE api.access_list_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE api.access_list_id_seq OWNED BY api.access_list.id;
CREATE TABLE api.application_token (
    id uuid NOT NULL,
    created_at timestamp without time zone NOT NULL,
    token_hash character varying(255) NOT NULL,
    application_name character varying(10) NOT NULL
);
CREATE TABLE api.categories (
    id integer NOT NULL,
    name character varying(512) NOT NULL,
    content text NOT NULL,
    weight smallint NOT NULL,
    menu_name character varying(512) NOT NULL,
    url character varying(512),
    published bit(1) DEFAULT (0)::bit(1) NOT NULL,
    published_devel bit(1) DEFAULT (0)::bit(1) NOT NULL,
    callable_from_client bit(1) DEFAULT (0)::bit(1) NOT NULL
);
CREATE SEQUENCE api.categories_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE api.categories_id_seq OWNED BY api.categories.id;
CREATE TABLE api.credentials_personal_token (
    id integer NOT NULL,
    token character(64) NOT NULL,
    account_id integer NOT NULL,
    token_hash character varying(255),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    expires_at timestamp without time zone,
    label character varying(255) DEFAULT NULL::character varying,
    uuid uuid
);
CREATE SEQUENCE api.credentials_personal_token_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE api.credentials_personal_token_id_seq OWNED BY api.credentials_personal_token.id;
CREATE TABLE api.examples (
    id integer NOT NULL,
    name character varying(512) NOT NULL,
    type character varying(512) NOT NULL,
    weight smallint NOT NULL,
    code_type integer NOT NULL,
    method_id integer NOT NULL,
    content text NOT NULL
);
CREATE SEQUENCE api.examples_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE api.examples_id_seq OWNED BY api.examples.id;
CREATE TABLE api.method_has_parameter (
    method_id integer NOT NULL,
    parameter_id integer NOT NULL,
    weight smallint NOT NULL,
    id integer NOT NULL,
    required bit(1),
    available_only_on_client bit(1) NOT NULL,
    visible smallint DEFAULT (1)::smallint NOT NULL,
    main_id boolean DEFAULT false NOT NULL
);
CREATE SEQUENCE api.method_has_parameter_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE api.method_has_parameter_id_seq OWNED BY api.method_has_parameter.id;
CREATE TABLE api.methods (
    id integer NOT NULL,
    category_id integer NOT NULL,
    name character varying(256) NOT NULL,
    url character varying(512) NOT NULL,
    weight smallint NOT NULL,
    type smallint NOT NULL,
    description text,
    published bit(1) DEFAULT (0)::bit(1) NOT NULL,
    published_devel bit(1) DEFAULT (0)::bit(1) NOT NULL,
    callable_from_client bit(1) DEFAULT (0)::bit(1) NOT NULL
);
COMMENT ON COLUMN api.methods.callable_from_client IS 'callable from client any time (independent on published or published_devel)';
CREATE TABLE api.methods_has_return_parameters (
    methods_id integer,
    weight integer NOT NULL,
    parameters_id integer,
    id integer NOT NULL
);
CREATE SEQUENCE api.methods_has_return_parameters_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE api.methods_has_return_parameters_id_seq OWNED BY api.methods_has_return_parameters.id;
CREATE SEQUENCE api.methods_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE api.methods_id_seq OWNED BY api.methods.id;
CREATE TABLE api.parameters (
    id integer NOT NULL,
    name character varying(512) NOT NULL,
    parameter_type_id integer NOT NULL,
    default_value character varying(512) DEFAULT ''::character varying NOT NULL,
    validation_text text DEFAULT ''::text NOT NULL,
    description text DEFAULT ''::text NOT NULL,
    internal_name character(512) NOT NULL,
    replace_search character varying(512),
    replace_replacement character varying(512),
    example text,
    min_length smallint,
    max_length smallint
);
CREATE SEQUENCE api.parameters_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE api.parameters_id_seq OWNED BY api.parameters.id;
CREATE TABLE api.parameters_types (
    name character varying(512) NOT NULL,
    id integer NOT NULL
);
CREATE SEQUENCE api.parameters_types_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE api.parameters_types_id_seq OWNED BY api.parameters_types.id;
CREATE TABLE api.parameters_valid_values (
    id integer NOT NULL,
    parameter_id integer NOT NULL,
    value character varying(512) NOT NULL,
    published boolean DEFAULT true NOT NULL
);
CREATE SEQUENCE api.parameters_valid_values_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE api.parameters_valid_values_id_seq OWNED BY api.parameters_valid_values.id;
CREATE TABLE api.request (
    started_at timestamp without time zone NOT NULL,
    ended_at timestamp without time zone NOT NULL,
    account_id integer,
    endpoint character varying(255),
    query_string text,
    request_body text,
    response_code integer,
    source character varying(50),
    method character varying(10),
    id uuid NOT NULL,
    ip inet,
    response_body text
);
CREATE TABLE api.team_member_access_configuration (
    id uuid NOT NULL,
    customer_id uuid NOT NULL,
    access_type character varying(16) NOT NULL,
    restrictions jsonb DEFAULT '{}'::jsonb NOT NULL,
    created_at timestamp without time zone
);
CREATE TABLE api.version_has_category (
    id integer NOT NULL,
    category_id integer NOT NULL,
    version_id integer NOT NULL
);
CREATE SEQUENCE api.version_has_category_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE api.version_has_category_id_seq OWNED BY api.version_has_category.id;
CREATE TABLE api.versions (
    id integer NOT NULL,
    name character varying(256) NOT NULL,
    published bit(1) NOT NULL,
    published_devel bit(1) NOT NULL,
    weight smallint NOT NULL,
    content text,
    url_prefix character varying(256)
);
CREATE SEQUENCE api.versions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE api.versions_id_seq OWNED BY api.versions.id;
CREATE TABLE data_manipulation.job (
    id uuid NOT NULL,
    cdn_id integer,
    type data_manipulation.type NOT NULL,
    paths jsonb NOT NULL,
    state data_manipulation.state NOT NULL,
    scheduled_at timestamp with time zone NOT NULL,
    completed_at timestamp with time zone,
    resource_id integer NOT NULL,
    percent_done smallint DEFAULT 0 NOT NULL,
    upstream_host character varying(255)
);
CREATE TABLE data_manipulation.legacy_request_mapping (
    job_id uuid NOT NULL,
    old_request_id integer NOT NULL
);
CREATE SEQUENCE datacenters.aflexi_locations_id_seq
    START WITH 1000001000
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
CREATE TABLE datacenters.aflexi_locations (
    id integer DEFAULT nextval('datacenters.aflexi_locations_id_seq'::regclass) NOT NULL,
    location_id character varying(50),
    name character varying(100),
    price double precision,
    gain double precision,
    ready boolean DEFAULT true,
    active boolean DEFAULT true,
    price_force double precision,
    in_group_4 boolean,
    dyndb_server_id integer,
    purchased timestamp without time zone,
    cost_own_hw smallint,
    note text,
    fake_location_id character varying(50),
    hw_host_id integer,
    old_edge_id integer,
    synchronized_ara_price boolean DEFAULT false NOT NULL,
    ts timestamp(0) without time zone DEFAULT CURRENT_TIMESTAMP
);
CREATE TABLE datacenters.aflexi_locations_edge_ids (
    id integer NOT NULL,
    aflexi_location_id integer,
    ts timestamp(0) without time zone DEFAULT now(),
    edge_id integer
);
CREATE SEQUENCE datacenters.aflexi_locations_edge_ids_id_seq
    START WITH 701
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE datacenters.aflexi_locations_edge_ids_id_seq OWNED BY datacenters.aflexi_locations_edge_ids.id;
CREATE TABLE datacenters.edges (
    id integer NOT NULL,
    location_id character varying,
    created_at timestamp with time zone,
    price_synced_to_ara_at timestamp(0) without time zone
);
CREATE TABLE datacenters.locations (
    id character varying(50) NOT NULL,
    city character varying,
    country character varying(2) NOT NULL,
    latitude double precision,
    longitude double precision,
    continent character varying(2),
    visible_in_reports boolean,
    uuid uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    city_code character(3),
    payg_price_per_gb real,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    country_id integer NOT NULL
);
CREATE SEQUENCE pricing.ara_queue_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
CREATE TABLE pricing.ara_queue (
    id integer DEFAULT nextval('pricing.ara_queue_id_seq'::regclass) NOT NULL,
    account_id integer,
    ts_done timestamp(0) without time zone,
    subject character varying(20),
    resource_id integer,
    ts_created timestamp(0) without time zone DEFAULT now() NOT NULL
);
CREATE TABLE pricing.custom_plan_billing_periods (
    id uuid NOT NULL,
    customer_id uuid NOT NULL,
    customer_legacy_id integer NOT NULL,
    active_from timestamp without time zone NOT NULL,
    active_to timestamp without time zone,
    billing_unit character varying(10),
    auto_generate_enabled_at timestamp without time zone,
    plan_id integer
);
CREATE TABLE pricing.custom_plans (
    id uuid NOT NULL,
    customer_id uuid,
    volume integer NOT NULL,
    price real NOT NULL,
    description json NOT NULL,
    invoice_lines json,
    payment_period interval NOT NULL,
    payment_plan character varying(10) NOT NULL,
    active_from timestamp without time zone NOT NULL,
    active_to timestamp without time zone NOT NULL,
    has_storage_included boolean NOT NULL,
    invoice_created_at timestamp without time zone,
    created_at timestamp without time zone NOT NULL,
    state character varying(50) NOT NULL,
    message text,
    customer_legacy_id integer,
    internal_note text,
    billing_unit character varying(10) DEFAULT 'TB'::character varying,
    currency character varying(3),
    custom_plan_billing_period_id uuid NOT NULL
);
CREATE TABLE pricing.individual_prices (
    id integer NOT NULL,
    account_id integer NOT NULL,
    gain double precision NOT NULL,
    time_from timestamp without time zone DEFAULT now(),
    time_to timestamp without time zone,
    account_id_editor integer,
    ts_created timestamp(0) without time zone DEFAULT now(),
    location_id character varying(50) NOT NULL,
    note character varying(250),
    location_uuid uuid
);
COMMENT ON COLUMN pricing.individual_prices.gain IS 'price for customer in USD/GB';
COMMENT ON COLUMN pricing.individual_prices.time_from IS 'price valid from';
COMMENT ON COLUMN pricing.individual_prices.time_to IS 'price valid to (null = infinity)';
CREATE SEQUENCE pricing.individual_prices_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE pricing.individual_prices_id_seq OWNED BY pricing.individual_prices.id;
CREATE SEQUENCE pricing.location_costs_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
CREATE TABLE pricing.plans (
    id integer NOT NULL,
    account_id integer NOT NULL,
    ts_from timestamp(0) without time zone NOT NULL,
    ts_to timestamp without time zone,
    price real NOT NULL,
    traffic real NOT NULL,
    note text,
    guardian integer,
    type character varying(50) DEFAULT 'fixed_plan'::character varying NOT NULL,
    ts_created timestamp(0) without time zone DEFAULT now(),
    monthly_traffic_plan_id integer
);
COMMENT ON COLUMN pricing.plans.traffic IS 'v TB';
COMMENT ON COLUMN pricing.plans.type IS 'fixed_plan | monthly_plan';
CREATE TABLE pricing.plans_cdns (
    id integer NOT NULL,
    plan_id integer NOT NULL,
    cdn_id integer NOT NULL,
    ts timestamp(0) with time zone DEFAULT now() NOT NULL
);
CREATE SEQUENCE pricing.plans_cdns_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE pricing.plans_cdns_id_seq OWNED BY pricing.plans_cdns.id;
CREATE SEQUENCE pricing.plans_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE pricing.plans_id_seq OWNED BY pricing.plans.id;
CREATE TABLE pricing.plans_locations (
    id integer NOT NULL,
    fk_plans_id integer NOT NULL,
    ts_from timestamp(0) without time zone NOT NULL,
    price double precision NOT NULL,
    fk_locations_id character varying(32) NOT NULL
);
CREATE SEQUENCE pricing.plans_locations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE pricing.plans_locations_id_seq OWNED BY pricing.plans_locations.id;
CREATE TABLE pricing.streaming_plans (
    id uuid NOT NULL,
    account_id integer NOT NULL,
    price real,
    bandwidth real,
    note text,
    active_from timestamp without time zone,
    deleted_at timestamp without time zone,
    traffic real
);
CREATE TABLE public.account_big_traffic_change (
    id integer NOT NULL,
    account_id integer,
    "timestamp" timestamp without time zone DEFAULT now(),
    type character varying(255)
);
CREATE SEQUENCE public.account_big_traffic_change_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.account_big_traffic_change_id_seq OWNED BY public.account_big_traffic_change.id;
CREATE TABLE public.account_category (
    id integer NOT NULL,
    ts timestamp without time zone,
    category character varying(250),
    who character varying(250),
    account_id integer,
    source character varying(15)
);
CREATE SEQUENCE public.account_category_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.account_category_id_seq OWNED BY public.account_category.id;
CREATE SEQUENCE public.account_cdn_settings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
CREATE TABLE public.account_cdn_settings (
    id integer DEFAULT nextval('public.account_cdn_settings_id_seq'::regclass) NOT NULL,
    account_id integer NOT NULL,
    origin_protection_proxy_server_group integer DEFAULT 1 NOT NULL
);
CREATE TABLE public.account_comments (
    id integer NOT NULL,
    message text,
    commented timestamp without time zone DEFAULT now(),
    account_id integer NOT NULL,
    account_id_editor integer
);
CREATE SEQUENCE public.account_comments_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.account_comments_id_seq OWNED BY public.account_comments.id;
CREATE TABLE public.account_flags (
    id integer NOT NULL,
    account_id integer,
    first_traffic timestamp(0) without time zone,
    uses_euna boolean DEFAULT true,
    uses_sa boolean DEFAULT true,
    uses_as boolean DEFAULT true,
    zero_vat boolean DEFAULT false,
    class smallint,
    lead_score character varying(250),
    min_estimated_monthly_traffic integer,
    max_estimated_monthly_traffic integer,
    enabled_control_panel boolean DEFAULT true NOT NULL,
    rating character varying(16) DEFAULT 'default'::character varying
);
CREATE SEQUENCE public.account_flags_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.account_flags_id_seq OWNED BY public.account_flags.id;
CREATE TABLE public.account_ga_cids (
    id integer NOT NULL,
    account_id integer NOT NULL,
    cid character varying(36) NOT NULL,
    ts timestamp with time zone DEFAULT now() NOT NULL
);
CREATE SEQUENCE public.account_ga_cids_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.account_ga_cids_id_seq OWNED BY public.account_ga_cids.id;
CREATE TABLE public.account_mailing_unsubscribes (
    id uuid NOT NULL,
    mail_type character varying(16) NOT NULL,
    unsubscribed_at timestamp without time zone NOT NULL,
    account_id uuid NOT NULL
);
CREATE TABLE public.account_mails (
    id integer NOT NULL,
    account_id integer,
    email character varying(255),
    "timestamp" timestamp without time zone DEFAULT now() NOT NULL,
    type character varying(150) DEFAULT 'history'::character varying NOT NULL
);
CREATE SEQUENCE public.account_mails_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.account_mails_id_seq OWNED BY public.account_mails.id;
CREATE TABLE public.account_monthly_traffic_plans (
    id integer NOT NULL,
    ts timestamp(0) without time zone DEFAULT now() NOT NULL,
    monthly_traffic_plan_id integer NOT NULL,
    account_id integer NOT NULL,
    active_until timestamp without time zone NOT NULL,
    terminated boolean DEFAULT false NOT NULL
);
CREATE SEQUENCE public.account_monthly_traffic_plans_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.account_monthly_traffic_plans_id_seq OWNED BY public.account_monthly_traffic_plans.id;
CREATE TABLE public.account_payment_settings (
    id integer NOT NULL,
    account_id integer NOT NULL,
    monthly_plan_auto_recharge boolean DEFAULT false NOT NULL,
    display_invoice_since date,
    monthly_plan_group integer DEFAULT 3 NOT NULL,
    tailored_offer integer
);
CREATE SEQUENCE public.account_payment_settings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.account_payment_settings_id_seq OWNED BY public.account_payment_settings.id;
CREATE TABLE public.account_settings (
    id integer NOT NULL,
    account_id integer NOT NULL,
    user_cdn_hash integer,
    max_cdn_cnames integer,
    free_trial_cdns integer,
    preferred_location_group smallint,
    max_cdn_iqp integer,
    max_cdn_iqp_length integer,
    free_trial_traffic integer DEFAULT 1000 NOT NULL,
    max_cdn_hlp_domains integer,
    max_storage integer DEFAULT 100 NOT NULL,
    max_cdns integer,
    max_deleted_cdns integer,
    min_payment integer,
    kox_account_parts character varying(200),
    stripe_currency character varying(32),
    api_data_limit_requests integer,
    zero_vat boolean DEFAULT false NOT NULL,
    max_cdn_gp_countries integer,
    max_cdn_ipp_addresses integer,
    always_void_invoice boolean DEFAULT false NOT NULL,
    forever_free_raw_logs boolean DEFAULT false NOT NULL,
    pecko_allowed boolean DEFAULT false NOT NULL,
    enabled_purge_all boolean DEFAULT true NOT NULL,
    min_cdn_datacenters integer DEFAULT 1
);
COMMENT ON COLUMN public.account_settings.user_cdn_hash IS 'used only for mc cdn with shared ssl';
COMMENT ON COLUMN public.account_settings.max_cdn_cnames IS 'only for mc cdns';
COMMENT ON COLUMN public.account_settings.free_trial_traffic IS 'in GB';
CREATE SEQUENCE public.account_settings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.account_settings_id_seq OWNED BY public.account_settings.id;
CREATE TABLE public.account_sign_up_invites (
    id uuid NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    customer_id uuid NOT NULL,
    email_address character varying(255) NOT NULL,
    used_at timestamp without time zone
);
CREATE TABLE public.account_traffic (
    id integer NOT NULL,
    account_id integer NOT NULL,
    last_4_hours numeric,
    interval_4_to_8 numeric,
    interval_24_to_28 numeric,
    last_change timestamp without time zone,
    total numeric DEFAULT (0)::numeric,
    last_change_total timestamp without time zone,
    non_cached_last_4_hours numeric,
    non_cached_4_to_8 numeric,
    non_cached_24_to_28 numeric,
    last_5_min numeric,
    non_cached_last_5_min numeric,
    updated_last_5_min timestamp without time zone,
    last_month numeric,
    current_month numeric
);
CREATE SEQUENCE public.account_traffic_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.account_traffic_id_seq OWNED BY public.account_traffic.id;
CREATE TABLE public.account_xero_contacts (
    id integer NOT NULL,
    account_id integer NOT NULL,
    contact_id character varying(50) NOT NULL,
    name character varying(100) NOT NULL,
    ts_created timestamp(0) with time zone DEFAULT now() NOT NULL
);
CREATE SEQUENCE public.account_xero_contacts_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.account_xero_contacts_id_seq OWNED BY public.account_xero_contacts.id;
CREATE TABLE public.accounts (
    id integer NOT NULL,
    email character varying(200) NOT NULL,
    created timestamp without time zone DEFAULT now(),
    company character varying(250),
    full_name character varying(150),
    time_zone character varying(150),
    first_order_ip inet,
    billingon boolean DEFAULT false,
    country character varying(100),
    expiredtrial timestamp without time zone,
    suspended timestamp without time zone,
    suspended_reason text,
    role character varying(16) DEFAULT 'user'::character varying NOT NULL,
    phone character varying(32),
    renewal_upper_limit numeric,
    passwd character varying(128),
    last_password_change timestamp(0) without time zone,
    subscribed boolean DEFAULT true,
    who_suspended integer,
    skype character varying(200),
    admin_email character varying(200),
    api_password_encrypted character varying(512),
    sponsored_client timestamp without time zone,
    parent_id integer,
    testing boolean DEFAULT false NOT NULL,
    two_step_auth_secret character varying(512),
    new_id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    email_confirmed_at timestamp without time zone,
    public_id uuid DEFAULT public.uuid_generate_v4() NOT NULL
);
COMMENT ON COLUMN public.accounts.expiredtrial IS 'Datum, od kdy zacne bezet trial';
COMMENT ON COLUMN public.accounts.subscribed IS 'If false - we can''t send him emails';
CREATE SEQUENCE public.accounts_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.accounts_id_seq OWNED BY public.accounts.id;
CREATE TABLE public.actionlogs (
    id integer NOT NULL,
    object character varying(100),
    object_id integer,
    action character varying(100),
    notice character varying(300),
    created timestamp(0) without time zone DEFAULT now(),
    solved boolean DEFAULT true
);
CREATE SEQUENCE public.actionlogs_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.actionlogs_id_seq OWNED BY public.actionlogs.id;
CREATE TABLE public.adwords_costs_daily (
    id integer NOT NULL,
    costs numeric NOT NULL,
    report_date date NOT NULL
);
CREATE SEQUENCE public.adwords_costs_daily_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.adwords_costs_daily_id_seq OWNED BY public.adwords_costs_daily.id;
CREATE TABLE public.adwords_costs_monitor_daily (
    id integer NOT NULL,
    ts timestamp without time zone DEFAULT now(),
    costs numeric,
    sms_sent boolean DEFAULT false,
    sms_increase_sent boolean DEFAULT false,
    ppc_stopped boolean DEFAULT false,
    adwords_customer character varying(5) DEFAULT 'cdn77'::character varying NOT NULL
);
CREATE SEQUENCE public.adwords_costs_monitor_daily_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.adwords_costs_monitor_daily_id_seq OWNED BY public.adwords_costs_monitor_daily.id;
CREATE TABLE public.alerts (
    id integer NOT NULL,
    ts timestamp(0) without time zone DEFAULT now() NOT NULL,
    type character varying(10) NOT NULL,
    text text NOT NULL,
    global boolean DEFAULT false NOT NULL,
    valid_until timestamp(0) without time zone,
    condition character varying,
    title character varying(255)
);
COMMENT ON COLUMN public.alerts.global IS 'global = true means that this alert is for multiple users';
CREATE SEQUENCE public.alerts_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.alerts_id_seq OWNED BY public.alerts.id;
CREATE TABLE public.alerts_users (
    id integer NOT NULL,
    ts timestamp(0) without time zone DEFAULT now() NOT NULL,
    hidden boolean DEFAULT false,
    account_id integer NOT NULL,
    alerts_id integer NOT NULL,
    manual_alert boolean DEFAULT false
);
COMMENT ON COLUMN public.alerts_users.hidden IS 'false - user can hide this alert, true - user hided this alert, null - user can''t hide alert';
CREATE SEQUENCE public.alerts_users_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.alerts_users_id_seq OWNED BY public.alerts_users.id;
CREATE TABLE public.application_states (
    id integer NOT NULL,
    name character varying(50) NOT NULL,
    value character varying(100) NOT NULL,
    ts timestamp with time zone DEFAULT now() NOT NULL,
    forced_value character varying(100)
);
CREATE SEQUENCE public.application_states_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.application_states_id_seq OWNED BY public.application_states.id;
CREATE TABLE public.ask_colleague_hash (
    account_id integer NOT NULL,
    hash text NOT NULL,
    validity timestamp without time zone DEFAULT (now() + '24:00:00'::interval) NOT NULL,
    id integer NOT NULL
);
CREATE SEQUENCE public.ask_colleague_hash_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.ask_colleague_hash_id_seq OWNED BY public.ask_colleague_hash.id;
CREATE TABLE public.blocked_ips (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    blocked_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    ip inet NOT NULL,
    note text,
    blocked_until timestamp without time zone
);
CREATE TABLE public.cdn (
    id integer NOT NULL,
    resource_id integer NOT NULL,
    type character varying(25) DEFAULT 'http'::character varying NOT NULL,
    cdn_url character varying(250) NOT NULL,
    origin_url character varying(320),
    account_id integer,
    created timestamp without time zone DEFAULT now() NOT NULL,
    removed timestamp without time zone,
    suspended timestamp without time zone,
    label character varying(250),
    suspended_reason character varying(250),
    origin_scheme character varying(10) DEFAULT 'http'::character varying NOT NULL,
    origin_port integer,
    origin_timeout integer,
    group_id integer NOT NULL
);
CREATE TABLE public.cdn_cnames (
    id integer NOT NULL,
    cdn_id integer NOT NULL,
    cname character varying(250) NOT NULL,
    priority integer DEFAULT 0 NOT NULL,
    uuid uuid NOT NULL,
    resource_id integer NOT NULL
);
CREATE SEQUENCE public.cdn_cnames_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.cdn_cnames_id_seq OWNED BY public.cdn_cnames.id;
CREATE TABLE public.cdn_http (
    id integer NOT NULL,
    cdn_id integer NOT NULL,
    push_zone_id character varying(100),
    cache_expiry integer NOT NULL,
    disable_query_string timestamp without time zone,
    ignore_set_cookie timestamp without time zone,
    url_signing_on timestamp without time zone,
    url_signing_key character varying(50),
    can_edit_locations boolean DEFAULT true,
    mp4_pseudo_on timestamp without time zone,
    instant_ssl timestamp(0) without time zone DEFAULT now(),
    https_redirect_code integer,
    url_signing_type character varying(10) DEFAULT 'parameter'::character varying,
    forward_host_header boolean DEFAULT false NOT NULL,
    streaming_playlist_bypass boolean DEFAULT false NOT NULL,
    waf boolean DEFAULT false NOT NULL,
    quic boolean DEFAULT false NOT NULL,
    cors_origin_header boolean DEFAULT false NOT NULL,
    origin_id uuid,
    query_string_ignore_type character varying(16),
    cache_expiry_404 integer
);
COMMENT ON COLUMN public.cdn_http.can_edit_locations IS 'If user can set individual locations';
CREATE SEQUENCE public.cdn_http_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.cdn_http_id_seq OWNED BY public.cdn_http.id;
CREATE TABLE public.cdn_http_protection (
    id integer NOT NULL,
    cdn_id integer NOT NULL,
    hlp_type character varying(10),
    hlp_deny_empty_referer boolean,
    ipp_type character varying(10),
    gp_type character varying(10)
);
CREATE SEQUENCE public.cdn_http_protection_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.cdn_http_protection_id_seq OWNED BY public.cdn_http_protection.id;
CREATE SEQUENCE public.cdn_id_seq
    START WITH 68903
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.cdn_id_seq OWNED BY public.cdn.id;
CREATE TABLE public.cdn_origin (
    id uuid NOT NULL,
    customer_id uuid NOT NULL,
    created_at timestamp without time zone NOT NULL,
    label character varying(255) NOT NULL,
    scheme character varying(5) NOT NULL,
    url character varying(255) NOT NULL,
    aws_access_key_id character varying(255),
    aws_access_key_secret character varying(255),
    aws_region character varying(255),
    type character varying(8) NOT NULL,
    port integer,
    base_dir character varying(255) DEFAULT NULL::character varying,
    timeout integer
);
CREATE TABLE public.cdn_origin_protection (
    id integer NOT NULL,
    cdn_id integer NOT NULL,
    cached boolean DEFAULT false NOT NULL,
    enabled timestamp with time zone
);
CREATE SEQUENCE public.cdn_origin_protection_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.cdn_origin_protection_id_seq OWNED BY public.cdn_origin_protection.id;
CREATE TABLE public.cdn_ssl (
    id integer NOT NULL,
    cdn_id integer NOT NULL,
    service_ssl_id integer NOT NULL
);
CREATE SEQUENCE public.cdn_ssl_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.cdn_ssl_id_seq OWNED BY public.cdn_ssl.id;
CREATE TABLE public.cdn_stream (
    cdn_id integer NOT NULL,
    protocol character varying(8) NOT NULL,
    origin_id integer NOT NULL,
    port integer NOT NULL,
    path character varying(512),
    key character varying(64) NOT NULL,
    ts_created timestamp(0) without time zone DEFAULT now() NOT NULL,
    password character varying(512),
    id integer NOT NULL
);
CREATE SEQUENCE public.cdn_stream_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.cdn_stream_id_seq OWNED BY public.cdn_stream.id;
CREATE TABLE public.cdn_traffic (
    id integer NOT NULL,
    cdn_id integer,
    "timestamp" timestamp(0) without time zone,
    last_month_ara numeric,
    last_day_ara numeric,
    non_cached_last_month_ara numeric,
    non_cached_last_day_ara numeric,
    total_ara numeric,
    non_cached_total_ara numeric
);
CREATE SEQUENCE public.cdn_traffic_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.cdn_traffic_id_seq OWNED BY public.cdn_traffic.id;
CREATE TABLE public.constants (
    id integer NOT NULL,
    constant text,
    what character varying(250)
);
CREATE SEQUENCE public.constants_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.constants_id_seq OWNED BY public.constants.id;
CREATE TABLE public.countries (
    id integer NOT NULL,
    iso character(2) NOT NULL,
    name character varying(255),
    short_name character varying(255) NOT NULL,
    iso3 character(3) DEFAULT NULL::bpchar,
    eu_member integer DEFAULT 0,
    vat_prefix character varying(4),
    vat_percent numeric,
    xero_tax_type_code character varying(15),
    continent character varying(3) NOT NULL,
    google_geo_id integer
);
CREATE SEQUENCE public.countries_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.countries_id_seq OWNED BY public.countries.id;
CREATE TABLE public.credit_history (
    id integer NOT NULL,
    account_id integer NOT NULL,
    date date NOT NULL,
    ts_updated timestamp(0) without time zone NOT NULL,
    credit real NOT NULL
);
CREATE SEQUENCE public.credit_history_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.credit_history_id_seq OWNED BY public.credit_history.id;
CREATE TABLE public.cron_log (
    id integer NOT NULL,
    ts timestamp(0) without time zone DEFAULT now() NOT NULL,
    subject character varying(256) NOT NULL,
    ts_end timestamp without time zone,
    result character varying(10) DEFAULT NULL::character varying,
    message text,
    parameters text
);
CREATE SEQUENCE public.cron_log_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.cron_log_id_seq OWNED BY public.cron_log.id;
CREATE TABLE public.crop_api_errors (
    id integer NOT NULL,
    description text,
    errors text,
    ts timestamp(0) without time zone DEFAULT now(),
    account_id integer,
    url character varying(1024),
    params text,
    ip inet
);
CREATE SEQUENCE public.crop_api_errors_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.crop_api_errors_id_seq OWNED BY public.crop_api_errors.id;
CREATE TABLE public.mail_addresses (
    id integer NOT NULL,
    ts timestamp(0) without time zone DEFAULT now() NOT NULL,
    email character varying(128) NOT NULL,
    wherefrom character varying(64),
    body json
);
CREATE SEQUENCE public.doctrine_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.doctrine_id_seq OWNED BY public.mail_addresses.id;
CREATE TABLE public.email_address_confirmation (
    id uuid NOT NULL,
    customer_id uuid NOT NULL,
    created_at timestamp(0) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    confirmed_at timestamp without time zone,
    email character varying(200) NOT NULL
);
CREATE TABLE public.events (
    id integer NOT NULL,
    event_time timestamp without time zone DEFAULT now(),
    source character varying(30),
    module character varying(50),
    controller character varying(50),
    action character varying(50),
    params text,
    account_id integer,
    vmachine_id integer,
    priority integer,
    http_host character varying(200),
    request_method character varying(10),
    remote_addr inet,
    cdn_id integer,
    message text,
    ajax boolean
);
CREATE TABLE public.events_before (
    id integer NOT NULL,
    ts timestamp without time zone DEFAULT now(),
    original_event_id integer NOT NULL,
    params text
);
CREATE SEQUENCE public.events_before_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.events_before_id_seq OWNED BY public.events_before.id;
CREATE TABLE public.events_history (
    id integer NOT NULL,
    event_time timestamp without time zone NOT NULL,
    source character varying(30),
    module character varying(50),
    controller character varying(50),
    action character varying(50),
    params text,
    account_id integer,
    vmachine_id integer,
    priority integer,
    http_host character varying(200),
    request_method character varying(10),
    remote_addr inet,
    cdn_id integer,
    message text,
    ajax boolean
);
CREATE SEQUENCE public.events_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.events_id_seq OWNED BY public.events.id;
CREATE TABLE public.exchange_rates (
    id integer NOT NULL,
    created timestamp without time zone DEFAULT now(),
    eur_2_usd numeric,
    gbp_2_usd numeric,
    brl_2_usd numeric,
    czk_2_usd numeric
);
CREATE SEQUENCE public.exchange_rates_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.exchange_rates_id_seq OWNED BY public.exchange_rates.id;
CREATE TABLE public.forgotten_password (
    id integer NOT NULL,
    email character varying(250) NOT NULL,
    hash character varying(100) NOT NULL,
    validity timestamp without time zone DEFAULT (now() + '24:00:00'::interval) NOT NULL,
    done boolean DEFAULT false NOT NULL,
    created_at timestamp without time zone
);
CREATE SEQUENCE public.forgotten_password_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.forgotten_password_id_seq OWNED BY public.forgotten_password.id;
CREATE TABLE public.geo_protection_countries (
    id integer NOT NULL,
    country character(2) NOT NULL,
    cdn_http_protection_id integer NOT NULL
);
CREATE SEQUENCE public.geo_protection_countries_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.geo_protection_countries_id_seq OWNED BY public.geo_protection_countries.id;
CREATE TABLE public.hlp_referer_domains (
    id integer NOT NULL,
    domain character varying(255) NOT NULL,
    cdn_id integer NOT NULL,
    ts timestamp(0) without time zone DEFAULT now() NOT NULL
);
CREATE SEQUENCE public.hlp_referer_domains_id_seq
    START WITH 121
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.hlp_referer_domains_id_seq OWNED BY public.hlp_referer_domains.id;
CREATE TABLE public.ignored_query_params (
    id integer NOT NULL,
    cdn_id integer NOT NULL,
    parameter character varying(100) NOT NULL,
    created timestamp without time zone DEFAULT now() NOT NULL
);
CREATE SEQUENCE public.ignored_query_params_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.ignored_query_params_id_seq OWNED BY public.ignored_query_params.id;
CREATE TABLE public.invoice_account_details (
    id integer NOT NULL,
    full_name character varying(150),
    company character varying(250),
    email character varying(200),
    street character varying(250),
    city character varying(250),
    state character varying(200),
    zip character varying(250),
    vat_number character varying(30),
    updated timestamp without time zone DEFAULT now(),
    account_id integer NOT NULL,
    fk_countries integer,
    phone character varying(32)
);
CREATE SEQUENCE public.invoice_account_details_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.invoice_account_details_id_seq OWNED BY public.invoice_account_details.id;
CREATE TABLE public.invoice_number_counter (
    issue_year integer NOT NULL,
    prefix character varying(8) NOT NULL,
    invoice_number integer DEFAULT 1 NOT NULL
);
CREATE TABLE public.invoices (
    id integer NOT NULL,
    account_id integer NOT NULL,
    invoice_date date NOT NULL,
    invoice_due_date date NOT NULL,
    number character varying(20) NOT NULL,
    line_amount_types character varying(30) NOT NULL,
    description character varying(250) NOT NULL,
    quantity integer NOT NULL,
    unit_amount numeric NOT NULL,
    payment_id integer,
    voided boolean DEFAULT false,
    sent timestamp without time zone,
    currency character varying(3),
    payments_general_id integer,
    tracked boolean DEFAULT false
);
CREATE SEQUENCE public.invoices_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.invoices_id_seq OWNED BY public.invoices.id;
CREATE TABLE public.ip_protection_addresses (
    id integer NOT NULL,
    ip_address cidr NOT NULL,
    cdn_http_protection_id integer NOT NULL
);
CREATE SEQUENCE public.ip_protection_addresses_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.ip_protection_addresses_id_seq OWNED BY public.ip_protection_addresses.id;
CREATE TABLE public.logins_log (
    id integer NOT NULL,
    account_id integer NOT NULL,
    ts timestamp(0) without time zone DEFAULT now() NOT NULL,
    ip character varying(50)
);
CREATE SEQUENCE public.logins_log_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.logins_log_id_seq OWNED BY public.logins_log.id;
CREATE TABLE public.mail_log_details (
    id integer NOT NULL,
    ts timestamp(0) without time zone DEFAULT now() NOT NULL,
    subject text,
    body text,
    account_id integer,
    email character varying(256) NOT NULL,
    solved boolean,
    item_id integer,
    subscribed_block boolean DEFAULT false,
    admin_email character varying(256),
    "from" character varying(256),
    delivery_status character varying(250),
    postmark_message_id uuid,
    type character varying(64)
);
COMMENT ON COLUMN public.mail_log_details.solved IS 'pokud je NULL, tak neposiled dalsi upozorneni';
COMMENT ON COLUMN public.mail_log_details.item_id IS 'ID objektu, ktereho se mail tykal (napr. SSL cert)';
CREATE SEQUENCE public.mail_log_details_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.mail_log_details_id_seq OWNED BY public.mail_log_details.id;
CREATE TABLE public.mail_templates (
    id integer NOT NULL,
    ts timestamp(0) without time zone DEFAULT now() NOT NULL,
    header text,
    footer text,
    name character varying(16) NOT NULL
);
CREATE SEQUENCE public.mail_templates_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.mail_templates_id_seq OWNED BY public.mail_templates.id;
CREATE TABLE public.mails (
    id integer NOT NULL,
    ts timestamp(0) without time zone DEFAULT now() NOT NULL,
    subject character varying(256) NOT NULL,
    body text NOT NULL,
    type character varying(64) NOT NULL,
    fk_mail_templates integer,
    title text,
    min_total_traffic integer,
    min_cdn_count integer,
    signature character varying(40),
    traffic_now boolean,
    days_before_send integer,
    dashboard_notice text,
    section character varying(256),
    implemented boolean DEFAULT false,
    note character varying(200),
    body_table text,
    utm character varying(200),
    copy_to_technical_mail boolean DEFAULT false,
    from_name character varying(255)
);
COMMENT ON COLUMN public.mails.min_total_traffic IS 'Minimalni celkovy traffic v MB';
COMMENT ON COLUMN public.mails.min_cdn_count IS 'Minimalni pocet vytvorenych CDNek';
COMMENT ON COLUMN public.mails.traffic_now IS 'Jestli ma nejaky traffic v poslednich 6ti hodinach';
COMMENT ON COLUMN public.mails.days_before_send IS 'Pocet dni pred udalosti, ke ktere se vaze email (pr. konec trialu)';
COMMENT ON COLUMN public.mails.utm IS 'nahrazuje v celem emailu u odkazu %utm%';
COMMENT ON COLUMN public.mails.from_name IS 'nastavuje name odesilatele';
CREATE SEQUENCE public.mails_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.mails_id_seq OWNED BY public.mails.id;
CREATE TABLE public.migration_versions (
    version character varying(14) NOT NULL,
    executed_at timestamp(0) without time zone NOT NULL
);
COMMENT ON COLUMN public.migration_versions.executed_at IS '(DC2Type:datetime_immutable)';
CREATE TABLE public.migrations_versions (
    version character varying(14) NOT NULL,
    executed_at timestamp(0) without time zone NOT NULL
);
COMMENT ON COLUMN public.migrations_versions.executed_at IS '(DC2Type:datetime_immutable)';
CREATE TABLE public.monthly_plan_group (
    id integer NOT NULL,
    name character varying(50) NOT NULL,
    edge_group_id integer NOT NULL
);
CREATE SEQUENCE public.monthly_plan_group_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.monthly_plan_group_id_seq OWNED BY public.monthly_plan_group.id;
CREATE TABLE public.monthly_traffic_plans (
    id integer NOT NULL,
    traffic real NOT NULL,
    price real NOT NULL,
    overages_price real NOT NULL,
    group_id integer NOT NULL
);
CREATE SEQUENCE public.monthly_traffic_plans_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.monthly_traffic_plans_id_seq OWNED BY public.monthly_traffic_plans.id;
CREATE TABLE public.netbanx_purchases (
    id integer NOT NULL,
    merchant_account_number character varying(20) NOT NULL,
    merchant_reference_number character varying(50),
    amount numeric,
    card_pay_method character varying(30) DEFAULT 'WEB'::character varying,
    first_name character varying(40),
    last_name character varying(40),
    street character varying(50),
    street2 character varying(50),
    city character varying(100),
    state_or_region character varying(40),
    country character varying(10),
    zip character varying(10),
    phone character varying(40),
    email character varying(100),
    recurring_indicator character varying(1),
    original_confirmation_number character varying(20),
    previous_confirmation_number character varying(20),
    customer_ip character varying(50),
    product_type character varying(1) DEFAULT 'D'::character varying,
    customer_id character varying(64),
    keyword character varying(255),
    created timestamp without time zone DEFAULT now(),
    cl_purchase_type character varying(100),
    updated timestamp without time zone,
    cl_product_type character varying(100),
    cl_product_comment character varying(100),
    cl_nodes integer,
    cl_billing_period_from timestamp without time zone,
    cl_billing_period_to timestamp without time zone,
    account_id integer,
    request_type character varying(50),
    confirmation_number character varying(20),
    resp_confirmation_number character varying(20),
    resp_decision character varying(20),
    resp_code integer,
    resp_action_code character varying(3),
    resp_auth_code character varying(20),
    resp_duplicate_found boolean,
    resp_xml text,
    resp_created timestamp without time zone,
    resp_txn_time timestamp without time zone,
    resp_currency_code character varying(10),
    resp_avs_response character varying(3),
    resp_cvd_response character varying(3),
    resp_description character varying(1024),
    random_verify_amount numeric,
    verified timestamp without time zone,
    get_rest_attempt timestamp without time zone,
    verify_attempts_count integer DEFAULT 0,
    accepted_by_us timestamp without time zone,
    rest_charged timestamp without time zone,
    rest_charge_attempts integer,
    last_fail_verify_attempt timestamp without time zone,
    cancelled timestamp without time zone,
    cancelled_status character varying(20) DEFAULT NULL::character varying,
    ccnf character varying(50),
    amount_in_dollars numeric,
    exchange_rate numeric,
    currency character varying(3) DEFAULT 'EUR'::character varying,
    cc_type character varying(30) DEFAULT NULL::character varying,
    stop_charging timestamp without time zone,
    vat_percent numeric DEFAULT (0)::numeric,
    vat_amount_usd numeric DEFAULT (0)::numeric,
    total_usd numeric,
    xero_invoice_number character varying(30),
    sent_xml text,
    autorecharge boolean DEFAULT false,
    total_gbp numeric,
    vat_amount_gbp numeric,
    who_recharge_account_id integer,
    payments_general_id integer
);
CREATE SEQUENCE public.netbanx_purchases_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.netbanx_purchases_id_seq OWNED BY public.netbanx_purchases.id;
CREATE TABLE public.oauth_tokens (
    id uuid NOT NULL,
    created_at timestamp without time zone NOT NULL,
    access_token text NOT NULL,
    refresh_token character varying(255) NOT NULL,
    type character varying(255) NOT NULL,
    service character varying(255) NOT NULL,
    expires_at timestamp without time zone NOT NULL
);
CREATE TABLE public.payment_recipes (
    id uuid NOT NULL,
    recipe_id uuid NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    last_four character varying(4) NOT NULL,
    brand character varying(10) NOT NULL,
    expires_at timestamp without time zone NOT NULL,
    account_id integer NOT NULL,
    is_default boolean DEFAULT false NOT NULL
);
CREATE TABLE public.payments_general (
    id integer NOT NULL,
    status boolean,
    ts_added timestamp(0) without time zone NOT NULL,
    ts_payment timestamp(0) without time zone NOT NULL,
    account_id integer NOT NULL,
    amount real NOT NULL,
    currency character varying(5) NOT NULL,
    amount_in_usd real NOT NULL,
    vat_percent real NOT NULL,
    payment_method character varying(10) NOT NULL,
    recharge boolean NOT NULL,
    description text,
    autorecharge boolean DEFAULT false NOT NULL,
    who_charged integer,
    bonus real DEFAULT (0)::real,
    source character varying,
    payment_fee real,
    status_message text,
    external_payment_id character varying(255),
    paygate_id uuid
);
COMMENT ON COLUMN public.payments_general.status IS 'if payment was successfull or not';
COMMENT ON COLUMN public.payments_general.amount IS 'amount with vat in specific currency';
COMMENT ON COLUMN public.payments_general.recharge IS 'is this first payment or recharge payment';
COMMENT ON COLUMN public.payments_general.description IS 'this is used for invoices';
COMMENT ON COLUMN public.payments_general.autorecharge IS 'is is autorecharge payment';
COMMENT ON COLUMN public.payments_general.who_charged IS 'who charged (for kox and invoice payments)';
CREATE SEQUENCE public.payments_general_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.payments_general_id_seq OWNED BY public.payments_general.id;
CREATE TABLE public.paypal (
    id integer NOT NULL,
    account_id integer NOT NULL,
    payerid character varying(50),
    correlationid character varying(50),
    ack character varying(50),
    version character varying(50),
    build character varying(50),
    email character varying(50),
    payerstatus character varying(50),
    firstname character varying(50),
    lastname character varying(50),
    countrycode character varying(50),
    "timestamp" character varying(50),
    token character varying(50),
    custom character varying(200),
    business character varying(100),
    response text,
    currency character varying(10),
    monthly_traffic_plan_id integer,
    promo_code_id integer
);
CREATE SEQUENCE public.paypal_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.paypal_id_seq OWNED BY public.paypal.id;
CREATE TABLE public.paypal_payments (
    id integer NOT NULL,
    account_id integer NOT NULL,
    token character varying(50),
    "timestamp" character varying(50),
    correlationid character varying(50),
    ack character varying(50),
    version character varying(50),
    build character varying(50),
    transactionid character varying(50),
    transactiontype character varying(50),
    paymenttype character varying(50),
    ordertime character varying(50),
    amt real,
    feeamt real,
    settleamt real,
    taxamt real,
    response text,
    reasoncode character varying(50),
    exchangerate real,
    currencycode character varying(50),
    paymentstatus character varying(50),
    pendingreason character varying(200),
    l_errorcode0 character varying(50),
    l_shortmessage0 character varying(200),
    l_longmessage0 text,
    l_severitycode0 character varying(50)
);
CREATE SEQUENCE public.paypal_payments_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.paypal_payments_id_seq OWNED BY public.paypal_payments.id;
CREATE TABLE public.perfops_asn (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    number integer NOT NULL,
    "95th_percentile" numeric
);
CREATE TABLE public.perfops_data (
    id uuid NOT NULL,
    "timestamp" timestamp without time zone NOT NULL,
    cdn_id character varying(3) NOT NULL,
    continent_code character varying(2) NOT NULL,
    header text NOT NULL,
    ms double precision NOT NULL,
    uagent text NOT NULL,
    uip character varying(255) NOT NULL,
    up boolean NOT NULL,
    asn_name character varying(255) NOT NULL,
    asn_number integer NOT NULL,
    cache boolean NOT NULL,
    hostname character varying(255) NOT NULL,
    country character varying(255) NOT NULL,
    edge_pop character varying(255) DEFAULT NULL::character varying
);
CREATE TABLE public.permanent_logins (
    account_id integer NOT NULL,
    ts timestamp(0) without time zone DEFAULT now() NOT NULL,
    token character(32) NOT NULL
);
CREATE TABLE public.predefined_contact_mails (
    id integer NOT NULL,
    subject character varying(255) NOT NULL,
    body text NOT NULL,
    email character varying(255),
    note character varying(200),
    language character varying(200) NOT NULL,
    group_type character varying(255) NOT NULL
);
CREATE SEQUENCE public.predefined_contact_mails_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.predefined_contact_mails_id_seq OWNED BY public.predefined_contact_mails.id;
CREATE TABLE public.promo_codes (
    code character varying(255) NOT NULL,
    account_id integer,
    name character varying(100) NOT NULL,
    description character varying(255),
    category character varying(50) NOT NULL,
    value_type character varying(50) NOT NULL,
    value integer NOT NULL,
    id integer NOT NULL,
    valid_since timestamp(0) with time zone,
    valid_until timestamp(0) with time zone
);
CREATE SEQUENCE public.promo_codes_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.promo_codes_id_seq OWNED BY public.promo_codes.id;
CREATE TABLE public.promo_codes_usages (
    id integer NOT NULL,
    promo_code_id integer NOT NULL,
    account_id integer NOT NULL,
    used_at timestamp with time zone DEFAULT now() NOT NULL
);
CREATE SEQUENCE public.promo_codes_usages_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.promo_codes_usages_id_seq OWNED BY public.promo_codes_usages.id;
CREATE TABLE public.publish_point_internal (
    id integer NOT NULL,
    name character varying(256) NOT NULL,
    active smallint
);
CREATE TABLE public.queue_calls (
    id integer NOT NULL,
    data text,
    created timestamp without time zone DEFAULT now(),
    queued_accounts integer,
    queued_paths integer,
    paths_in_call integer,
    accounts_in_call integer
);
COMMENT ON COLUMN public.queue_calls.queued_accounts IS 'pocet accounts, ktere jsou ve fronte';
COMMENT ON COLUMN public.queue_calls.queued_paths IS 'pocet paths, ktere jsou ve fronte';
COMMENT ON COLUMN public.queue_calls.paths_in_call IS 'pocet paths, ktere byly zarazeny do jednoho zpracovani';
COMMENT ON COLUMN public.queue_calls.accounts_in_call IS 'pocet accounts, ktere byly zarazeny do jednoho zpracovani';
CREATE SEQUENCE public.queue_calls_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.queue_calls_id_seq OWNED BY public.queue_calls.id;
CREATE TABLE public.queued_paths (
    id bigint NOT NULL,
    queued_requests_id integer NOT NULL,
    path text NOT NULL,
    finished timestamp without time zone,
    finished_successfully boolean DEFAULT false NOT NULL,
    lock timestamp without time zone
)
WITH (autovacuum_vacuum_scale_factor='0', autovacuum_vacuum_threshold='20000', autovacuum_analyze_scale_factor='0', autovacuum_analyze_threshold='20000');
CREATE TABLE public.queued_requests (
    id integer NOT NULL,
    account_id integer NOT NULL,
    cdn_id integer NOT NULL,
    type character varying(10) NOT NULL,
    created timestamp without time zone DEFAULT now(),
    finished timestamp without time zone,
    waiting_for integer
);
CREATE VIEW public.queue_status AS
 SELECT qr.id,
    qr.account_id,
    qr.cdn_id,
    qr.type,
    qr.created,
    qr.finished,
    qr.waiting_for,
    ( SELECT count(*) AS count
           FROM public.queued_paths
          WHERE ((queued_paths.queued_requests_id = qr.id) AND (queued_paths.finished_successfully IS FALSE))) AS finished_false
   FROM public.queued_requests qr
  WHERE (( SELECT count(*) AS count
           FROM public.queued_paths
          WHERE ((queued_paths.queued_requests_id = qr.id) AND (queued_paths.finished_successfully IS FALSE))) > 1)
  ORDER BY qr.id DESC;
CREATE SEQUENCE public.queued_paths_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.queued_paths_id_seq OWNED BY public.queued_paths.id;
CREATE TABLE public.queued_paths_requests (
    id integer NOT NULL,
    queued_paths_id bigint NOT NULL,
    requests_id integer NOT NULL,
    created timestamp without time zone DEFAULT now()
)
WITH (autovacuum_vacuum_scale_factor='0', autovacuum_vacuum_threshold='1000', autovacuum_analyze_scale_factor='0', autovacuum_analyze_threshold='1000');
CREATE SEQUENCE public.queued_paths_requests_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.queued_paths_requests_id_seq OWNED BY public.queued_paths_requests.id;
CREATE SEQUENCE public.queued_requests_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.queued_requests_id_seq OWNED BY public.queued_requests.id;
CREATE TABLE public.referers (
    id integer NOT NULL,
    ip character varying(15),
    rcookie text,
    referer text,
    created timestamp without time zone DEFAULT now()
);
CREATE SEQUENCE public.referers_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.referers_id_seq OWNED BY public.referers.id;
CREATE TABLE public.requests (
    id integer NOT NULL,
    ts_start timestamp without time zone,
    ts_end timestamp without time zone,
    url text,
    send_data text,
    response text,
    ts timestamp without time zone,
    account_id integer,
    note text,
    events_id integer,
    response_headers text,
    nxg_request_id character varying(60),
    response_code smallint
);
CREATE SEQUENCE public.requests_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.requests_id_seq OWNED BY public.requests.id;
CREATE TABLE public.responses (
    id integer NOT NULL,
    status character varying(32),
    description text,
    error text,
    errors text,
    others text,
    account_id integer,
    event_id integer,
    affected_account_id integer,
    created timestamp without time zone DEFAULT now(),
    request_from_client boolean
);
CREATE SEQUENCE public.responses_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.responses_id_seq OWNED BY public.responses.id;
CREATE TABLE public.service_payments (
    id integer NOT NULL,
    service_id integer NOT NULL,
    service_from timestamp without time zone NOT NULL,
    service_to timestamp without time zone NOT NULL,
    service_name character varying(100) NOT NULL,
    amount numeric NOT NULL,
    payment_table character varying(50) NOT NULL,
    payment_id integer NOT NULL,
    created timestamp without time zone DEFAULT now(),
    note text
);
CREATE SEQUENCE public.service_payments_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.service_payments_id_seq OWNED BY public.service_payments.id;
CREATE TABLE public.service_raw_logs (
    id integer NOT NULL,
    service_id integer NOT NULL,
    cdn_id integer NOT NULL,
    "from" timestamp(0) without time zone NOT NULL,
    "to" timestamp(0) without time zone NOT NULL,
    activated_by_account boolean DEFAULT true NOT NULL,
    price integer DEFAULT 49 NOT NULL,
    deactivate_in_nxg timestamp without time zone,
    who_deactivate character varying(255),
    trial boolean DEFAULT false
);
CREATE SEQUENCE public.service_raw_logs_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.service_raw_logs_id_seq OWNED BY public.service_raw_logs.id;
CREATE TABLE public.service_ssl (
    id integer NOT NULL,
    service_id integer,
    deleted timestamp(0) without time zone,
    type character varying(5) DEFAULT 'SNI'::character varying NOT NULL,
    ssl_key text,
    ssl_crt text,
    disable_notifications boolean DEFAULT false NOT NULL,
    ssl_valid_to timestamp(0) with time zone,
    updated timestamp without time zone,
    uuid uuid
);
CREATE SEQUENCE public.service_ssl_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.service_ssl_id_seq OWNED BY public.service_ssl.id;
CREATE TABLE public.services_new (
    id integer NOT NULL,
    account_id integer NOT NULL,
    ts timestamp(0) without time zone DEFAULT now() NOT NULL,
    canceled timestamp(0) without time zone,
    period integer,
    name text NOT NULL
);
COMMENT ON COLUMN public.services_new.period IS 'in months';
CREATE SEQUENCE public.services_new_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.services_new_id_seq OWNED BY public.services_new.id;
CREATE TABLE public.ssl_domains (
    id integer NOT NULL,
    service_ssl_id integer NOT NULL,
    domain character varying(260) NOT NULL
);
CREATE SEQUENCE public.ssl_domains_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.ssl_domains_id_seq OWNED BY public.ssl_domains.id;
CREATE TABLE public.status (
    id integer NOT NULL,
    location_id character varying(512) NOT NULL,
    country character varying(32),
    status integer DEFAULT 404 NOT NULL,
    updated_at timestamp without time zone DEFAULT '2015-07-02 14:25:12.053178'::timestamp without time zone,
    updated_timezone timestamp with time zone,
    visible boolean DEFAULT true NOT NULL
);
CREATE SEQUENCE public.status_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.status_id_seq OWNED BY public.status.id;
CREATE TABLE public.storage_pricing (
    id integer NOT NULL,
    size integer NOT NULL,
    price integer NOT NULL,
    files_count_limit integer
);
CREATE SEQUENCE public.storage_pricing_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.storage_pricing_id_seq OWNED BY public.storage_pricing.id;
CREATE TABLE public.storage_tariffs (
    id integer NOT NULL,
    account_id integer NOT NULL,
    tariff_id integer,
    tariff integer,
    charged_at timestamp with time zone DEFAULT now(),
    valid_to timestamp with time zone,
    note character varying(255),
    tariff_price integer,
    tariff_price_included boolean DEFAULT false NOT NULL,
    tariff_canceled timestamp with time zone,
    files_count_limit integer,
    auto_recharge boolean DEFAULT false
);
CREATE SEQUENCE public.storage_tariffs_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.storage_tariffs_id_seq OWNED BY public.storage_tariffs.id;
CREATE TABLE public.stream_origins (
    id integer NOT NULL,
    origin character varying NOT NULL,
    continent character varying NOT NULL,
    country character varying NOT NULL
);
CREATE SEQUENCE public.stream_origins_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.stream_origins_id_seq OWNED BY public.stream_origins.id;
CREATE TABLE public.tariffs (
    id integer NOT NULL,
    account_id integer NOT NULL,
    created timestamp without time zone DEFAULT now(),
    ended timestamp without time zone,
    price numeric,
    used_dollars numeric DEFAULT (0)::numeric NOT NULL,
    over_limit_dollars numeric DEFAULT (0)::numeric NOT NULL,
    free_bonus numeric DEFAULT (0)::numeric,
    message text,
    valid_from timestamp(0) without time zone,
    who_charge_account_id integer,
    ts_refresh timestamp(0) without time zone,
    used_dollars_traffic_db numeric,
    duration numeric
);
COMMENT ON COLUMN public.tariffs.who_charge_account_id IS 'who charge the account';
CREATE SEQUENCE public.tariffs_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.tariffs_id_seq OWNED BY public.tariffs.id;
CREATE TABLE public.tariffs_log (
    id integer NOT NULL,
    ts timestamp(0) without time zone DEFAULT now() NOT NULL,
    tariffs_id integer NOT NULL,
    initial_credit numeric NOT NULL,
    credit numeric NOT NULL,
    purpose character varying(255) NOT NULL,
    account_id integer NOT NULL,
    who_charged_account_id integer,
    purpose_internal character varying(255),
    public boolean DEFAULT true NOT NULL,
    type character varying(100),
    accounting_date date
);
COMMENT ON COLUMN public.tariffs_log.credit IS 'amount of substracted USD';
COMMENT ON COLUMN public.tariffs_log.account_id IS 'what account was charged';
COMMENT ON COLUMN public.tariffs_log.who_charged_account_id IS 'who charged the account';
CREATE SEQUENCE public.tariffs_log_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.tariffs_log_id_seq OWNED BY public.tariffs_log.id;
CREATE TABLE public.tik_tok_datacenter_traffic (
    id uuid NOT NULL,
    datacenter_id uuid NOT NULL,
    day timestamp without time zone NOT NULL,
    traffic numeric NOT NULL,
    is_dsa boolean NOT NULL
);
CREATE TABLE public.tik_tok_region_locations (
    id uuid NOT NULL,
    location_id uuid NOT NULL,
    region_id uuid NOT NULL,
    hidden boolean NOT NULL
);
CREATE TABLE public.tik_tok_regions (
    id uuid NOT NULL,
    ts_from timestamp without time zone NOT NULL,
    ts_to timestamp without time zone,
    name character varying(50) NOT NULL,
    price real NOT NULL,
    weight integer NOT NULL,
    customer_id uuid
);
CREATE TABLE public.tiktok_country_traffic (
    id uuid NOT NULL,
    country character(2) NOT NULL,
    traffic numeric NOT NULL,
    traffic_from timestamp without time zone NOT NULL,
    traffic_to timestamp without time zone NOT NULL
);
CREATE TABLE public.xero_checksum (
    id integer NOT NULL,
    year integer NOT NULL,
    month integer NOT NULL,
    checksum numeric NOT NULL,
    ts timestamp without time zone DEFAULT now()
);
CREATE SEQUENCE public.xero_checksum_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.xero_checksum_id_seq OWNED BY public.xero_checksum.id;
CREATE TABLE public.xero_report (
    id integer NOT NULL,
    year integer NOT NULL,
    month integer NOT NULL,
    server_depreciation_expense numeric NOT NULL,
    hw_accessories numeric NOT NULL,
    server_installation_maintanace_repairs numeric NOT NULL,
    insurance numeric NOT NULL,
    server_rental_data numeric NOT NULL,
    advertising_marketing numeric NOT NULL,
    events_conferences numeric NOT NULL,
    internal_tools numeric NOT NULL,
    bank_fees_barclay numeric NOT NULL,
    netbanx_fees numeric NOT NULL,
    bank_fees_paypal numeric NOT NULL,
    fees_stripe numeric NOT NULL,
    postage_freight_courier numeric NOT NULL,
    general_legal_administration numeric NOT NULL,
    travel_international numeric NOT NULL,
    telephone_internet numeric NOT NULL,
    sales numeric NOT NULL,
    other_revenue numeric NOT NULL,
    usd_2_gbp numeric NOT NULL,
    foreign_currency_gains_and_losses numeric,
    datacamp_s_r_o numeric,
    consulting numeric,
    advertising_marketing_other numeric,
    advertising_marketing_ppc numeric,
    advertising_marketing_pr numeric,
    bad_debts numeric,
    bank_fees_vr_bank numeric,
    bank_fees_stripe numeric,
    cost_of_goods numeric,
    sale_of_goods numeric,
    sales_services numeric,
    interest_income numeric,
    audit_accountancy_fees numeric,
    corporation_tax numeric,
    miscellaneous numeric,
    ip numeric,
    colocation_dedicated numeric,
    sales_services_10g numeric
);
CREATE SEQUENCE public.xero_report_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.xero_report_id_seq OWNED BY public.xero_report.id;
CREATE TABLE storages.server (
    id character varying NOT NULL,
    location character varying NOT NULL,
    public boolean DEFAULT false NOT NULL,
    type storages.drive_type DEFAULT 'HDD'::storages.drive_type
);
CREATE TABLE storages.zone (
    id character varying(64) NOT NULL,
    user_id integer NOT NULL,
    zone_name character varying(300) NOT NULL,
    connect_user character varying(30),
    connect_pass character varying(50),
    server character varying DEFAULT 'push-1.cdn77.com'::character varying NOT NULL,
    www_url character varying NOT NULL,
    deleted timestamp(0) without time zone,
    ts timestamp(0) without time zone DEFAULT now() NOT NULL,
    new_id uuid NOT NULL,
    timeout integer
);
CREATE TABLE storages.zone_space_stats (
    id bigint NOT NULL,
    zone_id character varying NOT NULL,
    "timestamp" timestamp without time zone DEFAULT now() NOT NULL,
    space bigint DEFAULT 0 NOT NULL,
    nodes bigint DEFAULT 0 NOT NULL
);
CREATE VIEW storages.zone_space_stats_actual AS
 SELECT DISTINCT zss.id,
    zss.zone_id,
    zss."timestamp",
    zss.space,
    zss.nodes
   FROM (( SELECT z.id AS zone_id,
            ( SELECT max(zs."timestamp") AS max
                   FROM storages.zone_space_stats zs
                  WHERE ((zs.zone_id)::text = (z.id)::text)) AS "timestamp"
           FROM storages.zone z
          WHERE (NOT (z.ts IS NULL))) f
     JOIN storages.zone_space_stats zss ON ((((f.zone_id)::text = (zss.zone_id)::text) AND (f."timestamp" = zss."timestamp"))));
CREATE SEQUENCE storages.zone_space_stats_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE storages.zone_space_stats_id_seq OWNED BY storages.zone_space_stats.id;
ALTER TABLE ONLY api.access_list ALTER COLUMN id SET DEFAULT nextval('api.access_list_id_seq'::regclass);
ALTER TABLE ONLY api.categories ALTER COLUMN id SET DEFAULT nextval('api.categories_id_seq'::regclass);
ALTER TABLE ONLY api.credentials_personal_token ALTER COLUMN id SET DEFAULT nextval('api.credentials_personal_token_id_seq'::regclass);
ALTER TABLE ONLY api.examples ALTER COLUMN id SET DEFAULT nextval('api.examples_id_seq'::regclass);
ALTER TABLE ONLY api.method_has_parameter ALTER COLUMN id SET DEFAULT nextval('api.method_has_parameter_id_seq'::regclass);
ALTER TABLE ONLY api.methods ALTER COLUMN id SET DEFAULT nextval('api.methods_id_seq'::regclass);
ALTER TABLE ONLY api.methods_has_return_parameters ALTER COLUMN id SET DEFAULT nextval('api.methods_has_return_parameters_id_seq'::regclass);
ALTER TABLE ONLY api.parameters ALTER COLUMN id SET DEFAULT nextval('api.parameters_id_seq'::regclass);
ALTER TABLE ONLY api.parameters_types ALTER COLUMN id SET DEFAULT nextval('api.parameters_types_id_seq'::regclass);
ALTER TABLE ONLY api.parameters_valid_values ALTER COLUMN id SET DEFAULT nextval('api.parameters_valid_values_id_seq'::regclass);
ALTER TABLE ONLY api.version_has_category ALTER COLUMN id SET DEFAULT nextval('api.version_has_category_id_seq'::regclass);
ALTER TABLE ONLY api.versions ALTER COLUMN id SET DEFAULT nextval('api.versions_id_seq'::regclass);
ALTER TABLE ONLY datacenters.aflexi_locations_edge_ids ALTER COLUMN id SET DEFAULT nextval('datacenters.aflexi_locations_edge_ids_id_seq'::regclass);
ALTER TABLE ONLY pricing.individual_prices ALTER COLUMN id SET DEFAULT nextval('pricing.individual_prices_id_seq'::regclass);
ALTER TABLE ONLY pricing.plans ALTER COLUMN id SET DEFAULT nextval('pricing.plans_id_seq'::regclass);
ALTER TABLE ONLY pricing.plans_cdns ALTER COLUMN id SET DEFAULT nextval('pricing.plans_cdns_id_seq'::regclass);
ALTER TABLE ONLY pricing.plans_locations ALTER COLUMN id SET DEFAULT nextval('pricing.plans_locations_id_seq'::regclass);
ALTER TABLE ONLY public.account_big_traffic_change ALTER COLUMN id SET DEFAULT nextval('public.account_big_traffic_change_id_seq'::regclass);
ALTER TABLE ONLY public.account_category ALTER COLUMN id SET DEFAULT nextval('public.account_category_id_seq'::regclass);
ALTER TABLE ONLY public.account_comments ALTER COLUMN id SET DEFAULT nextval('public.account_comments_id_seq'::regclass);
ALTER TABLE ONLY public.account_flags ALTER COLUMN id SET DEFAULT nextval('public.account_flags_id_seq'::regclass);
ALTER TABLE ONLY public.account_ga_cids ALTER COLUMN id SET DEFAULT nextval('public.account_ga_cids_id_seq'::regclass);
ALTER TABLE ONLY public.account_mails ALTER COLUMN id SET DEFAULT nextval('public.account_mails_id_seq'::regclass);
ALTER TABLE ONLY public.account_monthly_traffic_plans ALTER COLUMN id SET DEFAULT nextval('public.account_monthly_traffic_plans_id_seq'::regclass);
ALTER TABLE ONLY public.account_payment_settings ALTER COLUMN id SET DEFAULT nextval('public.account_payment_settings_id_seq'::regclass);
ALTER TABLE ONLY public.account_settings ALTER COLUMN id SET DEFAULT nextval('public.account_settings_id_seq'::regclass);
ALTER TABLE ONLY public.account_traffic ALTER COLUMN id SET DEFAULT nextval('public.account_traffic_id_seq'::regclass);
ALTER TABLE ONLY public.account_xero_contacts ALTER COLUMN id SET DEFAULT nextval('public.account_xero_contacts_id_seq'::regclass);
ALTER TABLE ONLY public.accounts ALTER COLUMN id SET DEFAULT nextval('public.accounts_id_seq'::regclass);
ALTER TABLE ONLY public.actionlogs ALTER COLUMN id SET DEFAULT nextval('public.actionlogs_id_seq'::regclass);
ALTER TABLE ONLY public.adwords_costs_daily ALTER COLUMN id SET DEFAULT nextval('public.adwords_costs_daily_id_seq'::regclass);
ALTER TABLE ONLY public.adwords_costs_monitor_daily ALTER COLUMN id SET DEFAULT nextval('public.adwords_costs_monitor_daily_id_seq'::regclass);
ALTER TABLE ONLY public.alerts ALTER COLUMN id SET DEFAULT nextval('public.alerts_id_seq'::regclass);
ALTER TABLE ONLY public.alerts_users ALTER COLUMN id SET DEFAULT nextval('public.alerts_users_id_seq'::regclass);
ALTER TABLE ONLY public.application_states ALTER COLUMN id SET DEFAULT nextval('public.application_states_id_seq'::regclass);
ALTER TABLE ONLY public.ask_colleague_hash ALTER COLUMN id SET DEFAULT nextval('public.ask_colleague_hash_id_seq'::regclass);
ALTER TABLE ONLY public.cdn ALTER COLUMN id SET DEFAULT nextval('public.cdn_id_seq'::regclass);
ALTER TABLE ONLY public.cdn_cnames ALTER COLUMN id SET DEFAULT nextval('public.cdn_cnames_id_seq'::regclass);
ALTER TABLE ONLY public.cdn_http ALTER COLUMN id SET DEFAULT nextval('public.cdn_http_id_seq'::regclass);
ALTER TABLE ONLY public.cdn_http_protection ALTER COLUMN id SET DEFAULT nextval('public.cdn_http_protection_id_seq'::regclass);
ALTER TABLE ONLY public.cdn_origin_protection ALTER COLUMN id SET DEFAULT nextval('public.cdn_origin_protection_id_seq'::regclass);
ALTER TABLE ONLY public.cdn_ssl ALTER COLUMN id SET DEFAULT nextval('public.cdn_ssl_id_seq'::regclass);
ALTER TABLE ONLY public.cdn_stream ALTER COLUMN id SET DEFAULT nextval('public.cdn_stream_id_seq'::regclass);
ALTER TABLE ONLY public.cdn_traffic ALTER COLUMN id SET DEFAULT nextval('public.cdn_traffic_id_seq'::regclass);
ALTER TABLE ONLY public.constants ALTER COLUMN id SET DEFAULT nextval('public.constants_id_seq'::regclass);
ALTER TABLE ONLY public.countries ALTER COLUMN id SET DEFAULT nextval('public.countries_id_seq'::regclass);
ALTER TABLE ONLY public.credit_history ALTER COLUMN id SET DEFAULT nextval('public.credit_history_id_seq'::regclass);
ALTER TABLE ONLY public.cron_log ALTER COLUMN id SET DEFAULT nextval('public.cron_log_id_seq'::regclass);
ALTER TABLE ONLY public.crop_api_errors ALTER COLUMN id SET DEFAULT nextval('public.crop_api_errors_id_seq'::regclass);
ALTER TABLE ONLY public.events ALTER COLUMN id SET DEFAULT nextval('public.events_id_seq'::regclass);
ALTER TABLE ONLY public.events_before ALTER COLUMN id SET DEFAULT nextval('public.events_before_id_seq'::regclass);
ALTER TABLE ONLY public.exchange_rates ALTER COLUMN id SET DEFAULT nextval('public.exchange_rates_id_seq'::regclass);
ALTER TABLE ONLY public.forgotten_password ALTER COLUMN id SET DEFAULT nextval('public.forgotten_password_id_seq'::regclass);
ALTER TABLE ONLY public.geo_protection_countries ALTER COLUMN id SET DEFAULT nextval('public.geo_protection_countries_id_seq'::regclass);
ALTER TABLE ONLY public.hlp_referer_domains ALTER COLUMN id SET DEFAULT nextval('public.hlp_referer_domains_id_seq'::regclass);
ALTER TABLE ONLY public.ignored_query_params ALTER COLUMN id SET DEFAULT nextval('public.ignored_query_params_id_seq'::regclass);
ALTER TABLE ONLY public.invoice_account_details ALTER COLUMN id SET DEFAULT nextval('public.invoice_account_details_id_seq'::regclass);
ALTER TABLE ONLY public.invoices ALTER COLUMN id SET DEFAULT nextval('public.invoices_id_seq'::regclass);
ALTER TABLE ONLY public.ip_protection_addresses ALTER COLUMN id SET DEFAULT nextval('public.ip_protection_addresses_id_seq'::regclass);
ALTER TABLE ONLY public.logins_log ALTER COLUMN id SET DEFAULT nextval('public.logins_log_id_seq'::regclass);
ALTER TABLE ONLY public.mail_addresses ALTER COLUMN id SET DEFAULT nextval('public.doctrine_id_seq'::regclass);
ALTER TABLE ONLY public.mail_log_details ALTER COLUMN id SET DEFAULT nextval('public.mail_log_details_id_seq'::regclass);
ALTER TABLE ONLY public.mail_templates ALTER COLUMN id SET DEFAULT nextval('public.mail_templates_id_seq'::regclass);
ALTER TABLE ONLY public.mails ALTER COLUMN id SET DEFAULT nextval('public.mails_id_seq'::regclass);
ALTER TABLE ONLY public.monthly_plan_group ALTER COLUMN id SET DEFAULT nextval('public.monthly_plan_group_id_seq'::regclass);
ALTER TABLE ONLY public.monthly_traffic_plans ALTER COLUMN id SET DEFAULT nextval('public.monthly_traffic_plans_id_seq'::regclass);
ALTER TABLE ONLY public.netbanx_purchases ALTER COLUMN id SET DEFAULT nextval('public.netbanx_purchases_id_seq'::regclass);
ALTER TABLE ONLY public.payments_general ALTER COLUMN id SET DEFAULT nextval('public.payments_general_id_seq'::regclass);
ALTER TABLE ONLY public.paypal ALTER COLUMN id SET DEFAULT nextval('public.paypal_id_seq'::regclass);
ALTER TABLE ONLY public.paypal_payments ALTER COLUMN id SET DEFAULT nextval('public.paypal_payments_id_seq'::regclass);
ALTER TABLE ONLY public.predefined_contact_mails ALTER COLUMN id SET DEFAULT nextval('public.predefined_contact_mails_id_seq'::regclass);
ALTER TABLE ONLY public.promo_codes ALTER COLUMN id SET DEFAULT nextval('public.promo_codes_id_seq'::regclass);
ALTER TABLE ONLY public.promo_codes_usages ALTER COLUMN id SET DEFAULT nextval('public.promo_codes_usages_id_seq'::regclass);
ALTER TABLE ONLY public.queue_calls ALTER COLUMN id SET DEFAULT nextval('public.queue_calls_id_seq'::regclass);
ALTER TABLE ONLY public.queued_paths ALTER COLUMN id SET DEFAULT nextval('public.queued_paths_id_seq'::regclass);
ALTER TABLE ONLY public.queued_paths_requests ALTER COLUMN id SET DEFAULT nextval('public.queued_paths_requests_id_seq'::regclass);
ALTER TABLE ONLY public.queued_requests ALTER COLUMN id SET DEFAULT nextval('public.queued_requests_id_seq'::regclass);
ALTER TABLE ONLY public.referers ALTER COLUMN id SET DEFAULT nextval('public.referers_id_seq'::regclass);
ALTER TABLE ONLY public.requests ALTER COLUMN id SET DEFAULT nextval('public.requests_id_seq'::regclass);
ALTER TABLE ONLY public.service_payments ALTER COLUMN id SET DEFAULT nextval('public.service_payments_id_seq'::regclass);
ALTER TABLE ONLY public.service_raw_logs ALTER COLUMN id SET DEFAULT nextval('public.service_raw_logs_id_seq'::regclass);
ALTER TABLE ONLY public.service_ssl ALTER COLUMN id SET DEFAULT nextval('public.service_ssl_id_seq'::regclass);
ALTER TABLE ONLY public.services_new ALTER COLUMN id SET DEFAULT nextval('public.services_new_id_seq'::regclass);
ALTER TABLE ONLY public.ssl_domains ALTER COLUMN id SET DEFAULT nextval('public.ssl_domains_id_seq'::regclass);
ALTER TABLE ONLY public.status ALTER COLUMN id SET DEFAULT nextval('public.status_id_seq'::regclass);
ALTER TABLE ONLY public.storage_pricing ALTER COLUMN id SET DEFAULT nextval('public.storage_pricing_id_seq'::regclass);
ALTER TABLE ONLY public.storage_tariffs ALTER COLUMN id SET DEFAULT nextval('public.storage_tariffs_id_seq'::regclass);
ALTER TABLE ONLY public.stream_origins ALTER COLUMN id SET DEFAULT nextval('public.stream_origins_id_seq'::regclass);
ALTER TABLE ONLY public.tariffs ALTER COLUMN id SET DEFAULT nextval('public.tariffs_id_seq'::regclass);
ALTER TABLE ONLY public.tariffs_log ALTER COLUMN id SET DEFAULT nextval('public.tariffs_log_id_seq'::regclass);
ALTER TABLE ONLY public.xero_checksum ALTER COLUMN id SET DEFAULT nextval('public.xero_checksum_id_seq'::regclass);
ALTER TABLE ONLY public.xero_report ALTER COLUMN id SET DEFAULT nextval('public.xero_report_id_seq'::regclass);
ALTER TABLE ONLY storages.zone_space_stats ALTER COLUMN id SET DEFAULT nextval('storages.zone_space_stats_id_seq'::regclass);
ALTER TABLE ONLY api.access_list
    ADD CONSTRAINT access_list_id PRIMARY KEY (id);
ALTER TABLE ONLY api.application_token
    ADD CONSTRAINT application_token_pkey PRIMARY KEY (id);
ALTER TABLE ONLY api.categories
    ADD CONSTRAINT categories_id PRIMARY KEY (id);
ALTER TABLE ONLY api.credentials_personal_token
    ADD CONSTRAINT credentials_personal_token_id PRIMARY KEY (id);
ALTER TABLE ONLY api.examples
    ADD CONSTRAINT examples_id PRIMARY KEY (id);
ALTER TABLE ONLY api.method_has_parameter
    ADD CONSTRAINT method_has_parameter_id PRIMARY KEY (id);
ALTER TABLE ONLY api.methods_has_return_parameters
    ADD CONSTRAINT methods_has_return_parameters_id PRIMARY KEY (id);
ALTER TABLE ONLY api.methods
    ADD CONSTRAINT methods_id PRIMARY KEY (id);
ALTER TABLE ONLY api.parameters
    ADD CONSTRAINT parameters_id PRIMARY KEY (id);
ALTER TABLE ONLY api.parameters_types
    ADD CONSTRAINT parameters_types_id PRIMARY KEY (id);
ALTER TABLE ONLY api.parameters_valid_values
    ADD CONSTRAINT parameters_valid_values_id PRIMARY KEY (id);
ALTER TABLE ONLY api.request
    ADD CONSTRAINT request_pkey PRIMARY KEY (id);
ALTER TABLE ONLY api.team_member_access_configuration
    ADD CONSTRAINT team_member_access_configuration_pkey PRIMARY KEY (id);
ALTER TABLE ONLY api.version_has_category
    ADD CONSTRAINT version_has_category_id PRIMARY KEY (id);
ALTER TABLE ONLY api.versions
    ADD CONSTRAINT versions_id PRIMARY KEY (id);
ALTER TABLE ONLY data_manipulation.job
    ADD CONSTRAINT job_pkey PRIMARY KEY (id);
ALTER TABLE ONLY data_manipulation.legacy_request_mapping
    ADD CONSTRAINT legacy_request_mapping_pkey PRIMARY KEY (job_id, old_request_id);
ALTER TABLE ONLY datacenters.aflexi_locations_edge_ids
    ADD CONSTRAINT aflexi_locations_edge_ids_edge_id UNIQUE (edge_id);
ALTER TABLE ONLY datacenters.aflexi_locations_edge_ids
    ADD CONSTRAINT aflexi_locations_edge_ids_pkey PRIMARY KEY (id);
ALTER TABLE ONLY datacenters.edges
    ADD CONSTRAINT edges_id PRIMARY KEY (id);
ALTER TABLE ONLY datacenters.aflexi_locations
    ADD CONSTRAINT id PRIMARY KEY (id);
ALTER TABLE ONLY datacenters.locations
    ADD CONSTRAINT locations_city_code_key UNIQUE (city_code);
ALTER TABLE ONLY datacenters.locations
    ADD CONSTRAINT locations_id PRIMARY KEY (id);
ALTER TABLE ONLY datacenters.locations
    ADD CONSTRAINT locations_uuid UNIQUE (uuid);
ALTER TABLE ONLY pricing.ara_queue
    ADD CONSTRAINT ara_queue_id PRIMARY KEY (id);
ALTER TABLE ONLY pricing.custom_plan_billing_periods
    ADD CONSTRAINT custom_plan_billing_periods_pkey PRIMARY KEY (id);
ALTER TABLE ONLY pricing.custom_plans
    ADD CONSTRAINT custom_plans_pkey PRIMARY KEY (id);
ALTER TABLE ONLY pricing.individual_prices
    ADD CONSTRAINT individual_prices_pkey PRIMARY KEY (id);
ALTER TABLE ONLY pricing.plans_locations
    ADD CONSTRAINT plan_locations_id PRIMARY KEY (id);
ALTER TABLE ONLY pricing.plans_cdns
    ADD CONSTRAINT plans_cdns_id PRIMARY KEY (id);
ALTER TABLE ONLY pricing.plans_cdns
    ADD CONSTRAINT plans_cdns_plan_id_cdn_id UNIQUE (plan_id, cdn_id);
ALTER TABLE ONLY pricing.plans
    ADD CONSTRAINT plans_id PRIMARY KEY (id);
ALTER TABLE ONLY pricing.streaming_plans
    ADD CONSTRAINT streaming_plans_account_id_key UNIQUE (account_id);
ALTER TABLE ONLY pricing.streaming_plans
    ADD CONSTRAINT streaming_plans_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.account_big_traffic_change
    ADD CONSTRAINT account_big_traffic_change_id PRIMARY KEY (id);
ALTER TABLE ONLY public.account_category
    ADD CONSTRAINT account_category_id PRIMARY KEY (id);
ALTER TABLE ONLY public.account_cdn_settings
    ADD CONSTRAINT account_cdn_settings_account_id UNIQUE (account_id);
ALTER TABLE ONLY public.account_cdn_settings
    ADD CONSTRAINT account_cdn_settings_id PRIMARY KEY (id);
ALTER TABLE ONLY public.account_comments
    ADD CONSTRAINT account_comments_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.account_flags
    ADD CONSTRAINT account_flags_account_id UNIQUE (account_id);
ALTER TABLE ONLY public.account_flags
    ADD CONSTRAINT account_flags_id PRIMARY KEY (id);
ALTER TABLE ONLY public.account_ga_cids
    ADD CONSTRAINT account_ga_cids_cid_account_id UNIQUE (cid, account_id);
ALTER TABLE ONLY public.account_ga_cids
    ADD CONSTRAINT account_ga_cids_id PRIMARY KEY (id);
ALTER TABLE ONLY public.account_mailing_unsubscribes
    ADD CONSTRAINT account_mailing_unsubscribes_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.account_mails
    ADD CONSTRAINT account_mails_id PRIMARY KEY (id);
ALTER TABLE ONLY public.account_monthly_traffic_plans
    ADD CONSTRAINT account_monthly_traffic_plans_id PRIMARY KEY (id);
ALTER TABLE ONLY public.account_payment_settings
    ADD CONSTRAINT account_payment_settings_account_id UNIQUE (account_id);
ALTER TABLE ONLY public.account_payment_settings
    ADD CONSTRAINT account_payment_settings_id PRIMARY KEY (id);
ALTER TABLE ONLY public.account_settings
    ADD CONSTRAINT account_settings_id PRIMARY KEY (id);
ALTER TABLE ONLY public.account_sign_up_invites
    ADD CONSTRAINT account_sign_up_invites_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.account_traffic
    ADD CONSTRAINT account_traffic_id PRIMARY KEY (id);
ALTER TABLE ONLY public.account_xero_contacts
    ADD CONSTRAINT account_xero_contacts_id PRIMARY KEY (id);
ALTER TABLE ONLY public.accounts
    ADD CONSTRAINT accounts_email_key UNIQUE (email);
ALTER TABLE ONLY public.accounts
    ADD CONSTRAINT accounts_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.actionlogs
    ADD CONSTRAINT actionlogs_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.adwords_costs_daily
    ADD CONSTRAINT adwords_costs_daily_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.adwords_costs_monitor_daily
    ADD CONSTRAINT adwords_costs_monitor_daily_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.alerts
    ADD CONSTRAINT alerts_id PRIMARY KEY (id);
ALTER TABLE ONLY public.alerts
    ADD CONSTRAINT alerts_id_key UNIQUE (id);
ALTER TABLE ONLY public.alerts_users
    ADD CONSTRAINT alerts_users_id PRIMARY KEY (id);
ALTER TABLE ONLY public.alerts_users
    ADD CONSTRAINT alerts_users_id_key UNIQUE (id);
ALTER TABLE ONLY public.application_states
    ADD CONSTRAINT application_states_id PRIMARY KEY (id);
ALTER TABLE ONLY public.ask_colleague_hash
    ADD CONSTRAINT ask_colleague_hash_id PRIMARY KEY (id);
ALTER TABLE ONLY public.blocked_ips
    ADD CONSTRAINT blocked_ips_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.cdn_origin
    ADD CONSTRAINT cdn_aws_origin_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.cdn_cnames
    ADD CONSTRAINT cdn_cnames_id PRIMARY KEY (id);
ALTER TABLE ONLY public.cdn_http
    ADD CONSTRAINT cdn_http_id PRIMARY KEY (id);
ALTER TABLE ONLY public.cdn_http_protection
    ADD CONSTRAINT cdn_http_protection_id PRIMARY KEY (id);
ALTER TABLE ONLY public.cdn
    ADD CONSTRAINT cdn_id PRIMARY KEY (id);
ALTER TABLE ONLY public.cdn_origin_protection
    ADD CONSTRAINT cdn_origin_protection_id PRIMARY KEY (id);
ALTER TABLE ONLY public.cdn
    ADD CONSTRAINT cdn_resource_id UNIQUE (resource_id);
ALTER TABLE ONLY public.cdn_ssl
    ADD CONSTRAINT cdn_ssl_id PRIMARY KEY (id);
ALTER TABLE ONLY public.cdn_stream
    ADD CONSTRAINT cdn_stream_id PRIMARY KEY (id);
ALTER TABLE ONLY public.cdn_traffic
    ADD CONSTRAINT cdn_traffic_id PRIMARY KEY (id);
ALTER TABLE ONLY public.storage_tariffs
    ADD CONSTRAINT charged_storages_logs_id PRIMARY KEY (id);
ALTER TABLE ONLY public.constants
    ADD CONSTRAINT constants_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.countries
    ADD CONSTRAINT countries_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.countries
    ADD CONSTRAINT country_iso_ukey UNIQUE (iso);
ALTER TABLE ONLY public.credit_history
    ADD CONSTRAINT credit_history_id PRIMARY KEY (id);
ALTER TABLE ONLY public.cron_log
    ADD CONSTRAINT cron_log_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.crop_api_errors
    ADD CONSTRAINT crop_api_errors_id PRIMARY KEY (id);
ALTER TABLE ONLY public.email_address_confirmation
    ADD CONSTRAINT customer_sign_up_confirmation_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.events_before
    ADD CONSTRAINT events_before_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.events_history
    ADD CONSTRAINT events_history_id PRIMARY KEY (id);
ALTER TABLE ONLY public.events
    ADD CONSTRAINT events_id PRIMARY KEY (id);
ALTER TABLE ONLY public.exchange_rates
    ADD CONSTRAINT exchange_rates_id PRIMARY KEY (id);
ALTER TABLE ONLY public.forgotten_password
    ADD CONSTRAINT forgotten_password_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.geo_protection_countries
    ADD CONSTRAINT geo_protection_countries_id PRIMARY KEY (id);
ALTER TABLE ONLY public.hlp_referer_domains
    ADD CONSTRAINT hlp_referer_domains_id PRIMARY KEY (id);
ALTER TABLE ONLY public.ignored_query_params
    ADD CONSTRAINT ignored_query_param_cdn_id_parameter UNIQUE (cdn_id, parameter);
ALTER TABLE ONLY public.ignored_query_params
    ADD CONSTRAINT ignored_query_params_id PRIMARY KEY (id);
ALTER TABLE ONLY public.invoice_account_details
    ADD CONSTRAINT invoice_account_details_account_id UNIQUE (account_id);
ALTER TABLE ONLY public.invoice_account_details
    ADD CONSTRAINT invoice_account_details_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.invoice_number_counter
    ADD CONSTRAINT invoice_number_counter_pkey PRIMARY KEY (issue_year, prefix);
ALTER TABLE ONLY public.invoices
    ADD CONSTRAINT invoices_id PRIMARY KEY (id);
ALTER TABLE ONLY public.invoices
    ADD CONSTRAINT invoices_id_key UNIQUE (id);
ALTER TABLE ONLY public.invoices
    ADD CONSTRAINT invoices_payments_general_id_key UNIQUE (payments_general_id);
ALTER TABLE ONLY public.ip_protection_addresses
    ADD CONSTRAINT ip_protection_addresses_id PRIMARY KEY (id);
ALTER TABLE ONLY public.logins_log
    ADD CONSTRAINT logins_log_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.mail_addresses
    ADD CONSTRAINT mail_addresses_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.mail_log_details
    ADD CONSTRAINT mail_log_details_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.mail_templates
    ADD CONSTRAINT mail_templates_id PRIMARY KEY (id);
ALTER TABLE ONLY public.mails
    ADD CONSTRAINT mails_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.migration_versions
    ADD CONSTRAINT migration_versions_pkey PRIMARY KEY (version);
ALTER TABLE ONLY public.migrations_versions
    ADD CONSTRAINT migrations_versions_pkey PRIMARY KEY (version);
ALTER TABLE ONLY public.monthly_plan_group
    ADD CONSTRAINT monthly_plan_group_id PRIMARY KEY (id);
ALTER TABLE ONLY public.monthly_traffic_plans
    ADD CONSTRAINT monthly_traffic_plans_id PRIMARY KEY (id);
ALTER TABLE ONLY public.netbanx_purchases
    ADD CONSTRAINT netbanx_purchases_payments_general_id_key UNIQUE (payments_general_id);
ALTER TABLE ONLY public.netbanx_purchases
    ADD CONSTRAINT netbanx_purchases_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.oauth_tokens
    ADD CONSTRAINT oauth_tokens_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.payment_recipes
    ADD CONSTRAINT payment_recipes_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.payment_recipes
    ADD CONSTRAINT payment_recipes_recipe_id_key UNIQUE (recipe_id);
ALTER TABLE ONLY public.payments_general
    ADD CONSTRAINT payments_general_id_key UNIQUE (id);
ALTER TABLE ONLY public.payments_general
    ADD CONSTRAINT payments_general_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.paypal
    ADD CONSTRAINT paypal_id PRIMARY KEY (id);
ALTER TABLE ONLY public.paypal_payments
    ADD CONSTRAINT paypal_payments_id PRIMARY KEY (id);
ALTER TABLE ONLY public.paypal
    ADD CONSTRAINT paypal_token UNIQUE (token);
ALTER TABLE ONLY public.perfops_asn
    ADD CONSTRAINT perfops_asn_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.perfops_data
    ADD CONSTRAINT perfops_data_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.permanent_logins
    ADD CONSTRAINT permanent_logins_pkey PRIMARY KEY (account_id, token);
ALTER TABLE ONLY public.publish_point_internal
    ADD CONSTRAINT pk_publish_point_internal_id PRIMARY KEY (id);
ALTER TABLE ONLY public.predefined_contact_mails
    ADD CONSTRAINT predefined_contact_mails_id PRIMARY KEY (id);
ALTER TABLE ONLY public.promo_codes
    ADD CONSTRAINT promo_codes_code_account_id UNIQUE (code, account_id);
ALTER TABLE ONLY public.promo_codes
    ADD CONSTRAINT promo_codes_id PRIMARY KEY (id);
ALTER TABLE ONLY public.promo_codes_usages
    ADD CONSTRAINT promo_codes_usages_id PRIMARY KEY (id);
ALTER TABLE ONLY public.queue_calls
    ADD CONSTRAINT queue_calls_id PRIMARY KEY (id);
ALTER TABLE ONLY public.queued_paths
    ADD CONSTRAINT queued_paths_2_id PRIMARY KEY (id);
ALTER TABLE ONLY public.queued_paths_requests
    ADD CONSTRAINT queued_paths_requests_2_id PRIMARY KEY (id);
ALTER TABLE ONLY public.queued_requests
    ADD CONSTRAINT queued_requests_2_id PRIMARY KEY (id);
ALTER TABLE ONLY public.referers
    ADD CONSTRAINT referers_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.requests
    ADD CONSTRAINT requests2_id PRIMARY KEY (id);
ALTER TABLE ONLY public.responses
    ADD CONSTRAINT responses2_id PRIMARY KEY (id);
ALTER TABLE ONLY public.service_payments
    ADD CONSTRAINT service_payments_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.service_raw_logs
    ADD CONSTRAINT service_raw_logs_id PRIMARY KEY (id);
ALTER TABLE ONLY public.service_ssl
    ADD CONSTRAINT service_ssl_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.services_new
    ADD CONSTRAINT services_new_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.ssl_domains
    ADD CONSTRAINT ssl_domains_id PRIMARY KEY (id);
ALTER TABLE ONLY public.ssl_domains
    ADD CONSTRAINT ssl_domains_service_ssl_id_domain UNIQUE (service_ssl_id, domain);
ALTER TABLE ONLY public.status
    ADD CONSTRAINT status_id PRIMARY KEY (id);
ALTER TABLE ONLY public.storage_pricing
    ADD CONSTRAINT storage_pricing_id PRIMARY KEY (id);
ALTER TABLE ONLY public.stream_origins
    ADD CONSTRAINT stream_origins_id PRIMARY KEY (id);
ALTER TABLE ONLY public.tariffs_log
    ADD CONSTRAINT tariff_substract_id PRIMARY KEY (id);
ALTER TABLE ONLY public.tariffs
    ADD CONSTRAINT tariffs_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.tik_tok_datacenter_traffic
    ADD CONSTRAINT tik_tok_datacenter_traffic_datacenter_id_day_is_dsa_key UNIQUE (datacenter_id, day, is_dsa);
ALTER TABLE ONLY public.tik_tok_datacenter_traffic
    ADD CONSTRAINT tik_tok_datacenter_traffic_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.tik_tok_region_locations
    ADD CONSTRAINT tik_tok_region_locations_location_id_region_id_key UNIQUE (location_id, region_id);
ALTER TABLE ONLY public.tik_tok_region_locations
    ADD CONSTRAINT tik_tok_region_locations_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.tik_tok_regions
    ADD CONSTRAINT tik_tok_regions_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.tiktok_country_traffic
    ADD CONSTRAINT tiktok_country_traffic_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.xero_checksum
    ADD CONSTRAINT xero_checksum_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.xero_report
    ADD CONSTRAINT xero_report_pkey PRIMARY KEY (id);
ALTER TABLE ONLY storages.server
    ADD CONSTRAINT server_zone_space_stats_pkey PRIMARY KEY (id);
ALTER TABLE ONLY storages.zone
    ADD CONSTRAINT zone_connect_user_key UNIQUE (connect_user);
ALTER TABLE ONLY storages.zone
    ADD CONSTRAINT zone_pkey PRIMARY KEY (id);
ALTER TABLE ONLY storages.zone_space_stats
    ADD CONSTRAINT zone_space_stats_pkey1 PRIMARY KEY (id);
CREATE INDEX access_list_account_id ON api.access_list USING btree (account_id);
CREATE INDEX access_list_category_id ON api.access_list USING btree (category_id);
CREATE INDEX access_list_method_id ON api.access_list USING btree (method_id);
CREATE INDEX credentials_personal_token_account_id ON api.credentials_personal_token USING btree (account_id);
CREATE INDEX examples_method_id ON api.examples USING btree (method_id);
CREATE INDEX idx_api_request_endpoint ON api.request USING btree (endpoint);
CREATE INDEX idx_api_request_started_at ON api.request USING btree (started_at);
CREATE UNIQUE INDEX idx_credentials_personal_token_uuid ON api.credentials_personal_token USING btree (uuid);
CREATE INDEX idx_team_member_access_configuration_customer_id ON api.team_member_access_configuration USING btree (customer_id);
CREATE INDEX method_has_parameter_method_id ON api.method_has_parameter USING btree (method_id);
CREATE INDEX method_has_parameter_parameter_id ON api.method_has_parameter USING btree (parameter_id);
CREATE INDEX methods_category_id ON api.methods USING btree (category_id);
CREATE INDEX methods_has_return_parameters_methods_id ON api.methods_has_return_parameters USING btree (methods_id);
CREATE INDEX methods_has_return_parameters_parameters_id ON api.methods_has_return_parameters USING btree (parameters_id);
CREATE INDEX parameters_parameter_type_id ON api.parameters USING btree (parameter_type_id);
CREATE INDEX parameters_valid_values_parameter_id ON api.parameters_valid_values USING btree (parameter_id);
CREATE INDEX request_account_id_idx ON api.request USING btree (account_id);
CREATE INDEX job_cdn_id_idx ON data_manipulation.job USING btree (cdn_id);
CREATE INDEX job_completed_at_idx ON data_manipulation.job USING btree (completed_at);
CREATE INDEX job_resource_id_idx ON data_manipulation.job USING btree (resource_id);
CREATE INDEX job_scheduled_at_idx ON data_manipulation.job USING btree (scheduled_at);
CREATE INDEX idx_custom_plan_billing_periods_customer_id ON pricing.custom_plan_billing_periods USING btree (customer_id);
CREATE INDEX idx_custom_plan_billing_periods_customer_legacy_id ON pricing.custom_plan_billing_periods USING btree (customer_legacy_id);
CREATE INDEX idx_custom_plans_custom_plan_billing_period_id ON pricing.custom_plans USING btree (custom_plan_billing_period_id);
CREATE INDEX individual_prices_idx ON pricing.individual_prices USING btree (account_id);
CREATE INDEX individual_prices_idx2 ON pricing.individual_prices USING btree (time_from);
CREATE INDEX individual_prices_idx3 ON pricing.individual_prices USING btree (time_to);
CREATE INDEX plan_locations_fk_plans_id ON pricing.plans_locations USING btree (fk_plans_id);
CREATE INDEX plans_account_id ON pricing.plans USING btree (account_id);
CREATE INDEX plans_cdns_plan_id ON pricing.plans_cdns USING btree (plan_id);
CREATE INDEX account_ga_cids_account_id ON public.account_ga_cids USING btree (account_id);
CREATE INDEX account_monthly_traffic_plans_account_id ON public.account_monthly_traffic_plans USING btree (account_id);
CREATE INDEX account_monthly_traffic_plans_monthly_traffic_plan_id ON public.account_monthly_traffic_plans USING btree (monthly_traffic_plan_id);
CREATE INDEX account_settings_account_id ON public.account_settings USING btree (account_id);
CREATE INDEX account_traffic_account_id ON public.account_traffic USING btree (account_id);
CREATE INDEX account_xero_contacts_account_id ON public.account_xero_contacts USING btree (account_id);
CREATE INDEX cdn_account_id ON public.cdn USING btree (account_id);
CREATE INDEX cdn_cnames_cdn_id ON public.cdn_cnames USING btree (cdn_id);
CREATE INDEX cdn_cnames_cname ON public.cdn_cnames USING btree (cname);
CREATE INDEX cdn_cnames_resource_id ON public.cdn_cnames USING btree (resource_id);
CREATE INDEX cdn_http_cdn_id ON public.cdn_http USING btree (cdn_id);
CREATE INDEX cdn_http_origin_id ON public.cdn_http USING btree (origin_id);
CREATE INDEX cdn_http_protection_cdn_id ON public.cdn_http_protection USING btree (cdn_id);
CREATE INDEX cdn_http_storage_secret ON public.cdn_http USING btree (push_zone_id);
CREATE INDEX cdn_origin_customer_id ON public.cdn_origin USING btree (customer_id);
CREATE INDEX cdn_origin_protection_cdn_id ON public.cdn_origin_protection USING btree (cdn_id);
CREATE INDEX cdn_ssl_cdn_id ON public.cdn_ssl USING btree (cdn_id);
CREATE INDEX cdn_ssl_service_ssl_id ON public.cdn_ssl USING btree (service_ssl_id);
CREATE INDEX cdn_traffic_cdn_id ON public.cdn_traffic USING btree (cdn_id);
CREATE INDEX charged_storages_logs_valid_to ON public.storage_tariffs USING btree (valid_to);
CREATE INDEX credit_history_account_id ON public.credit_history USING btree (account_id);
CREATE INDEX cron_log_subject ON public.cron_log USING btree (subject);
CREATE UNIQUE INDEX customer_new_id ON public.accounts USING btree (new_id);
CREATE INDEX event_time_index ON public.events USING btree (event_time);
CREATE INDEX events_account_id ON public.events USING btree (account_id);
CREATE INDEX geo_protection_countries_cdn_http_protection_id ON public.geo_protection_countries USING btree (cdn_http_protection_id);
CREATE INDEX hlp_referer_domains_cdn_id ON public.hlp_referer_domains USING btree (cdn_id);
CREATE INDEX i_action ON public.actionlogs USING btree (action);
CREATE INDEX i_created ON public.exchange_rates USING btree (created);
CREATE INDEX i_id ON public.events_history USING btree (id);
CREATE INDEX i_nbp_account_id ON public.netbanx_purchases USING btree (account_id);
CREATE INDEX i_nbp_decision ON public.netbanx_purchases USING btree (resp_decision);
CREATE INDEX i_object ON public.actionlogs USING btree (object);
CREATE INDEX i_object_id ON public.actionlogs USING btree (object_id);
CREATE INDEX i_tariffs_account_id ON public.tariffs USING btree (account_id);
CREATE INDEX idx_accounts_parent_id ON public.accounts USING btree (parent_id);
CREATE UNIQUE INDEX idx_accounts_public_id ON public.accounts USING btree (public_id);
CREATE INDEX idx_cdn_cdn_id ON public.service_raw_logs USING btree (cdn_id);
CREATE INDEX idx_cdn_stream_cdn_id ON public.cdn_stream USING btree (cdn_id);
CREATE INDEX idx_crop_api_errors_account_id ON public.crop_api_errors USING btree (account_id);
CREATE INDEX idx_forgotten_password_email ON public.forgotten_password USING btree (email);
CREATE INDEX idx_invoices_account_id ON public.invoices USING btree (account_id);
CREATE INDEX idx_service_raw_logs_service_id ON public.service_raw_logs USING btree (service_id);
CREATE INDEX idx_service_ssl_uuid ON public.service_ssl USING btree (uuid);
CREATE INDEX idx_services_new_account_id ON public.services_new USING btree (account_id);
CREATE UNIQUE INDEX idx_unique_invoices_number ON public.invoices USING btree (number);
CREATE INDEX index_account_id ON public.events_history USING btree (account_id);
CREATE INDEX ip_protection_addresses_cdn_http_protection_id ON public.ip_protection_addresses USING btree (cdn_http_protection_id);
CREATE INDEX logins_log_account_id ON public.logins_log USING btree (account_id);
CREATE INDEX mail_log_details_account_id ON public.mail_log_details USING btree (account_id);
CREATE INDEX payments_general_account_id ON public.payments_general USING btree (account_id);
CREATE INDEX perfops_data_timestamp ON public.perfops_data USING btree ("timestamp");
CREATE INDEX publish_point_internal_id ON public.publish_point_internal USING btree (id);
CREATE INDEX queued_paths_2_finished ON public.queued_paths USING btree (finished);
CREATE INDEX queued_paths_2_queued_requests_id ON public.queued_paths USING btree (queued_requests_id);
CREATE INDEX queued_paths_requests_2_queued_paths_id ON public.queued_paths_requests USING btree (queued_paths_id);
CREATE INDEX queued_paths_requests_2_requests_id ON public.queued_paths_requests USING btree (requests_id);
CREATE INDEX queued_requests_2_account_id ON public.queued_requests USING btree (account_id);
CREATE INDEX queued_requests_2_cdn_id ON public.queued_requests USING btree (cdn_id);
CREATE INDEX queued_requests_2_waiting_for ON public.queued_requests USING btree (waiting_for);
CREATE INDEX requests2_account_id ON public.requests USING btree (account_id);
CREATE INDEX requests2_events_id ON public.requests USING btree (events_id);
CREATE INDEX requests2_ts ON public.requests USING btree (ts);
CREATE INDEX responses2_account_id ON public.responses USING btree (account_id);
CREATE INDEX responses_created ON public.responses USING btree (created);
CREATE INDEX tariff_substract_tariffs_id ON public.tariffs_log USING btree (tariffs_id);
CREATE UNIQUE INDEX uniq_account_traffic_account_id ON public.account_traffic USING btree (account_id);
CREATE INDEX zone_server ON storages.zone USING btree (server);
CREATE INDEX zone_space_stats_idx2 ON storages.zone_space_stats USING btree ("timestamp" DESC, zone_id);
CREATE INDEX zone_space_stats_zone_idx ON storages.zone_space_stats USING btree (zone_id, "timestamp" DESC);
CREATE INDEX zone_user_idx ON storages.zone USING btree (user_id);
ALTER TABLE ONLY api.access_list
    ADD CONSTRAINT access_list_category_id_fkey FOREIGN KEY (category_id) REFERENCES api.categories(id);
ALTER TABLE ONLY api.access_list
    ADD CONSTRAINT access_list_method_id_fkey FOREIGN KEY (method_id) REFERENCES api.methods(id);
ALTER TABLE ONLY api.examples
    ADD CONSTRAINT examples_method_id_fkey FOREIGN KEY (method_id) REFERENCES api.methods(id);
ALTER TABLE ONLY api.credentials_personal_token
    ADD CONSTRAINT fk_credentials_personal_token_accounts FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON DELETE CASCADE DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE ONLY api.method_has_parameter
    ADD CONSTRAINT method_has_parameter_method_id_fkey FOREIGN KEY (method_id) REFERENCES api.methods(id);
ALTER TABLE ONLY api.method_has_parameter
    ADD CONSTRAINT method_has_parameter_parameter_id_fkey FOREIGN KEY (parameter_id) REFERENCES api.parameters(id);
ALTER TABLE ONLY api.methods_has_return_parameters
    ADD CONSTRAINT method_has_return_parameter_methods_id_fkey FOREIGN KEY (methods_id) REFERENCES api.methods(id);
ALTER TABLE ONLY api.methods_has_return_parameters
    ADD CONSTRAINT method_has_return_parameter_parameters_id_fkey FOREIGN KEY (parameters_id) REFERENCES api.parameters(id);
ALTER TABLE ONLY api.methods
    ADD CONSTRAINT methods_category_id_fkey FOREIGN KEY (category_id) REFERENCES api.categories(id);
ALTER TABLE ONLY api.parameters
    ADD CONSTRAINT parameters_parameter_type_id_fkey FOREIGN KEY (parameter_type_id) REFERENCES api.parameters_types(id);
ALTER TABLE ONLY api.parameters_valid_values
    ADD CONSTRAINT parameters_valid_values_parameter_id_fkey FOREIGN KEY (parameter_id) REFERENCES api.parameters(id);
ALTER TABLE ONLY api.request
    ADD CONSTRAINT request_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON DELETE CASCADE;
ALTER TABLE ONLY api.team_member_access_configuration
    ADD CONSTRAINT team_member_access_configuration_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.accounts(new_id) DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE ONLY api.version_has_category
    ADD CONSTRAINT version_has_category_category_id_fkey FOREIGN KEY (category_id) REFERENCES api.categories(id);
ALTER TABLE ONLY api.version_has_category
    ADD CONSTRAINT version_has_category_version_id_fkey FOREIGN KEY (version_id) REFERENCES api.versions(id);
ALTER TABLE ONLY data_manipulation.job
    ADD CONSTRAINT job_cdn_id_fkey FOREIGN KEY (cdn_id) REFERENCES public.cdn(id);
ALTER TABLE ONLY data_manipulation.job
    ADD CONSTRAINT job_resource_id_fkey FOREIGN KEY (resource_id) REFERENCES public.cdn(resource_id) DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE ONLY data_manipulation.legacy_request_mapping
    ADD CONSTRAINT legacy_request_mapping_job_id_fkey FOREIGN KEY (job_id) REFERENCES data_manipulation.job(id) ON DELETE CASCADE;
ALTER TABLE ONLY data_manipulation.legacy_request_mapping
    ADD CONSTRAINT legacy_request_mapping_old_request_id_fkey FOREIGN KEY (old_request_id) REFERENCES public.queued_requests(id) ON DELETE CASCADE;
ALTER TABLE ONLY datacenters.locations
    ADD CONSTRAINT datacenters_location_country_fkey FOREIGN KEY (country) REFERENCES public.countries(iso);
ALTER TABLE ONLY datacenters.edges
    ADD CONSTRAINT edges_location_id_fkey FOREIGN KEY (location_id) REFERENCES datacenters.locations(id);
ALTER TABLE ONLY datacenters.locations
    ADD CONSTRAINT locations_country_id_fkey FOREIGN KEY (country_id) REFERENCES public.countries(id);
ALTER TABLE ONLY pricing.ara_queue
    ADD CONSTRAINT ara_queue_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON DELETE CASCADE;
ALTER TABLE ONLY pricing.custom_plan_billing_periods
    ADD CONSTRAINT custom_plan_billing_periods_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.accounts(new_id) ON DELETE CASCADE;
ALTER TABLE ONLY pricing.custom_plan_billing_periods
    ADD CONSTRAINT custom_plan_billing_periods_customer_legacy_id_fkey FOREIGN KEY (customer_legacy_id) REFERENCES public.accounts(id) ON DELETE CASCADE;
ALTER TABLE ONLY pricing.custom_plan_billing_periods
    ADD CONSTRAINT custom_plan_billing_periods_plan_id_fkey FOREIGN KEY (plan_id) REFERENCES pricing.plans(id);
ALTER TABLE ONLY pricing.custom_plans
    ADD CONSTRAINT custom_plans_custom_plan_billing_period_id_fkey FOREIGN KEY (custom_plan_billing_period_id) REFERENCES pricing.custom_plan_billing_periods(id) ON DELETE CASCADE DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE ONLY pricing.custom_plans
    ADD CONSTRAINT custom_plans_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.accounts(new_id) ON DELETE CASCADE;
ALTER TABLE ONLY pricing.custom_plans
    ADD CONSTRAINT custom_plans_customer_legacy_id_fkey FOREIGN KEY (customer_legacy_id) REFERENCES public.accounts(id) ON DELETE CASCADE;
ALTER TABLE ONLY pricing.individual_prices
    ADD CONSTRAINT individual_prices_account_id_editor_fkey FOREIGN KEY (account_id_editor) REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE RESTRICT;
ALTER TABLE ONLY pricing.individual_prices
    ADD CONSTRAINT individual_prices_location_id_fkey FOREIGN KEY (location_id) REFERENCES datacenters.locations(id) ON UPDATE RESTRICT ON DELETE RESTRICT;
ALTER TABLE ONLY pricing.individual_prices
    ADD CONSTRAINT individual_prices_locations_uuid_fkey FOREIGN KEY (location_uuid) REFERENCES datacenters.locations(uuid);
ALTER TABLE ONLY pricing.plans_cdns
    ADD CONSTRAINT plans_cdns_cdn_id_fkey FOREIGN KEY (cdn_id) REFERENCES public.cdn(id) ON UPDATE CASCADE ON DELETE CASCADE;
ALTER TABLE ONLY pricing.plans_cdns
    ADD CONSTRAINT plans_cdns_plan_id_fkey FOREIGN KEY (plan_id) REFERENCES pricing.plans(id) ON DELETE CASCADE;
ALTER TABLE ONLY pricing.plans_locations
    ADD CONSTRAINT plans_locations_fk_plans_id_fkey FOREIGN KEY (fk_plans_id) REFERENCES pricing.plans(id) ON DELETE CASCADE;
ALTER TABLE ONLY pricing.streaming_plans
    ADD CONSTRAINT streaming_plans_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id);
ALTER TABLE ONLY public.account_category
    ADD CONSTRAINT account_category_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON DELETE CASCADE;
ALTER TABLE ONLY public.account_cdn_settings
    ADD CONSTRAINT account_cdn_settings_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON DELETE CASCADE;
ALTER TABLE ONLY public.account_comments
    ADD CONSTRAINT account_comments_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON UPDATE CASCADE;
ALTER TABLE ONLY public.account_flags
    ADD CONSTRAINT account_flags_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE CASCADE DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE ONLY public.account_ga_cids
    ADD CONSTRAINT account_ga_cids_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON DELETE CASCADE;
ALTER TABLE ONLY public.account_mailing_unsubscribes
    ADD CONSTRAINT account_mailing_unsubscribes_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(new_id) ON DELETE CASCADE;
ALTER TABLE ONLY public.account_monthly_traffic_plans
    ADD CONSTRAINT account_monthly_traffic_plans_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON DELETE CASCADE;
ALTER TABLE ONLY public.account_monthly_traffic_plans
    ADD CONSTRAINT account_monthly_traffic_plans_monthly_traffic_plan_id_fkey FOREIGN KEY (monthly_traffic_plan_id) REFERENCES public.monthly_traffic_plans(id) ON DELETE CASCADE;
ALTER TABLE ONLY public.account_payment_settings
    ADD CONSTRAINT account_payment_settings_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON DELETE CASCADE DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE ONLY public.account_payment_settings
    ADD CONSTRAINT account_payment_settings_monthly_plan_group_fkey FOREIGN KEY (monthly_plan_group) REFERENCES public.monthly_plan_group(id) ON DELETE CASCADE;
ALTER TABLE ONLY public.account_settings
    ADD CONSTRAINT account_settings_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE CASCADE DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE ONLY public.account_sign_up_invites
    ADD CONSTRAINT account_sign_up_invites_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.accounts(new_id) ON DELETE CASCADE DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE ONLY public.account_traffic
    ADD CONSTRAINT account_traffic_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON DELETE CASCADE;
ALTER TABLE ONLY public.account_xero_contacts
    ADD CONSTRAINT account_xero_contacts_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON DELETE CASCADE;
ALTER TABLE ONLY public.accounts
    ADD CONSTRAINT accounts_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES public.accounts(id);
ALTER TABLE ONLY public.alerts_users
    ADD CONSTRAINT alerts_users_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id);
ALTER TABLE ONLY public.alerts_users
    ADD CONSTRAINT alerts_users_alerts_id_fkey FOREIGN KEY (alerts_id) REFERENCES public.alerts(id);
ALTER TABLE ONLY public.ask_colleague_hash
    ADD CONSTRAINT ask_colleague_hash_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON UPDATE CASCADE;
ALTER TABLE ONLY public.cdn
    ADD CONSTRAINT cdn_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON UPDATE CASCADE;
ALTER TABLE ONLY public.cdn_cnames
    ADD CONSTRAINT cdn_cnames_cdn_id_fkey FOREIGN KEY (cdn_id) REFERENCES public.cdn(id) ON DELETE CASCADE DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE ONLY public.cdn_cnames
    ADD CONSTRAINT cdn_cnames_resource_id_fkey FOREIGN KEY (resource_id) REFERENCES public.cdn(resource_id) DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE ONLY public.cdn_http
    ADD CONSTRAINT cdn_http_cdn_id_fkey FOREIGN KEY (cdn_id) REFERENCES public.cdn(id) ON DELETE CASCADE DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE ONLY public.cdn_http
    ADD CONSTRAINT cdn_http_origin_id_fkey FOREIGN KEY (origin_id) REFERENCES public.cdn_origin(id) DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE ONLY public.cdn_http_protection
    ADD CONSTRAINT cdn_http_protection_cdn_id_fkey FOREIGN KEY (cdn_id) REFERENCES public.cdn(id) ON DELETE CASCADE DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE ONLY public.cdn_origin
    ADD CONSTRAINT cdn_origin_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.accounts(new_id) ON DELETE CASCADE DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE ONLY public.cdn_origin_protection
    ADD CONSTRAINT cdn_origin_protection_cdn_id_fkey FOREIGN KEY (cdn_id) REFERENCES public.cdn(id) ON DELETE CASCADE DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE ONLY public.cdn_ssl
    ADD CONSTRAINT cdn_ssl_cdn_id_fkey FOREIGN KEY (cdn_id) REFERENCES public.cdn(id);
ALTER TABLE ONLY public.cdn_ssl
    ADD CONSTRAINT cdn_ssl_service_ssl_id_fkey FOREIGN KEY (service_ssl_id) REFERENCES public.service_ssl(id) ON UPDATE CASCADE ON DELETE CASCADE;
ALTER TABLE ONLY public.cdn_stream
    ADD CONSTRAINT cdn_stream_cdn_id_fkey FOREIGN KEY (cdn_id) REFERENCES public.cdn(id);
ALTER TABLE ONLY public.cdn_stream
    ADD CONSTRAINT cdn_stream_origin_id_fkey FOREIGN KEY (origin_id) REFERENCES public.stream_origins(id);
ALTER TABLE ONLY public.storage_tariffs
    ADD CONSTRAINT charged_storages_logs_tariff_id_fkey FOREIGN KEY (tariff_id) REFERENCES public.storage_pricing(id) ON DELETE SET NULL;
ALTER TABLE ONLY public.credit_history
    ADD CONSTRAINT credit_history_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON UPDATE CASCADE;
ALTER TABLE ONLY public.crop_api_errors
    ADD CONSTRAINT crop_api_errors_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id);
ALTER TABLE ONLY public.email_address_confirmation
    ADD CONSTRAINT customer_sign_up_confirmation_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.accounts(new_id) ON DELETE CASCADE DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE ONLY public.geo_protection_countries
    ADD CONSTRAINT geo_protection_countries_cdn_http_protection_id_fkey FOREIGN KEY (cdn_http_protection_id) REFERENCES public.cdn_http_protection(id) ON DELETE CASCADE DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE ONLY public.hlp_referer_domains
    ADD CONSTRAINT hlp_referer_domains_cdn_id_fkey FOREIGN KEY (cdn_id) REFERENCES public.cdn(id) DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE ONLY public.ignored_query_params
    ADD CONSTRAINT ignored_query_param_cdn_id_fkey FOREIGN KEY (cdn_id) REFERENCES public.cdn(id) ON DELETE CASCADE;
ALTER TABLE ONLY public.invoice_account_details
    ADD CONSTRAINT invoice_account_details_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE CASCADE DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE ONLY public.invoice_account_details
    ADD CONSTRAINT invoice_account_details_fk_countries_fkey FOREIGN KEY (fk_countries) REFERENCES public.countries(id) ON DELETE RESTRICT;
ALTER TABLE ONLY public.invoices
    ADD CONSTRAINT invoices_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON UPDATE CASCADE;
ALTER TABLE ONLY public.invoices
    ADD CONSTRAINT invoices_payments_general_id_fkey FOREIGN KEY (payments_general_id) REFERENCES public.payments_general(id);
ALTER TABLE ONLY public.ip_protection_addresses
    ADD CONSTRAINT ip_protection_addresses_cdn_http_protection_id_fkey FOREIGN KEY (cdn_http_protection_id) REFERENCES public.cdn_http_protection(id) ON DELETE CASCADE DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE ONLY public.logins_log
    ADD CONSTRAINT logins_log_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE CASCADE;
ALTER TABLE ONLY public.mail_log_details
    ADD CONSTRAINT mail_log_details_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE CASCADE;
ALTER TABLE ONLY public.mails
    ADD CONSTRAINT mails_fk_mail_templates_fkey FOREIGN KEY (fk_mail_templates) REFERENCES public.mail_templates(id);
ALTER TABLE ONLY public.monthly_traffic_plans
    ADD CONSTRAINT monthly_traffic_plans_group_id_fkey FOREIGN KEY (group_id) REFERENCES public.monthly_plan_group(id) ON DELETE CASCADE;
ALTER TABLE ONLY public.netbanx_purchases
    ADD CONSTRAINT netbanx_purchases_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON UPDATE CASCADE;
ALTER TABLE ONLY public.netbanx_purchases
    ADD CONSTRAINT netbanx_purchases_payments_general_id_fkey FOREIGN KEY (payments_general_id) REFERENCES public.payments_general(id);
ALTER TABLE ONLY public.payment_recipes
    ADD CONSTRAINT payment_recipes_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON DELETE CASCADE;
ALTER TABLE ONLY public.payments_general
    ADD CONSTRAINT payments_general_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON UPDATE CASCADE;
ALTER TABLE ONLY public.payments_general
    ADD CONSTRAINT payments_general_who_charged_fkey FOREIGN KEY (who_charged) REFERENCES public.accounts(id) ON UPDATE CASCADE;
ALTER TABLE ONLY public.paypal
    ADD CONSTRAINT paypal_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON UPDATE CASCADE;
ALTER TABLE ONLY public.permanent_logins
    ADD CONSTRAINT permanent_logins_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON UPDATE CASCADE ON DELETE CASCADE;
ALTER TABLE ONLY public.promo_codes
    ADD CONSTRAINT promo_codes_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON DELETE CASCADE;
ALTER TABLE ONLY public.promo_codes_usages
    ADD CONSTRAINT promo_codes_usages_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON DELETE CASCADE;
ALTER TABLE ONLY public.promo_codes_usages
    ADD CONSTRAINT promo_codes_usages_promo_code_id_fkey FOREIGN KEY (promo_code_id) REFERENCES public.promo_codes(id) ON DELETE CASCADE;
ALTER TABLE ONLY public.queued_requests
    ADD CONSTRAINT queued_requests_2_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id);
ALTER TABLE ONLY public.queued_requests
    ADD CONSTRAINT queued_requests_2_cdn_id_fkey FOREIGN KEY (cdn_id) REFERENCES public.cdn(id) ON DELETE CASCADE;
ALTER TABLE ONLY public.service_raw_logs
    ADD CONSTRAINT service_raw_logs_cdn_id_fkey FOREIGN KEY (cdn_id) REFERENCES public.cdn(id);
ALTER TABLE ONLY public.service_raw_logs
    ADD CONSTRAINT service_raw_logs_service_id_fkey FOREIGN KEY (service_id) REFERENCES public.services_new(id) ON DELETE CASCADE;
ALTER TABLE ONLY public.service_ssl
    ADD CONSTRAINT service_ssl_service_id_fkey FOREIGN KEY (service_id) REFERENCES public.services_new(id) ON UPDATE CASCADE ON DELETE CASCADE;
ALTER TABLE ONLY public.services_new
    ADD CONSTRAINT services_new_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON DELETE CASCADE;
ALTER TABLE ONLY public.ssl_domains
    ADD CONSTRAINT ssl_domains_service_ssl_id_fkey FOREIGN KEY (service_ssl_id) REFERENCES public.service_ssl(id) ON UPDATE CASCADE ON DELETE CASCADE;
ALTER TABLE ONLY public.storage_tariffs
    ADD CONSTRAINT storage_tariffs_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id);
ALTER TABLE ONLY public.tariffs
    ADD CONSTRAINT tariffs_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON UPDATE CASCADE;
ALTER TABLE ONLY public.tariffs_log
    ADD CONSTRAINT tariffs_log_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON UPDATE CASCADE;
ALTER TABLE ONLY public.tariffs_log
    ADD CONSTRAINT tariffs_log_who_charged_account_id_fkey FOREIGN KEY (who_charged_account_id) REFERENCES public.accounts(id) ON UPDATE CASCADE;
ALTER TABLE ONLY public.tik_tok_datacenter_traffic
    ADD CONSTRAINT tik_tok_datacenter_traffic_datacenter_id_fkey FOREIGN KEY (datacenter_id) REFERENCES datacenters.locations(uuid);
ALTER TABLE ONLY public.tik_tok_region_locations
    ADD CONSTRAINT tik_tok_region_locations_location_id_fkey FOREIGN KEY (location_id) REFERENCES datacenters.locations(uuid) ON DELETE CASCADE;
ALTER TABLE ONLY public.tik_tok_region_locations
    ADD CONSTRAINT tik_tok_region_locations_region_id_fkey FOREIGN KEY (region_id) REFERENCES public.tik_tok_regions(id) ON DELETE CASCADE;
ALTER TABLE ONLY public.tik_tok_regions
    ADD CONSTRAINT tik_tok_regions_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.accounts(new_id) ON DELETE CASCADE DEFERRABLE INITIALLY DEFERRED;
ALTER TABLE ONLY storages.zone
    ADD CONSTRAINT zone_server_fkey FOREIGN KEY (server) REFERENCES storages.server(id);
CREATE PUBLICATION cdn77_pub FOR ALL TABLES WITH (publish = 'insert, update, delete');
