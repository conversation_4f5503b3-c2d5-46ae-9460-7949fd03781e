<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

use function Safe\file_get_contents;
use function Safe\preg_split;
use function str_starts_with;
use function trim;

use const PREG_SPLIT_NO_EMPTY;

final class Version20220420150600 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $dump = file_get_contents(__DIR__ . '/schema.sql');
        $statements = preg_split('~;$\n~m', $dump, -1, PREG_SPLIT_NO_EMPTY);

        /** @var string $statement */
        foreach ($statements as $statement) {
            $statement = trim($statement);

            if ($statement === '' || str_starts_with($statement, '--')) {
                continue;
            }

            $this->addSql($statement);
        }
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException();
    }
}
