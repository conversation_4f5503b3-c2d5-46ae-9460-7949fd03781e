<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20221125121500 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
ALTER TABLE public.payments_general
ADD COLUMN uuid uuid NOT NULL DEFAULT uuid_generate_v4()
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
CREATE INDEX idx_payments_general_uuid ON public.payments_general (uuid)
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException();
    }
}
