<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230419143000 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
CREATE TABLE storages.rgw_stats (
    id uuid PRIMARY KEY,
    origin_id uuid REFERENCES public.cdn_origin(id),
    collected_at timestamp,
    collection_period timestamp,
    size_bytes bigint,
    object_count bigint
)
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
CREATE VIEW storages.rgw_stats_current AS
SELECT DISTINCT ON (origin_id) id, origin_id, collected_at, size_bytes, object_count
FROM storages.rgw_stats
ORDER BY origin_id, collected_at DESC;
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException();
    }
}
