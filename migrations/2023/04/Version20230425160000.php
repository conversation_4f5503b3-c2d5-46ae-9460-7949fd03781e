<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230425160000 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
ALTER TABLE storages.rgw_stats
ALTER COLUMN origin_id SET NOT NULL,
ALTER COLUMN collected_at SET NOT NULL,
ALTER COLUMN collection_period SET NOT NULL,
ALTER COLUMN size_bytes SET NOT NULL,
ALTER COLUMN object_count SET NOT NULL;
PSQL,
        );
    }
}
