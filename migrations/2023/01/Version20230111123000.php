<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230111123000 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
ALTER TABLE account_cdn_settings
ADD COLUMN max_cdns INTEGER NOT NULL DEFAULT 50,
ADD COLUMN max_deleted_cdns INTEGER NOT NULL DEFAULT 50,
ADD COLUMN min_data_centers INTEGER NOT NULL DEFAULT 4,
ADD COLUMN max_iqp INTEGER NOT NULL DEFAULT 5,
ADD COLUMN max_iqp_length INTEGER NOT NULL DEFAULT 20,
ADD COLUMN max_hlp_domains INTEGER NOT NULL DEFAULT 20,
ADD COLUMN max_gp_countries INTEGER NOT NULL DEFAULT 100,
ADD COLUMN max_ipp_addresses INTEGER NOT NULL DEFAULT 100,
ADD COLUMN max_cnames INTEGER NOT NULL DEFAULT 10,
ADD COLUMN purge_all_disabled BOOLEAN NOT NULL DEFAULT FALSE,
ADD COLUMN data_centers_edit_enabled BOOLEAN NOT NULL DEFAULT TRUE
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
ALTER TABLE account_cdn_settings
ALTER CONSTRAINT account_cdn_settings_account_id_fkey INITIALLY DEFERRED
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException();
    }
}
