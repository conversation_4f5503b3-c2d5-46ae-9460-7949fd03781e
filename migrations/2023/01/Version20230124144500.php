<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230124144500 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
ALTER TABLE account_cdn_settings
    ALTER COLUMN origin_protection_proxy_server_group DROP NOT NULL;
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
ALTER TABLE account_cdn_settings
    ALTER COLUMN origin_protection_proxy_server_group SET DEFAULT NULL;
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException();
    }
}
