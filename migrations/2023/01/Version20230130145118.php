<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20230130145118 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('CREATE INDEX CONCURRENTLY idx_cdn_created ON cdn USING btree (created)');
        $this->addSql('CREATE INDEX CONCURRENTLY idx_accounts_created ON accounts USING btree (created)');
        $this->addSql('CREATE INDEX CONCURRENTLY idx_gin_accounts_email ON accounts USING GIN (email gin_trgm_ops)');
        $this->addSql(
            <<<'PSQL'
CREATE INDEX CONCURRENTLY idx_gin_accounts_company ON accounts USING GIN (company gin_trgm_ops)
PSQL,
        );
        $this->addSql(
            <<<'PSQL'
CREATE INDEX CONCURRENTLY idx_gin_accounts_full_name ON accounts USING GIN (full_name gin_trgm_ops)
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }

    public function isTransactional(): bool
    {
        return false;
    }
}
