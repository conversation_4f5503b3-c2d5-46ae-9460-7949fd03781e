<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20230125133901 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('CREATE EXTENSION IF NOT EXISTS pg_trgm');
        $this->addSql('CREATE INDEX idx_gin_cdn_label ON cdn USING GIN (label gin_trgm_ops)');
        $this->addSql('CREATE INDEX idx_gin_cdn_cdn_url ON cdn USING GIN (cdn_url gin_trgm_ops)');
        $this->addSql('CREATE INDEX idx_gin_cdn_origin_url ON cdn_origin USING GIN (url gin_trgm_ops)');
        $this->addSql('CREATE INDEX idx_gin_cdn_origin_label ON cdn_origin USING GIN (label gin_trgm_ops)');
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
