<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20231020110616 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
            CREATE TABLE kayako_tickets (
                id UUID NOT NULL PRIMARY KEY,
                customer_id UUID NOT NULL REFERENCES accounts (new_id),
                external_id INTEGER NOT NULL,
                status INTEGER NOT NULL,
                created_at TIMESTAMP NOT NULL,
                updated_at TIMESTAMP
            )
            PSQL,
        );
        $this->addSql('CREATE UNIQUE INDEX kayako_tickets_external_id_idx ON kayako_tickets (external_id)');
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
