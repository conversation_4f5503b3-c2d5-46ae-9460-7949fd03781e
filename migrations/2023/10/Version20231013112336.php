<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20231013112336 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
            ALTER TABLE cdn_http
            ADD COLUMN rate_limit boolean NOT NULL DEFAULT false,
            ADD COLUMN content_disposition_type varchar(16) NOT NULL DEFAULT 'none',
            ADD COLUMN custom_origin_headers jsonb
            PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
