<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20231221103000 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
DROP TABLE IF EXISTS crop_api_errors;
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
DROP TABLE IF EXISTS ask_colleague_hash;
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
DROP TABLE IF EXISTS alerts_users;
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException();
    }
}
