<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20231227155000 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
ALTER TABLE account_xero_contacts
    ADD COLUMN xero_type int NOT NULL DEFAULT 0;
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
ALTER TABLE countries
    ADD COLUMN xero_tax_type varchar(15) NULL;
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException();
    }
}
