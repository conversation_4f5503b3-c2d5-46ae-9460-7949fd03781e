<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20230911053004 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('CREATE INDEX cdn_origin_bucket_name_idx ON cdn_origin (bucket_name)');
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
