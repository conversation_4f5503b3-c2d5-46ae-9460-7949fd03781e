<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230904090000 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
DROP TABLE 
    api.categories,
    api.examples,
    api.method_has_parameter,
    api.methods,
    api.methods_has_return_parameters,
    api.parameters,
    api.parameters_types,
    api.parameters_valid_values,
    api.version_has_category,
    api.versions;
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        parent::down($schema); // TODO: Change the autogenerated stub
    }
}
