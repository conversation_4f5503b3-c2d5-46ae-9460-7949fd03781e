<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20230908045913 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE storages.rgw_pricing RENAME COLUMN data_price_gb TO data_price_additional_gb');
        $this->addSql('ALTER TABLE storages.rgw_pricing ADD COLUMN data_price_initial_tb DOUBLE PRECISION NOT NULL');
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
