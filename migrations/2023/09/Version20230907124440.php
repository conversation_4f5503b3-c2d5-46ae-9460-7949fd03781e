<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20230907124440 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
            ALTER TABLE public.account_payment_settings
            ADD COLUMN paypal_enabled bool NOT NULL DEFAULT true
            PSQL,
        );

        $this->addSql(
            <<<'PSQL'
            ALTER TABLE public.account_payment_settings
            ALTER COLUMN paypal_enabled DROP DEFAULT
            PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
