<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20231121152653 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
CREATE OR REPLACE VIEW storages.rgw_stats_current AS
SELECT
    rss.id, origin_id, collected_at, size_bytes, object_count
FROM (
    (
        SELECT
          co.id,
          (SELECT MAX(rs.collected_at) FROM storages.rgw_stats rs WHERE co.id = rs.origin_id) AS ts
        FROM public.cdn_origin co
        WHERE co.type = 'object-storage'
    ) max_collected_at
    JOIN storages.rgw_stats rss ON (max_collected_at.id = rss.origin_id AND max_collected_at.ts = rss.collected_at)
)
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
