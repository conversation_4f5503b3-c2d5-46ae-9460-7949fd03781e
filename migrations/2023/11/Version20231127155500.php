<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20231127155500 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
ALTER TABLE cdn_origin
    ADD COLUMN deleted_at timestamp;
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
UPDATE cdn_origin
    SET deleted_at = removed_at
    WHERE removed_at IS NOT NULL
        AND deleted_at IS NULL
        AND type = 'object-storage';
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException();
    }
}
