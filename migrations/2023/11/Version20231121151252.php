<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20231121151252 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
CREATE INDEX CONCURRENTLY idx_rgw_stats_origin_id_collected_at ON storages.rgw_stats 
USING btree (origin_id, collected_at DESC)
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
CREATE INDEX CONCURRENTLY idx_rgw_stats_collected_at ON storages.rgw_stats USING btree (collected_at)
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }

    public function isTransactional(): bool
    {
        return false;
    }
}
