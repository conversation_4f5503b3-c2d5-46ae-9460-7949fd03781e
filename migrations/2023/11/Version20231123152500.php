<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20231123152500 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
alter table account_comments drop constraint account_comments_account_id_fkey;
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
alter table account_comments
    add foreign key (account_id) references accounts (id)
        on delete cascade;
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
alter table api.team_member_access_configuration
    drop constraint team_member_access_configuration_customer_id_fkey;
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
alter table api.team_member_access_configuration
    add foreign key (customer_id) references accounts (new_id)
        on delete cascade;
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
alter table api.credentials_personal_token
    drop constraint fk_credentials_personal_token_accounts;
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
alter table api.credentials_personal_token
    add foreign key (account_id) references accounts (id)
        on delete cascade;
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
alter table kayako_tickets
    drop constraint kayako_tickets_customer_id_fkey;
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
alter table kayako_tickets
    add foreign key (customer_id) references accounts (new_id)
        on delete cascade;
PSQL,
        );
    }
}
