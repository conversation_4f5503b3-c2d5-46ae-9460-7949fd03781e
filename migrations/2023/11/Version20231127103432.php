<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20231127103432 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
CREATE TABLE account_cache_expiry_values (
    id uuid NOT NULL PRIMARY KEY,
    customer_id uuid REFERENCES accounts(new_id),
    values jsonb NOT NULL,
    type character varying(20) NOT NULL,
    UNIQUE (customer_id, type)
)
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
