<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20230613153730 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
            CREATE TABLE public.account_origin_settings (
                id uuid PRIMARY KEY,
                customer_id uuid NOT NULL REFERENCES public.accounts(new_id) ON DELETE CASCADE INITIALLY DEFERRED,
                max_access_keys_per_cluster int NOT NULL
            )
            PSQL,
        );

        $this->addSql(
            <<<'PSQL'
            CREATE INDEX ON public.account_origin_settings (customer_id)
            PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
