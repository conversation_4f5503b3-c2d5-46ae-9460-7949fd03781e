<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20230624091521 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
            ALTER TABLE datacenter_traffic
            ADD COLUMN customer_id uuid REFERENCES accounts(new_id)
            PSQL,
        );

        $this->addSql(
            <<<'PSQL'
            ALTER TABLE datacenter_traffic
            ADD CONSTRAINT datacenter_traffic_customer_id_datacenter_id_day_is_dsa_key
                UNIQUE (customer_id, datacenter_id, day, is_dsa)
            PSQL,
        );

        $this->addSql(
            <<<'PSQL'
            ALTER TABLE datacenter_traffic
            DROP CONSTRAINT tik_tok_datacenter_traffic_datacenter_id_day_is_dsa_key
            PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
