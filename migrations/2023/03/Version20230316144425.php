<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20230316144425 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
CREATE TABLE command_buffer (
    id uuid NOT NULL CONSTRAINT command_buffer_pkey PRIMARY KEY,
    recorded_at timestamp NOT NULL,
    body jsonb NOT NULL,
    command_name character varying(255) NOT NULL
)
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
