<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230313154636 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
ALTER TABLE stream_origins 
ADD CONSTRAINT stream_origin_format_check
    CHECK (origin ~* '^(\w+-)?\d+\.s\.cdn77\.(com|eu)$' OR origin = 'livesport-flus.cdn77.eu')
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException();
    }
}
