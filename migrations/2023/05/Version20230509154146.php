<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230509154146 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
            ALTER TABLE public.payments_general
            DROP COLUMN status,
            DROP COLUMN status_message,
            DROP COLUMN payment_fee
        PSQL,
        );
    }
}
