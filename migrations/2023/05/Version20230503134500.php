<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230503134500 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
CREATE TABLE storages.users (
    id uuid PRIMARY KEY,
    created_at timestamp NOT NULL,
    cluster_id uuid REFERENCES storages.rgw_clusters(id) NOT NULL,
    customer_id uuid REFERENCES public.accounts(new_id) NOT NULL,
    access_key_id varchar(255) NOT NULL,
    label varchar(255) NOT NULL
);
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException();
    }
}
