<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20230529130107 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
            CREATE TABLE public.scheduled_suspensions (
                id uuid PRIMARY KEY,
                customer_id uuid REFERENCES public.accounts(new_id) NOT NULL,
                status varchar(16) NOT NULL,
                created_at timestamp NOT NULL,
                suspension_reason varchar(255) NOT NULL,
                suspension_date timestamp NOT NULL,
                suspended_by uuid REFERENCES public.accounts(new_id) NOT NULL
            )
            PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
