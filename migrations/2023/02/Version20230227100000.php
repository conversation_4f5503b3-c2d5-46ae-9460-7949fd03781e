<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230227100000 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
ALTER TABLE account_settings
    DROP COLUMN max_cdn_cnames,
    DROP COLUMN max_cdn_iqp,
    DROP COLUMN max_cdn_iqp_length,
    DROP COLUMN max_cdn_hlp_domains,
    DROP COLUMN max_cdns,
    DROP COLUMN max_deleted_cdns,
    DROP COLUMN max_cdn_gp_countries,
    DROP COLUMN max_cdn_ipp_addresses,
    DROP COLUMN min_cdn_datacenters,
    DROP COLUMN cdn_datacenters_edit_enabled,
    DROP COLUMN max_storage;
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException();
    }
}
