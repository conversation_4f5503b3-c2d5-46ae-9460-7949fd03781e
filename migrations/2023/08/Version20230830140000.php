<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230830140000 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
CREATE TABLE storages.rgw_private_cluster_members (
    id UUID NOT NULL primary key,
    cluster_id UUID references storages.rgw_clusters(id) NOT NULL,
    customer_id UUID references public.accounts(new_id) NOT NULL,
    UNIQUE (cluster_id, customer_id)
);
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException();
    }
}
