<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20230824135055 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
            ALTER TABLE pricing.contracts
            ADD COLUMN billing_unit character varying(10)
            PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
