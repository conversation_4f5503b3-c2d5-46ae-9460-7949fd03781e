<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20230831131532 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
            CREATE TABLE storages.rgw_pricing
            (
                id UUID NOT NULL PRIMARY KEY,
                customer_id UUID REFERENCES accounts(new_id),
                data_price_gb DOUBLE PRECISION NOT NULL,
                traffic_price_gb DOUBLE PRECISION NOT NULL,
                requests_price DOUBLE PRECISION NOT NULL
            )
            PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
