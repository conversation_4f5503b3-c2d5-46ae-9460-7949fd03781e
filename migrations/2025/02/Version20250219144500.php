<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250219144500 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
ALTER TABLE cdn_cnames
ADD CONSTRAINT idx_cdn_cnames_cname_resource_id UNIQUE (cname, resource_id) DEFERRABLE INITIALLY DEFERRED;
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
ALTER TABLE cdn_cnames
ADD CONSTRAINT idx_cdn_cnames_priority_resource_id UNIQUE (priority, resource_id) DEFERRABLE INITIALLY DEFERRED;
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException();
    }
}
