<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20250131172406 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
ALTER TABLE account_origin_settings
DROP CONSTRAINT account_origin_settings_default_real_time_log_access_key_i_fkey
PSQL,
        );
        $this->addSql(
            <<<'PSQL'
ALTER TABLE account_origin_settings
ALTER COLUMN default_real_time_log_access_key_id TYPE VARCHAR
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
