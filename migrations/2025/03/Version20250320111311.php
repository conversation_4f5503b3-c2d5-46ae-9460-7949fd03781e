<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20250320111311 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
ALTER TABLE real_time_log_fields
ADD COLUMN category_id uuid REFERENCES real_time_log_field_categories (id),
ADD COLUMN sort_order int
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
ALTER TABLE real_time_logs
DROP COLUMN category_id,
DROP COLUMN sort_order
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
