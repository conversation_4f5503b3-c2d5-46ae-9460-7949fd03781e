<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20250319100525 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
CREATE TABLE real_time_log_field_categories (
    id uuid PRIMARY KEY,
    name character varying (255) NOT NULL,
    sort_order int NOT NULL UNIQUE
)
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
ALTER TABLE real_time_logs
ADD COLUMN category_id uuid REFERENCES real_time_log_field_categories (id),
ADD COLUMN sort_order int
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
