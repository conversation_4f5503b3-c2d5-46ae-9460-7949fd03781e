<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20250414114701 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
            ALTER TABLE account_flags
            DROP COLUMN uses_euna,
            DROP COLUMN uses_sa,
            DROP COLUMN uses_as,
            DROP COLUMN zero_vat,
            DROP COLUMN class,
            DROP COLUMN enabled_control_panel,
            DROP COLUMN read_storage_termination_notice_at,
            DROP COLUMN read_storage_termination_notice_2_at
            PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
