<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20250423162135 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
ALTER TABLE real_time_logs
RENAME COLUMN filters TO internal_filters
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
ALTER TABLE real_time_logs
ADD COLUMN public_filters jsonb NOT NULL DEFAULT '[]'::jsonb
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
