<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20250521131244 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
            CREATE TABLE account_groups (
                id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
                name VARCHAR(255) NOT NULL,
                type VARCHAR(32) NOT NULL,
                created_at TIMESTAMP DEFAULT now()
            )
            PSQL,
        );

        $this->addSql(
            'CREATE INDEX idx_account_groups_type ON account_groups(type);',
        );

        $this->addSql(
            <<<'PSQL'
            CREATE TABLE account_group_members (
                id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
                account_group_id uuid NOT NULL REFERENCES account_groups(id) ON DELETE CASCADE,
                account_id uuid NOT NULL REFERENCES accounts(new_id) ON DELETE CASCADE,
                joined_at TIMESTAMP DEFAULT now(),
                UNIQUE (account_group_id, account_id)
            )
            PSQL,
        );

        $this->addSql(
            'CREATE INDEX idx_agm_account ON account_group_members(account_id);',
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
