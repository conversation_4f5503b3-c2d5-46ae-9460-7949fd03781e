<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20250512085504 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
            CREATE TABLE real_time_log_cdns (
                id uuid PRIMARY KEY,
                log_id uuid NOT NULL REFERENCES real_time_logs(id) INITIALLY DEFERRED,
                cdn_id int NOT NULL REFERENCES cdn(resource_id),
                UNIQUE (log_id, cdn_id)
            )
            PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
