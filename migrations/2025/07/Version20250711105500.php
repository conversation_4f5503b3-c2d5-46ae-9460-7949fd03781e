<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250711105500 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
ALTER TABLE cdn_http
ADD COLUMN cache_content_length_limit bigint NULL,
ADD COLUMN cache_lock_age integer NULL,
ADD COLUMN cache_lock_timeout integer NULL;
PSQL,
        );
    }
}
