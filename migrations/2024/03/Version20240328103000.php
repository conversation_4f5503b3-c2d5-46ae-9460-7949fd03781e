<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240328103000 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
CREATE TABLE datacenters.location_data_centers (
    id UUID PRIMARY KEY,
    location_id UUID REFERENCES datacenters.locations (uuid) NOT NULL,
    data_center_id VARCHAR(255) NOT NULL
);
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException();
    }
}
