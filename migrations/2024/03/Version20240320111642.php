<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20240320111642 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
ALTER TABLE datacenters.edges
DROP CONSTRAINT "edges_location_id_fkey",
ADD FOREIGN KEY ("location_id") REFERENCES datacenters.locations ("id") ON DELETE NO ACTION ON UPDATE CASCADE
PSQL,
        );
        $this->addSql(
            <<<'PSQL'
ALTER TABLE pricing.individual_prices
DROP CONSTRAINT "individual_prices_location_id_fkey",
ADD FOREIGN KEY ("location_id") REFERENCES datacenters.locations ("id") ON DELETE RESTRICT ON UPDATE CASCADE
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
