<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240207102500 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
CREATE INDEX idx_cdn_origin_created_at ON cdn_origin USING btree (created_at);
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
CREATE INDEX idx_storages_zone_created_at ON storages.zone USING btree (ts);
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException();
    }
}
