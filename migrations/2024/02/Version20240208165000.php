<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240208165000 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
CREATE UNIQUE INDEX cdn_origin_customer_id_label_unique
ON cdn_origin (customer_id, label)
WHERE removed_at IS NULL
AND bucket_name IS NULL;
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
CREATE UNIQUE INDEX cdn_object_storage_origin_customer_id_label_unique
ON cdn_origin (customer_id, label, url)
WHERE removed_at IS NULL
AND bucket_name IS NOT NULL;
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException();
    }
}
