<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240924150500 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
ALTER TABLE command_buffer
ADD COLUMN topic VARCHAR(60) NOT NULL default 'generic';
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
CREATE INDEX idx_command_buffer_topic ON command_buffer USING btree (topic);
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException();
    }
}
