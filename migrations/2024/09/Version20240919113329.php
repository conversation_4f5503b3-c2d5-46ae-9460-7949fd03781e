<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20240919113329 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
ALTER TABLE public.account_settings
ADD required_team_members_2fa boolean NOT NULL DEFAULT false;
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
