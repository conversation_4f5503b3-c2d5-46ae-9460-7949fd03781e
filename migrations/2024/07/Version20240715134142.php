<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20240715134142 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
ALTER TABLE "account_settings"
ADD COLUMN "real_time_log_enabled" boolean NOT NULL DEFAULT false;
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
CREATE TABLE "real_time_logs" (
  "id" uuid PRIMARY KEY,
  "cdn_id" integer NOT NULL REFERENCES cdn (resource_id),
  "created_at" timestamp NOT NULL,
  "active_from" timestamp NOT NULL,
  "active_until" timestamp,
  "customer_id" uuid NOT NULL REFERENCES accounts (new_id),
  "bucket_name" character varying(500) NOT NULL,
  "format" character varying(500) NOT NULL,
  "region" character varying(100) NOT NULL,
  "dump_by_account" boolean NOT NULL,
  "fields" jsonb NOT NULL
);
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
CREATE INDEX "real_time_logs_cdn_id" ON "real_time_logs" ("cdn_id");
PSQL,
        );
        $this->addSql(
            <<<'PSQL'
CREATE INDEX "real_time_logs_active_from" ON "real_time_logs" ("active_from");
PSQL,
        );
        $this->addSql(
            <<<'PSQL'
CREATE INDEX "real_time_logs_active_until" ON "real_time_logs" ("active_until");
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
