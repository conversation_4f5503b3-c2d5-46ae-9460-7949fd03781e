<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20240702082025 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
DROP INDEX stream_origins_uuid_idx
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
CREATE UNIQUE INDEX stream_origins_uuid_idx ON public.stream_origins USING btree (uuid)
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
DROP INDEX cdn_stream_uuid_idx
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
CREATE UNIQUE INDEX cdn_stream_uuid_idx ON public.cdn_stream USING btree (uuid)
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
