<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20240729122656 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
    ALTER TABLE "account_comments"
    ALTER COLUMN "message" DROP DEFAULT,
    ALTER COLUMN "message" SET NOT NULL;
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
