<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240716130000 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
ALTER TABLE account_settings
ADD COLUMN prefetch_requests_limit INTEGER DEFAULT NULL,
ADD COLUMN purge_requests_limit INTEGER DEFAULT NULL;
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
UPDATE account_settings
SET prefetch_requests_limit = api_data_limit_requests,
    purge_requests_limit = api_data_limit_requests
WHERE api_data_limit_requests IS NOT NULL;
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException();
    }
}
