<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20240702091652 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
CREATE TABLE public.invoice_lines (
    id UUID PRIMARY KEY,
    invoice_id UUID NOT NULL REFERENCES public.invoices (uuid) INITIALLY DEFERRED,
    billing_period_from timestamp,
    billing_period_to timestamp,
    created_at timestamp NOT NULL,
    description text NOT NULL,
    quantity real NOT NULL,
    unit_price_amount real NOT NULL,
    unit_price_currency character varying(3) NOT NULL,
    order_index integer NOT NULL,
    CONSTRAINT unique_invoice_order UNIQUE (invoice_id, order_index) INITIALLY DEFERRED 
)
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
