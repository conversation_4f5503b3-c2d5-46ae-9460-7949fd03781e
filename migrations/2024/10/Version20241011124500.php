<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Cdn77\Api\Core\Domain\Entity\ObjectStorage\RgwClusterId;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241011124500 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $nil = RgwClusterId::nil()->toString();

        $this->addSql(
            <<<PSQL
ALTER TABLE storages.rgw_pricing
    ADD COLUMN rgw_cluster_id uuid DEFAULT '$nil' NOT NULL;
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
ALTER TABLE storages.rgw_clusters
    ADD COLUMN disk_type int NOT NULL DEFAULT 0;
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException();
    }
}
