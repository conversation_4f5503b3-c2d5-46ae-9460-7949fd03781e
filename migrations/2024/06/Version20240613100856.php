<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20240613100856 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
ALTER TABLE api.request
DROP CONSTRAINT "request_account_id_fkey"
PSQL,
        );
        $this->addSql(
            <<<'PSQL'
ALTER TABLE api.request
ADD COLUMN host character varying(500) NULL
PSQL,
        );
        $this->addSql(
            <<<'PSQL'
ALTER TABLE api.request
ADD COLUMN parent_id integer NULL
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
