<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20240628134157 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
DROP INDEX invoices_uuid_idx
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
CREATE UNIQUE INDEX invoices_uuid_idx ON public.invoices USING btree (uuid)
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
