<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20240625123251 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
ALTER TABLE public.invoices
ADD COLUMN uuid UUID NOT NULL DEFAULT uuid_generate_v4()
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
CREATE INDEX invoices_uuid_idx ON public.invoices USING btree (uuid)
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
