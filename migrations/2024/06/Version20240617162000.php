<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20240617162000 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
ALTER TABLE storages.users
ADD created_by uuid NULL;
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
UPDATE "storages"."users" SET "created_by" = customer_id;
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
ALTER TABLE "storages"."users"
ALTER "created_by" SET NOT NULL;
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
ALTER TABLE "storages"."users"
ADD FOREIGN KEY ("created_by") REFERENCES "public"."accounts" ("new_id") ON DELETE NO ACTION ON UPDATE NO ACTION
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
