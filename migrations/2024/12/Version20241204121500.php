<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241204121500 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
ALTER TABLE account_settings
ADD COLUMN tsunami_enabled BOOLEAN NOT NULL DEFAULT FALSE;
PSQL,
        );
    }
}
