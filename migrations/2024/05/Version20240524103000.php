<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240524103000 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
ALTER TABLE cdn_stream
ADD COLUMN uuid UUID NOT NULL DEFAULT uuid_generate_v4();
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
CREATE INDEX cdn_stream_uuid_idx ON public.cdn_stream USING btree (uuid);
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException();
    }
}
