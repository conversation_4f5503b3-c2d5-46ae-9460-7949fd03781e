<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240524122500 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
ALTER TABLE stream_origins
ADD COLUMN uuid UUID NOT NULL DEFAULT uuid_generate_v4();
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
CREATE INDEX stream_origins_uuid_idx ON public.stream_origins USING btree (uuid);
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        $this->throwIrreversibleMigrationException();
    }
}
