<?php

declare(strict_types=1);

namespace Cdn77\Api\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\IrreversibleMigration;

final class Version20240522133633 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'PSQL'
ALTER TABLE stream_origins 
DROP CONSTRAINT stream_origin_format_check
PSQL,
        );

        $this->addSql(
            <<<'PSQL'
ALTER TABLE stream_origins 
ADD CONSTRAINT stream_origin_format_check
    CHECK (origin ~* '^(\w+-)?\d+\.s\.cdn77\.(com|eu)$'
    OR origin = 'livesport-flus.cdn77.eu' OR origin = 'prg-test.s.cdn77.com')
PSQL,
        );
    }

    public function down(Schema $schema): void
    {
        throw new IrreversibleMigration();
    }
}
