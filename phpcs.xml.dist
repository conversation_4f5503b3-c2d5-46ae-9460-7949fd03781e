<?xml version="1.0"?>
<ruleset
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="vendor/squizlabs/php_codesniffer/phpcs.xsd"
>
    <arg name="basepath" value="."/>
    <arg name="cache" value=".phpcs-cache"/>

    <rule ref="Cdn77">
        <exclude name="SlevomatCodingStandard.Classes.SuperfluousInterfaceNaming.SuperfluousSuffix"/>
        <exclude name="SlevomatCodingStandard.PHP.RequireExplicitAssertion.RequiredExplicitAssertion" />

        <!-- Tmp exclude new rules -->
        <exclude name="SlevomatCodingStandard.Classes.RequireConstructorPropertyPromotion.RequiredConstructorPropertyPromotion" />
    </rule>

    <file>phpstan/</file>
    <file>public/</file>
    <file>src/</file>
    <file>migrations/</file>
    <file>tests/</file>
    <file>bootstrap.php</file>
    <file>composer-dependency-analyser.php</file>
    <file>rector.php</file>
    <file>sailor.php</file>
</ruleset>
