version: "3.6"
services:
    nginx:
        build: .docker/nginx
        volumes:
            - ./:/app:delegated
        environment:
            VIRTUAL_HOST: clap.localhost
        restart: always
        networks:
            default:
            workspace:

    php-fpm:
        container_name: clap-php-fpm
        build: .docker/php-fpm
        restart: always
        depends_on:
            - nginx
        working_dir: /app
        volumes:
            - ./:/app:delegated
        environment:
            PHP_IDE_CONFIG: serverName=clap-docker
        networks:
            default:
            workspace:

    php-cli:
        build: .docker/php-cli
        volumes:
            - ./:/app:delegated
            - composer-home:/home/<USER>/.composer
            - ~/.ssh:/home/<USER>/.ssh:ro
        environment:
            - SSH_AUTH_SOCK=${SSH_AUTH_SOCK}
            - XDEBUG_CONFIG="client_host=dockerhost"
            - PHP_IDE_CONFIG=serverName=docker
        working_dir: /app
        restart: "no"

    postgres:
        image: postgres:17
        restart: always
        volumes:
            - postgres:/var/lib/postgresql/data:delegated
        environment:
            POSTGRES_DB: api
            POSTGRES_PASSWORD: postgres
        networks:
            default:
            workspace:
                aliases:
                    - clap-postgres
        ports:
            -   "5432:5432"

    clickhouse:
        image: clickhouse/clickhouse-server:25.7
        restart: always
        environment:
            CLICKHOUSE_USER: clap
            CLICKHOUSE_PASSWORD: p4ssw0rd
            CLICKHOUSE_DB: default
            CLICKHOUSE_TIMEZONE: UTC
        networks:
            default:
            workspace:
                aliases:
                    - clap-clickhouse
        ports:
            - "9000:9000"
            - "8123:8123"

    redis:
        image: redis:latest
        restart: always

volumes:
    postgres:
    composer-home:
        external: true

networks:
    workspace:
        name: workspace
        external: true
