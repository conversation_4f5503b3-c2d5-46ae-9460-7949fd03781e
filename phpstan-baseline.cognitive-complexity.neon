parameters:
	ignoreErrors:
		-
			message: '#^Cognitive complexity for "Cdn77\\Api\\Cdn\\Application\\Payload\\EditCdnSchema\:\:validateSchemaProperties\(\)" is 18, keep it under 10$#'
			identifier: complexity.functionLike
			count: 1
			path: src/Cdn/Application/Payload/EditCdnSchema.php

		-
			message: '#^Cognitive complexity for "Cdn77\\Api\\Cdn\\Domain\\Configuration\\CdnHttpConfigurator\:\:configure\(\)" is 16, keep it under 10$#'
			identifier: complexity.functionLike
			count: 1
			path: src/Cdn/Domain/Configuration/CdnHttpConfigurator.php

		-
			message: '#^Cognitive complexity for "Cdn77\\Api\\Core\\Domain\\Ceph\\PolicyConfigurator\:\:configure\(\)" is 12, keep it under 10$#'
			identifier: complexity.functionLike
			count: 1
			path: src/Core/Domain/Ceph/PolicyConfigurator.php

		-
			message: '#^Cognitive complexity for "Cdn77\\Api\\Core\\Infrastructure\\ClickHouse\\Provider\\ClickHouseLiveStreamingStatsProvider\:\:requestStats\(\)" is 13, keep it under 10$#'
			identifier: complexity.functionLike
			count: 1
			path: src/Core/Infrastructure/ClickHouse/Provider/ClickHouseLiveStreamingStatsProvider.php

		-
			message: '#^Cognitive complexity for "Cdn77\\Api\\Core\\Infrastructure\\ClickHouse\\Provider\\ClickHouseStatsProvider\:\:requestStats\(\)" is 13, keep it under 10$#'
			identifier: complexity.functionLike
			count: 1
			path: src/Core/Infrastructure/ClickHouse/Provider/ClickHouseStatsProvider.php

		-
			message: '#^Cognitive complexity for "Cdn77\\Api\\GraphQL\\Application\\Runtime\\DefaultFieldResolver\:\:__invoke\(\)" is 15, keep it under 10$#'
			identifier: complexity.functionLike
			count: 1
			path: src/GraphQL/Application/Runtime/DefaultFieldResolver.php

		-
			message: '#^Cognitive complexity for "Cdn77\\Api\\Stats\\Domain\\Chart\\ChartDataConverter\:\:convertStats\(\)" is 11, keep it under 10$#'
			identifier: complexity.functionLike
			count: 1
			path: src/Stats/Domain/Chart/ChartDataConverter.php

		-
			message: '#^Cognitive complexity for "Cdn77\\Api\\Stats\\Domain\\DataTable\\TableDataConverter\:\:convertContinentStats\(\)" is 12, keep it under 10$#'
			identifier: complexity.functionLike
			count: 1
			path: src/Stats/Domain/DataTable/TableDataConverter.php

		-
			message: '#^Cognitive complexity for "Cdn77\\Api\\Stats\\Domain\\Factory\\ContinentStatsFactory\:\:create\(\)" is 15, keep it under 10$#'
			identifier: complexity.functionLike
			count: 1
			path: src/Stats/Domain/Factory/ContinentStatsFactory.php

		-
			message: '#^Cognitive complexity for "Cdn77\\Api\\Ticket\\Domain\\Command\\SynchronizeKayakoTicketsHandler\:\:handle\(\)" is 11, keep it under 10$#'
			identifier: complexity.functionLike
			count: 1
			path: src/Ticket/Domain/Command/SynchronizeKayakoTicketsHandler.php
