deptrac:
  skip_violations:
    Cdn77\Api\Api\Domain\Resolver\RequestLogFactory:
      - Cdn77\Api\Api\Infrastructure\Request\SensitiveInformationObfuscator
      - Cdn77\Api\Core\Application\Controller\IsExcludedFromRequestLog
      - Cdn77\Api\Core\Application\Symfony\LoggedAccountProvider
    Cdn77\Api\Core\Domain\Entity\Origin\OriginId:
      - Cdn77\Api\Core\Application\OpenApi\DocumentationSectionLinkGenerator
      - Cdn77\Api\Core\Application\OpenApi\Model\Tags
    Cdn77\Api\Core\Domain\RateLimit\RateLimitChecker:
      - Cdn77\Api\Core\Application\Dto\RateLimitConfig
    Cdn77\Api\Core\Domain\RateLimit\RateLimiter:
      - Cdn77\Api\Core\Application\Dto\RateLimitConfig
    Cdn77\Api\Core\Domain\Stats\StatsProviderBag:
      - Cdn77\Api\Core\Infrastructure\Ara\Provider\AraStatsProvider
      - Cdn77\Api\Core\Infrastructure\ClickHouse\Provider\ClickHouseLiveStreamingStatsProvider
      - Cdn77\Api\Core\Infrastructure\ClickHouse\Provider\ClickHouseStatsProvider
    Cdn77\Api\Core\Domain\Dto\PruneTableSettings:
      - Cdn77\Api\Core\Infrastructure\DbalCommandProducer
    Cdn77\Api\Customer\Domain\Command\UpdateCustomerProfileInfo:
      - Cdn77\Api\Customer\Application\Payload\Dto\EditedCustomer
    Cdn77\Api\Customer\Domain\Limit\PasswordChangeRequestRateLimiter:
      - Cdn77\Api\Customer\Application\Controller\ChangePasswordController
    Cdn77\Api\Customer\Domain\Limit\PasswordVerifyRequestRateLimiter:
      - Cdn77\Api\Customer\Application\Controller\VerifyPasswordController
    Cdn77\Api\Customer\Domain\SetupAccount:
      - Cdn77\Api\Customer\Infrastructure\Factory\CustomerFactory
    Cdn77\Api\GraphQL\Domain\DataResolution\DataLoader\DataLoaderPromiseAdapterProvider:
      - Cdn77\Api\GraphQL\Application\Runtime\DataLoaderPromiseAdapter
    Cdn77\Api\Invoice\Domain\Command\AddInvoice:
      - Cdn77\Api\Invoice\Application\Payload\Dto\NewInvoice
    Cdn77\Api\Invoice\Domain\Dto\Address:
      - Cdn77\Api\Core\Application\Payload\SchemaPropertyResolver
      - Cdn77\Api\Invoice\Application\Payload\EditContactDetailSchema
    Cdn77\Api\Invoice\Domain\Dto\ContactDetail:
      - Cdn77\Api\Core\Application\Payload\SchemaPropertyResolver
      - Cdn77\Api\Invoice\Application\Payload\EditContactDetailSchema
    Cdn77\Api\Invoice\Domain\Query\FindInvoicesForExternalPaymentIds:
      - Cdn77\Api\Invoice\Application\Payload\Dto\ReconciliationExternalPaymentIdsScope
    Cdn77\Api\Job\Domain\Dto\PrefetchJob:
      - Cdn77\Api\Core\Application\Payload\SchemaPropertyResolver
      - Cdn77\Api\Job\Application\Payload\SchedulePrefetchSchema
    Cdn77\Api\Job\Domain\Dto\PurgeJob:
      - Cdn77\Api\Core\Application\Payload\SchemaPropertyResolver
      - Cdn77\Api\Job\Application\Payload\SchedulePurgeSchema
    Cdn77\Api\Monitoring\Overkill\Domain\MetricFactory\CustomerBandwidthMetricsFactory:
      - Cdn77\Api\Core\Infrastructure\ClickHouse\Provider\ClickHouseStatsProvider
    Cdn77\Api\ObjectStorage\Domain\Value\Policy:
      - Cdn77\Api\Core\Application\Payload\SchemaPropertyResolver
      - Cdn77\Api\ObjectStorage\Application\Payload\PolicySchema
    Cdn77\Api\Origin\Domain\Command\ConnectCdnsToOrigin:
      - Cdn77\Api\Origin\Application\Payload\Dto\OriginCdns
    Cdn77\Api\Origin\Domain\Command\EditOrigin:
      - Cdn77\Api\Origin\Application\Payload\Dto\EditedOrigin
    Cdn77\Api\Origin\Domain\Command\SetTimeoutToOrigin:
      - Cdn77\Api\Origin\Application\Payload\Dto\OriginTimeout
    Cdn77\Api\Origin\Domain\Validation\OriginAccessValidator:
      - Cdn77\Api\Core\Application\Validation\Validator
    Cdn77\Api\Payment\Domain\Command\AddPayment:
      - Cdn77\Api\Payment\Application\Payload\Dto\NewPayment
    Cdn77\Api\Payment\Domain\Command\RemoveCardInfo:
      - Cdn77\Api\Payment\Application\Payload\Dto\PaymentCard
    Cdn77\Api\Payment\Domain\Dto\CustomPlanPaymentData:
      - Cdn77\Api\Payment\Application\Payload\Dto\PaymentData
    Cdn77\Api\Payment\Domain\Dto\UpdatedCreditCard:
      - Cdn77\Api\Payment\Application\Payload\EditCreditCardSchema
    Cdn77\Api\Payment\Domain\Dto\UpdatedPaymentSettings:
      - Cdn77\Api\Payment\Application\Payload\PaymentSettings\EditSchema
    Cdn77\Api\Payment\Domain\Value\MonthlyPlanPaymentData:
      - Cdn77\Api\Payment\Application\Payload\Dto\PaymentData
    Cdn77\Api\Payment\Domain\Value\PAYGPaymentData:
      - Cdn77\Api\Payment\Application\Payload\Dto\PaymentData
    Cdn77\Api\Plan\Domain\Command\CloseMonthlyPlan:
      - Cdn77\Api\Plan\Application\Payload\Dto\MonthlyPlanTermination
    Cdn77\Api\RawLog\Domain\Dto\LogsSampleScope:
      - Cdn77\Api\RawLog\Application\Payload\LogsSampleScopeSchema
    Cdn77\Api\RawLog\Domain\Dto\RawLogFileAttributes:
      - Cdn77\Api\Core\Application\Payload\OASchema
    Cdn77\Api\Ssl\Domain\Contract\SslId:
      - Cdn77\Api\Core\Application\OpenApi\Schema\UuidSchema
      - Cdn77\Api\Core\Application\Payload\OASchema
    Cdn77\Api\Ssl\Domain\Dto\EditedSsl:
      - Cdn77\Api\Core\Application\Payload\SchemaPropertyResolver
      - Cdn77\Api\Ssl\Application\Payload\EditSslSchema
    Cdn77\Api\Ssl\Domain\Dto\SslToCheck:
      - Cdn77\Api\Core\Application\Payload\SchemaPropertyResolver
      - Cdn77\Api\Ssl\Application\Payload\CheckSslSchema
    Cdn77\Api\Ssl\Domain\Value\Certificate:
      - Cdn77\Api\Core\Application\Payload\OASchema
    Cdn77\Api\Ssl\Domain\Value\PrivateKey:
      - Cdn77\Api\Core\Application\Payload\OASchema
    Cdn77\Api\Storage\Domain\Dto\EditedStorage:
      - Cdn77\Api\Core\Application\Payload\Field
      - Cdn77\Api\Storage\Application\Payload\EditStorageSchema
    Cdn77\Api\Storage\Domain\Dto\NewStorage:
      - Cdn77\Api\Core\Application\Payload\SchemaPropertyResolver
      - Cdn77\Api\Storage\Application\Payload\NewStorageSchema
