<?php

declare(strict_types=1);

use Rector\Config\RectorConfig;
use <PERSON>\Php55\Rector\String_\StringClassNameToClassConstantRector;
use <PERSON>\Php73\Rector\String_\SensitiveHereNowDocRector;
use <PERSON>\Php74\Rector\Property\RestoreDefaultNullToNullableTypePropertyRector;
use <PERSON>\Php80\Rector\Class_\ClassPropertyAssignToConstructorPromotionRector;
use <PERSON>\Php81\Rector\FuncCall\NullToStrictStringFuncCallArgRector;
use Rector\Php81\Rector\Property\ReadOnlyPropertyRector;
use <PERSON>\Php83\Rector\ClassMethod\AddOverrideAttributeToOverriddenMethodsRector;
use Rector\PHPUnit\CodeQuality\Rector\Class_\PreferPHPUnitThisCallRector;
use Rector\PHPUnit\Set\PHPUnitSetList;
use Rector\Set\ValueObject\LevelSetList;
use Rector\ValueObject\PhpVersion;

return static function (RectorConfig $rectorConfig): void {
    $rectorConfig->parallel();
    $rectorConfig->phpVersion(PhpVersion::PHP_83);
    $rectorConfig->paths([
        __DIR__ . '/src',
        __DIR__ . '/tests',
    ]);

    $rectorConfig->sets([
        PHPUnitSetList::PHPUNIT_CODE_QUALITY,
        LevelSetList::UP_TO_PHP_83,
    ]);

    $rectorConfig->skip([
        PreferPHPUnitThisCallRector::class, // Keep PHPUnit static calls
        SensitiveHereNowDocRector::class,
        NullToStrictStringFuncCallArgRector::class,
        StringClassNameToClassConstantRector::class,
        RestoreDefaultNullToNullableTypePropertyRector::class,
        AddOverrideAttributeToOverriddenMethodsRector::class,
    ]);

    $rectorConfig->skip([
        ClassPropertyAssignToConstructorPromotionRector::class => [
            __DIR__ . '/src/Authentication/Application/Payload',
            __DIR__ . '/src/Cdn/Application/Payload',
            __DIR__ . '/src/Cname/Application/Payload',
            __DIR__ . '/src/Core/Application/Payload',
            __DIR__ . '/src/Core/Infrastructure/ClickHouse',
            __DIR__ . '/src/Core/Domain/Entity',
            __DIR__ . '/src/CoreLibrary/Application/Payload',
            __DIR__ . '/src/Customer/Application/Payload',
            __DIR__ . '/src/CustomPlan/Application/Payload',
            __DIR__ . '/src/Datacenter/Application/Payload',
            __DIR__ . '/src/Job/Application/Payload',
            __DIR__ . '/src/Inquiry/Application/Payload',
            __DIR__ . '/src/Invoice/Application/Payload',
            __DIR__ . '/src/MonthlyTrafficPlan/Application/Payload',
            __DIR__ . '/src/ObjectStorage/Application/Payload',
            __DIR__ . '/src/Origin/Application/Payload',
            __DIR__ . '/src/Payment/Application/Payload',
            __DIR__ . '/src/Plan/Application/Payload',
            __DIR__ . '/src/Rate/Application/Payload',
            __DIR__ . '/src/RawLog/Application/Payload',
            __DIR__ . '/src/Server/Application/Payload',
            __DIR__ . '/src/Statistics/Application/Payload',
            __DIR__ . '/src/Stats/Application/Payload',
            __DIR__ . '/src/Storage/Application/Payload',
            __DIR__ . '/src/StorageLocation/Application/Payload',
            __DIR__ . '/src/StoragePlan/Application/Payload',
            __DIR__ . '/src/Tariff/Application/Payload',
            __DIR__ . '/tests',
        ],
        ReadOnlyPropertyRector::class => [
            __DIR__ . '/src/Core/Domain/Entity',
        ],
    ]);
};
