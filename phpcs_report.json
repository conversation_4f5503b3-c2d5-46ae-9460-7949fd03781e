{"totals": {"errors": 12, "warnings": 0, "fixable": 12}, "files": {"phpstan\\ShipmonkDeadCode\\BusEntrypointProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "phpstan\\ShipmonkDeadCode\\OpenApiSpecProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "public\\index.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Alert\\Application\\Controller\\ListController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Alert\\Application\\Payload\\AlertSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Alert\\Application\\Payload\\AlertsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Alert\\Domain\\Query\\GetGlobalAlerts.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Alert\\Domain\\Query\\GetGlobalAlertsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Alert\\Domain\\Repository\\AlertRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Alert\\Domain\\Value\\Severity.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Alert\\Infrastructure\\Repository\\DoctrineAlertRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Api\\Domain\\Dto\\RequestLog.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Api\\Domain\\Finder\\BlockedIpFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Api\\Domain\\Query\\IsRequestRestricted.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Api\\Domain\\Query\\IsRequestRestrictedHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Api\\Domain\\RequestAttributeSetter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Api\\Domain\\Resolver\\RequestLogFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Api\\Domain\\Value\\RequestAttribute.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Api\\EventListener\\RequestListener.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Api\\EventListener\\ResponseListener.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Api\\Infrastructure\\Finder\\DbalBlockedIpFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Api\\Infrastructure\\Logger\\DbalRequestLogger.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Api\\Infrastructure\\Request\\SensitiveInformationObfuscator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Api\\Logger\\RequestLogger.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Controller\\AddPersonalTokenController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Controller\\DeleteController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Controller\\DisableTwoFactorAuthenticationController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Controller\\EnableTwoFactorAuthenticationController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Controller\\GenerateTokenController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Controller\\GetTwoFactorAuthenticationSetupController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Controller\\InvalidateAllSessionsController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Controller\\InvalidateSessionController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Controller\\ListController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Controller\\RefreshSessionController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Controller\\SessionsController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Controller\\VerifyCredentialsController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Controller\\XeroOAuthController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Payload\\AuthCredentialsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Payload\\AuthenticationFailureSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Payload\\DisableTwoFactorAuthenticationSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Payload\\EditCustomerSettingsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Payload\\EnableTwoFactorAuthenticationSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Payload\\GeneratedTokenSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Payload\\NewPersonalTokenSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Payload\\OAuthCodeSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Payload\\OAuthRedirectSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Payload\\PersonalBearerTokenSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Payload\\PersonalTokenListSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Payload\\PersonalTokenSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Payload\\SessionSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Payload\\SessionsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Payload\\TwoFactorAuthenticationSetupSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Application\\Payload\\VerificationResultSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Console\\GenerateApplicationToken.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Console\\UpdateApplicationToken.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Command\\CreateApplicationToken.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Command\\CreateApplicationTokenHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Command\\CreatePersonalToken.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Command\\CreatePersonalTokenHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Command\\DisableTwoFactorAuthentication.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Command\\DisableTwoFactorAuthenticationHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Command\\EnableTwoFactorAuthentication.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Command\\EnableTwoFactorAuthenticationHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Command\\ExchangeCodeForXeroBearerToken.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Command\\ExchangeCodeForXeroBearerTokenHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Command\\InvalidateSession.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Command\\InvalidateSessionHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Command\\InvalidateSessionsForCustomer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Command\\InvalidateSessionsForCustomerHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Command\\RefreshSession.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Command\\RefreshSessionHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Command\\RemovePersonalToken.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Command\\RemovePersonalTokenHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Command\\UpdateApplicationToken.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Command\\UpdateApplicationTokenHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Command\\VerifyCredentials.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Command\\VerifyCredentialsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Dto\\AuthCredentials.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Dto\\AuthenticatedCustomer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Dto\\NewPersonalToken.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Dto\\OAuthCode.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Dto\\OneTimePassword.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Dto\\QRCode.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Dto\\TwoFactorAuthenticationSetup.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Dto\\VerificationResult.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Exception\\ApplicationTokenNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Exception\\FailedToAddNewToken.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Exception\\InvalidCredentials.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Exception\\InvalidOtpProvided.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Exception\\MissingOtp.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Exception\\SessionExpired.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Exception\\SessionInvalidationFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Exception\\TokenNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Exception\\TwoFactorAuthenticationSetupFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Factory\\OneTimePasswordFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Factory\\QRCodeFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Finder\\PersonalTokenCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Finder\\SessionsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Query\\FindSessions\\FindSessions.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Query\\FindSessions\\FindSessionsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Query\\FindSessions\\Input\\Filter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Query\\FindSessions\\Input\\SelectionField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Query\\FindSessions\\Output\\Session.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Query\\FindSessions\\Output\\Sessions.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Query\\GetTokensForCustomer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Query\\GetTokensForCustomerHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Query\\GetTwoFactorAuthenticationSetup.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Query\\GetTwoFactorAuthenticationSetupHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Query\\GetXeroAuthorizationUri.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Query\\GetXeroAuthorizationUriHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Repository\\ApplicationTokenRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Repository\\LoginLogRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Repository\\PersonalTokenRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\TokenAccessValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\TwoFactorAuthenticator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Value\\ApiToken.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Value\\AuthenticationCode.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Value\\AuthenticationSecret.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Value\\AuthErrorType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Value\\AuthorizationHeader.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Value\\AuthorizationType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Value\\Token.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Value\\TokenHash.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Domain\\Value\\TokenSecret.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Infrastructure\\Factory\\EndroidQRCodeFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Infrastructure\\Finder\\DbalPersonalTokenCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Infrastructure\\Finder\\DbalSessionsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Infrastructure\\Repository\\DoctrineApplicationTokenRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Infrastructure\\Repository\\DoctrineLoginLogRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Authentication\\Infrastructure\\Repository\\DoctrinePersonalTokenRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Console\\ChargeRealTimeLogCustomers.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Console\\DeleteSuspendedCdnCommand.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\AddController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\AddStreamController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\ChangeLocationGroupController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\ChangePreferredLocationGroupController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\DatacenterLocationsController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\DeleteController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\DetailController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\DetailedListController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\EditController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\EditStreamCdnController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\EnableDatacentersController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\InternalListController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\ListController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\ListMaxAgeValuesController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\Log\\CreateRealTimeLogController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\Log\\DeleteRealTimeLogController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\Log\\EditRealTimeLogController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\Log\\GetRealTimeLogBillingController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\Log\\ListActiveRealTimeLogController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\Log\\ListCustomerRealTimeLogController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\Log\\ListRealTimeLogFieldsController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\Log\\PreviewRealTimeLogController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\Log\\RealTimeLogDetailController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\Log\\SumController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\SslStatusController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\StreamListController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\SuspendController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\Tsunami\\CdnController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\Tsunami\\DisableController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\Tsunami\\EnableTsunamiController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\Tsunami\\StatusController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\Tsunami\\UpdateController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Controller\\UnsuspendController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Docs\\AccessProtection\\GeoProtectionSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Docs\\AccessProtection\\HotlinkProtectionSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Docs\\AccessProtection\\IpProtectionSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Docs\\Cache\\CacheSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Docs\\CdnSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Docs\\CdnSchemaSummarySpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Docs\\Headers\\ContentDispositionSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Docs\\Headers\\HeadersSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Docs\\HttpsRedirect\\HttpsRedirectSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Docs\\MP4PseudoStreaming\\MP4PseudoStreamingSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Docs\\OriginHeaders\\OriginHeadersSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Docs\\QueryString\\QueryStringSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Docs\\RateLimit\\RateLimitSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Docs\\Redirect\\FollowRedirectSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Docs\\ResponseHeaders\\ResponseHeaderSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Docs\\ResponseHeaders\\ResponseHeadersSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Docs\\SecureToken\\SecureTokenSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Docs\\Ssl\\InstantSslSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Docs\\Ssl\\SslSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Docs\\Stream\\StreamSettingsSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Mapping\\CdnRequestMapper.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Mapping\\CdnResponseFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Mapping\\StreamCdnRequestMapper.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\AccessProtection\\GeoProtectionSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\AccessProtection\\HotlinkProtectionSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\AccessProtection\\IpProtectionSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Cache\\CacheSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Cache\\MaxAgeValuesListSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\CdnSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\CdnSchemaSummary.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\CdnsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\ChangeLocationGroupSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\ChangePreferredLocationGroupSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\CnamesSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\DatacenterLocationSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\DatacenterLocationsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\DetailedCdnsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\EditCdnSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\EditStreamCdnSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\EnabledDatacentersSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Forman\\ActiveRealTimeLogSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Forman\\AzureSinkConfigSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Forman\\DataSinkSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Forman\\FieldSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Forman\\FieldsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Forman\\ListActiveRealTimeLogsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Forman\\S3SinkConfigSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Headers\\ContentDispositionSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Headers\\HeadersSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\HttpsRedirect\\HttpsRedirectSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\InternalCdnSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\InternalCdnsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Log\\CdnReferenceSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Log\\CreatedRealTimeLogSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Log\\CreateRealTimeLogSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Log\\CustomerRealTimeLogSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Log\\EditRealTimeLogSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Log\\FilterSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Log\\ListCustomerRealTimeLogSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Log\\LoggedRequestsCountSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Log\\LogPathSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Log\\LogRecordsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Log\\ObjectStorageReferenceSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Log\\OutputDestinationConfigSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Log\\PreviewRealTimeLogScope.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Log\\RealTimeLogBillingSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Log\\RealTimeLogDetailSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Log\\RealTimeLogFieldSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Log\\RealTimeLogFieldsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Log\\RealTimeLoggedRequestsPricingSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Log\\TtlConfigSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\MP4PseudoStreaming\\MP4PseudoStreamingSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\NewCdnSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\NewStreamCdnSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\OriginHeaders\\OriginHeadersSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\OriginProtection\\LegacyOriginProtectionSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\QueryString\\QueryStringSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Quic\\QuicSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\RateLimitSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Redirect\\FollowRedirectSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\ResponseHeaders\\HeaderSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\ResponseHeaders\\ResponseHeadersSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\SecureToken\\ConfigSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\SecureToken\\SecureTokenSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Ssl\\InstantSslSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Ssl\\SniSslSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Ssl\\SslSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Stream\\StreamSettingsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\StreamCdnSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\StreamCdnsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\SuspendCdnSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Tsunami\\Request\\CdnProtectionSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Tsunami\\Request\\DisableSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Tsunami\\Request\\EnableTsunamiSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Tsunami\\Request\\GetStatusSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Tsunami\\Request\\TargetSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Tsunami\\Request\\UpdateSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Tsunami\\Response\\CdnProtectionSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Tsunami\\Response\\CdnSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Tsunami\\Response\\CdnsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Tsunami\\Response\\EnableTsunamiSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Tsunami\\Response\\FailedTargetSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Tsunami\\Response\\StatsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Tsunami\\Response\\SuccessfulTargetSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Tsunami\\Response\\TargetsStatusSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Tsunami\\Response\\TargetStatusSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Application\\Payload\\Waf\\WafSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\AccessProtection\\SetupGeoProtection.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\AccessProtection\\SetupHotlinkProtection.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\AccessProtection\\SetupIpProtection.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Ceph\\ClientApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\ChangeLocationGroup.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\ChangeLocationGroupHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\ChangePreferredLocationGroup.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\ChangePreferredLocationGroupHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\CreateCdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\CreateCdnHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\CreateStreamCdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\CreateStreamCdnHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\DeleteSuspendedCdns.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\DeleteSuspendedCdnsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\EditCdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\EditCdnHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\EditStreamCdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\EditStreamCdnHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\EnableDatacentersForCdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\EnableDatacentersForCdnHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\Log\\ChargeCustomers.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\Log\\ChargeCustomersHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\Log\\CreateRealTimeLog.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\Log\\CreateRealTimeLogHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\Log\\DeleteRealTimeLog.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\Log\\DeleteRealTimeLogHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\Log\\EditRealTimeLog.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\Log\\EditRealTimeLogHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\RemoveCdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\RemoveCdnHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\SuspendCdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\SuspendCdnHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\Tsunami\\DisableTsunami.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\Tsunami\\DisableTsunamiHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\Tsunami\\EnableTsunami.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\Tsunami\\EnableTsunamiHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\Tsunami\\UpdateTsunami.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\Tsunami\\UpdateTsunamiHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\UnsuspendCdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\UnsuspendCdnHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\UpdateCdnSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Command\\UpdateCdnSettingsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Configuration\\BucketLifecycleConfigurator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Configuration\\CdnCnamesConfigurator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Configuration\\CdnHttpConfigurator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Configuration\\CdnServerGroupConfigurator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Configuration\\CdnSslConfigurator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Configuration\\RealTimeLogConfigurator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\AccessProtection\\GeoProtection.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\AccessProtection\\HotlinkProtection.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\AccessProtection\\IpProtection.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\CacheSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\Cdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\CdnDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\CdnLocationGroupSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\CdnReference.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\CdnSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\CdnSuspensionDetails.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\DatacenterLocation.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\DatacenterLocationStatus.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\EditedStreamCdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\Headers\\HeadersSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\Headers\\UpdateHeadersSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\HttpsRedirectSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\InstantSslSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\Log\\ActiveRealTimeLog.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\Log\\CdnReference.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\Log\\CreatedRealTimeLog.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\Log\\LogRecords.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\Log\\ObjectStorageReference.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\Log\\OutputDestinationConfig.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\Log\\RealTimeLogDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\Log\\RealTimeLogWithCdns.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\MaxAgeValues.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\Mp4PseudoStreamingSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\NewCdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\NewStreamCdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\PreferredLocationGroupSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\QueryStringSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\QuicSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\SecureToken.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\SniSslSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\SslSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\StreamCdnDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\StreamSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\Tsunami\\GetTargetStatusScope.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\Tsunami\\ProtectableCdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\Tsunami\\Stats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\Tsunami\\TargetStatus.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\Tsunami\\TsunamiResult.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\Tsunami\\UpdateScope.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\UpdateCacheSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Dto\\WafSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\CdnAccessNotAllowed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\CdnAlreadyDeleted.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\CdnCouldNotBeCreated.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\CdnDatacentersCouldNotBeUpdated.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\CdnHttpIsNotSetup.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\CdnHttpProtectionIsNotSetup.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\CdnNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\CdnOriginConfigurationFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\CdnRemovalFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\CdnServerGroupConfigurationFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\CdnSettingsNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\CdnUpdateFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\FailedToResolveTargets.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\InstantSslToggleFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\InvalidAccessProtectionSetup.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\InvalidCustomHeader.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\Log\\DeleteRealTimeLogFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\Log\\EditRealTimeLogFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\Log\\EnableRealTimeLogFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\Log\\FailedToPreviewRealTimeLog.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\Log\\InvalidLogFields.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\Log\\InvalidLogPath.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\Log\\RealTimeLogNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\Log\\TtlConfigurationFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\OriginCouldNotBeResolved.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\QueryStringSetupFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\TsunamiAccessNotAllowed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Exception\\UnableToResolveSslStatus.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Factory\\CdnDetailFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Factory\\CdnFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Factory\\CdnLegacyIdFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Factory\\CdnResourceIdFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Factory\\RealTimeLogFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Finder\\CdnDomainsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Finder\\CdnIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Finder\\CdnLabelFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Finder\\CdnSettingsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Finder\\CdnsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Finder\\CustomerCdnCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Finder\\DataCenterIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Finder\\Log\\CustomerIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Finder\\Log\\RealTimeLogCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Finder\\Log\\RealTimeLogFieldFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Finder\\Log\\RealTimeLogFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Finder\\Log\\RealTimeLoggedRequestsCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Finder\\Log\\RealTimeLogIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Finder\\MaxAgeCustomValuesFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Finder\\OriginIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Finder\\StreamCdnFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Finder\\StreamCdnIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Finder\\StreamingPlaylistBypassEnabledStatusFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\ForbiddenDatacentersMap.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Forman\\FormanApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Forman\\FormanApiError.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Logger\\DatacenterLocationsDifferencesLogger.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindAccountCdns.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindAccountCdnsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindCdnHistoryForCustomer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindCdnHistoryForCustomerHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindCdns\\FindCdns.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindCdns\\FindCdnsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindCdns\\Input\\Filter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindCdns\\Input\\SelectionField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindCdns\\OrderField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindCdns\\Output\\Cdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindCdns\\Output\\Cdns.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindCdnSettings\\FindCdnSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindCdnSettings\\FindCdnSettingsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindCdnSettings\\Input\\Filter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindCdnSettings\\Input\\SelectionField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindCdnSettings\\Output\\CdnSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindCdnSettings\\Output\\CdnSettingsCollection.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindRealTimeLogFields\\FindRealTimeLogFields.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindRealTimeLogFields\\FindRealTimeLogFieldsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindRealTimeLogFields\\Input\\Filter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindRealTimeLogFields\\Input\\SelectionField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindRealTimeLogFields\\Output\\RealTimeLogField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindRealTimeLogFields\\Output\\RealTimeLogFields.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindRealTimeLogs\\FindRealTimeLogs.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindRealTimeLogs\\FindRealTimeLogsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindRealTimeLogs\\Input\\Filter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindRealTimeLogs\\Input\\SelectionField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindRealTimeLogs\\Output\\RealTimeLog.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindRealTimeLogs\\Output\\RealTimeLogs.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindStreamCdns\\FindStreamCdns.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindStreamCdns\\FindStreamCdnsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindStreamCdns\\Input\\Filter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindStreamCdns\\Input\\SelectionField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindStreamCdns\\Output\\StreamCdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindStreamCdns\\Output\\StreamCdns.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindStreamCdnsForCustomer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\FindStreamCdnsForCustomerHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\GetCdnDetails.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\GetCdnDetailsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\GetCdnForCustomer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\GetCdnForCustomerHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\GetCustomerMaxAgeValues.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\GetCustomerMaxAgeValuesHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\GetDatacenterLocationsForCdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\GetDatacenterLocationsForCdnHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\GetSslStatus.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\GetSslStatusHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\GetTsunamiStatus.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\GetTsunamiStatusHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\Log\\FindAllActiveRealTimeLogs.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\Log\\FindAllActiveRealTimeLogsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\Log\\GetRealTimeLogBilling.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\Log\\GetRealTimeLogBillingHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\Log\\GetRealTimeLogDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\Log\\GetRealTimeLogDetailHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\Log\\GetSum.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\Log\\GetSumHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\Log\\PreviewRealTimeLog.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\Log\\PreviewRealTimeLogHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\Tsunami\\FindProtectableCdns.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Query\\Tsunami\\FindProtectableCdnsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\QueryString\\SetupIgnoredQueryParams.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Quota\\CreatedCdnQuotaChecker.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Quota\\DeletedCdnQuotaChecker.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\RealTimeLogCharger.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\RealTimeLogDisabler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\RealTimeLogEnabler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\RealTimeLogPriceCalculator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Repository\\CdnHttpProtectionRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Repository\\CdnHttpRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Repository\\CdnRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Repository\\CdnSslRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Repository\\GeoProtectionCountryRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Repository\\HotlinkProtectionDomainRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Repository\\IgnoredQueryParametersRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Repository\\IpProtectionAddressRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Repository\\OriginFallbackRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Repository\\SslRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Repository\\StreamCdnRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Repository\\StreamOriginRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Repository\\TeamMemberAccessConfigurationRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Repository\\TsunamiGroupRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Resolver\\Cdn77BucketDestinationResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Resolver\\OutputDestinationResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Resolver\\RealTimeLogCdnsResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Resolver\\TtlConfigResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\ServerGroupIdResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Tsunami\\AllowedTargetsResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Tsunami\\CdnDomainLevelsMapper.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Tsunami\\CdnResourceTargetsResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Tsunami\\CdnServerGroupValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Tsunami\\TargetIpResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Validation\\CdnLabelValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Validation\\CdnOriginValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Validation\\MaxAgeValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Validation\\RealTimeLogAccessValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Validation\\RealTimeLogActivationValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Validation\\RealTimeLogFieldsValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Validation\\RealTimeLogPathValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Validation\\TsunamiAccessValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\AccessProtectionType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\AccountGroup.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Cache\\ContentLengthLimit.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Cache\\DefaultMaxAge404.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Cache\\LockAge.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Cache\\LockTimeout.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Cache\\RequestsWithCookies.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\CdnCname.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\CdnType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\CdnUrl.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Cname\\CnameRecord.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\CreatedCdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Forman\\AzureSinkConfig.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Forman\\DataSink.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Forman\\NoopSinkConfig.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Forman\\RealTimeLogConfiguration.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Forman\\S3SinkConfig.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\HttpSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\HttpsRedirectCode.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Log\\Filter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Log\\FilterOperator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Log\\Filters.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Log\\LogBilling.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Log\\LoggedRequestsCount.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Log\\LoggedRequestsPricing.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Log\\LoggingField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Log\\LoggingFieldAvailability.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Log\\LoggingFieldName.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Log\\LoggingFields.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Log\\LogPath.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Log\\LogPrice.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Log\\LogScope.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Log\\LogStatus.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Log\\OutputDestination\\AzureDestination.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Log\\OutputDestination\\Cdn77BucketDestination.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Log\\OutputDestination\\NoopDestination.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Log\\OutputDestination.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Log\\OutputDestinationType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Log\\OutputFormat.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\MaxAge.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\MaxAge404.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\NoAssignedSsl.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Parameters\\ContentDisposition.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Parameters\\ContentDispositionType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Parameters\\CustomHeader.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Parameters\\CustomOriginHeaders.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Parameters\\CustomResponseHeaders.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Parameters\\FollowRedirect.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Parameters\\FollowRedirectType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Parameters\\MaxAgeType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Parameters\\RateLimit.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\Parameters\\StreamingPlaylistBypass.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\ProxyServerGroup.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\QueryStringIgnoreType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\SecureTokenConfig.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\SecureTokenType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\StreamProtocol.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\SuspensionReason.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\TsunamiStatus.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Domain\\Value\\UpdatedHttpSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Ceph\\FakeS3ClientApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Ceph\\S3ClientApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Factory\\DbalCdnResourceIdFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Factory\\DbalSequenceCdnLegacyIdFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Finder\\DbalCdnDomainsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Finder\\DbalCdnIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Finder\\DbalCdnSettingsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Finder\\DbalCdnsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Finder\\DbalDataCenterIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Finder\\DbalMaxAgeCustomValuesFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Finder\\DbalOriginIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Finder\\DbalStreamCdnIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Finder\\DbalStreamCdnsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Finder\\DbalStreamingPlaylistBypassEnabledStatusFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Finder\\DoctrineCdnLabelFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Finder\\DoctrineCustomerCdnCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Finder\\Log\\ClickHouseRealTimeLoggedRequestsCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Finder\\Log\\DbalCustomerIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Finder\\Log\\DbalRealTimeLogCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Finder\\Log\\DbalRealTimeLogFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Finder\\Log\\DbalRealTimeLogIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Finder\\Log\\DbaRealTimeLogFieldFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Forman\\Endpoint\\BaseEndpoint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Forman\\Endpoint\\DisableEndpoint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Forman\\Endpoint\\EnableEndpoint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Forman\\Endpoint\\PeekEndpoint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Forman\\FakeFormanApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Forman\\HttpFormanApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Quota\\DatabaseCreatedCdnQuotaChecker.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Quota\\DatabaseDeletedCdnQuotaChecker.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Repository\\DoctrineCdnHttpProtectionRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Repository\\DoctrineCdnHttpRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Repository\\DoctrineCdnRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Repository\\DoctrineCdnSslRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Repository\\DoctrineGeoProtectionCountryRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Repository\\DoctrineHotlinkProtectionRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Repository\\DoctrineIgnoredQueryParametersRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Repository\\DoctrineIpProtectionAddressRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Repository\\DoctrineOriginFallbackRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Repository\\DoctrineSslRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Repository\\DoctrineStreamCdnRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Repository\\DoctrineStreamOriginRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Repository\\DoctrineTeamMemberAccessConfigurationRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Repository\\DoctrineTsunamiGroupRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cdn\\Infrastructure\\Validation\\DatabaseCdnLabelValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ClapException.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Application\\Console\\DetectFraudHosts.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Application\\Controller\\AddController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Application\\Controller\\ListController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Application\\Controller\\ListWithSniCertificateController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Application\\Docs\\CnameSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Application\\Docs\\CnamesSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Application\\Payload\\CnameSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Application\\Payload\\CnamesSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Application\\Payload\\CnamesWithCertificateSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Application\\Payload\\CnameWithCertificateSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Application\\Payload\\NewCnameSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Domain\\Command\\AddCnameForCdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Domain\\Command\\AddCnameForCdnHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Domain\\Command\\DetectFraudHosts.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Domain\\Command\\DetectFraudHostsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Domain\\Dto\\CnameDnsRecord.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Domain\\Dto\\CnameWithCertificate.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Domain\\Dto\\SslCertificate.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Domain\\Exception\\CnameDnsRecordsNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Domain\\Exception\\CnameIsForbidden.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Domain\\Exception\\CnameLimitReached.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Domain\\Exception\\CnamesAreInUse.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Domain\\Finder\\CnameCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Domain\\Finder\\CnameWithCertificateFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Domain\\Finder\\DnsRecordsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Domain\\Finder\\SslCertificateWithActiveCdnsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Domain\\Query\\FindCdnCnames.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Domain\\Query\\FindCdnCnamesHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Domain\\Query\\FindCnamesWithCertificate.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Domain\\Query\\FindCnamesWithCertificateHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Domain\\Repository\\CnameRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Domain\\Validation\\CnameValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Infrastructure\\Finder\\DbalCnameCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Infrastructure\\Finder\\DbalSslCertificateWithActiveCdnsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Infrastructure\\Finder\\DoctrineCnameWithCertificateFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Infrastructure\\Finder\\SpatieDnsRecordsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Cname\\Infrastructure\\Repository\\DoctrineCnameRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Console\\CatchCommandProcessName.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Console\\Command\\EncryptColumnCommand.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Console\\Exception\\CommandAlreadyRunning.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Console\\LockAcquirer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Console\\Output\\ConsoleOutputBuffer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Console\\Style\\BufferedSymfonyStyle.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Console\\Value\\LockName.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Container\\ExceptionResponseHandlerPass.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Container\\MockedHttpClientPass.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Container\\OpenApiComponentsCompilerPass.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Container\\OpenApiPathsCompilerPass.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Controller\\DocumentationJsonController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Controller\\HasInternalOpenApiPaths.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Controller\\HasOpenApiPaths.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Controller\\InternalDocumentationJsonController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Controller\\IsAllowedForSource.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Controller\\IsExcludedFromRequestLog.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Controller\\NxgApiProxyController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Controller\\PingController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Controller\\PingInternalController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Controller\\ProcessDeliveryDataController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Controller\\RequiresInternalTokenAuthentication.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Controller\\SupportsAnonymousRequest.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Controller\\SupportsCredentialsRateLimit.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Docs\\ErrorsSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Docs\\FieldsErrorsSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Dto\\RateLimitConfig.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\EventHandler\\ExceptionListener.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\EventHandler\\Handler\\DomainExceptionHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\EventHandler\\Handler\\JsonExceptionHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\EventHandler\\Handler\\MethodNotAllowedHttpExceptionHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\EventHandler\\Handler\\NotFoundHttpExceptionHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\EventHandler\\Handler\\SerializerRuntimeExceptionHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\EventHandler\\Handler\\SupportsExceptionSerialization.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\EventHandler\\HandlerChain.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Exception\\InvalidType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\OpenApi\\DocumentationSectionLinkGenerator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\OpenApi\\ErrorsOpenApiResponse.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\OpenApi\\FieldsErrorsOpenApiResponse.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\OpenApi\\Model\\ComponentReference.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\OpenApi\\Model\\Tags.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\OpenApi\\OpenApiComponents.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\OpenApi\\OpenApiPaths.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\OpenApi\\OpenApiSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\OpenApi\\PathGenerator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\OpenApi\\Paths\\JsonResponseSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\OpenApi\\Schema\\ArraySchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\OpenApi\\Schema\\BooleanSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\OpenApi\\Schema\\DateTimeSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\OpenApi\\Schema\\EmailSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\OpenApi\\Schema\\FloatSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\OpenApi\\Schema\\IntegerSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\OpenApi\\Schema\\ObjectSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\OpenApi\\Schema\\SchemaBuilder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\OpenApi\\Schema\\StringSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\OpenApi\\Schema\\UuidSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\AbstractQuerySchemaResult.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\CdnReferenceSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\CommandBusResultSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\ComponentSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\DateTimeRangeSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\DeliveryDataSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\ErrorsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\Field.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\FieldsErrorsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\HasReference.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\MoneySchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\NxgApiProxyRequestSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\OAParamSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\OASchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\QueryBusResultSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\SchemaEditor.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\SchemaPropertyResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\SchemaPropertyValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\SignInTokenSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\SupportsCommandDto.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\SupportsDeserialization.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\SupportsDtoDeserialization.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\SupportsPropertyValidation.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\SupportsQueryDto.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\SupportsRequestCommandDeserialization.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\SupportsRequestQueryDeserialization.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\TimeRangeSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Payload\\TimestampedDataSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Request\\CatchRequestProcessName.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Request\\ControllerSchemaSerializer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Response\\AccessDeniedExceptionHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Response\\ControllerCommandHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Response\\ControllerQueryHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Response\\ControllerRateLimitProcessor.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Response\\ControllerRequestSchemaProcessor.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Response\\DomainExceptionResponseResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Response\\Exception\\UnknownErrorOccurred.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Response\\ExceptionHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Response\\ResponseFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\RunningProcessNameCarrier.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Sentry\\CommandCheckInHelper.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Sentry\\Exception\\CommandCheckInFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Sentry\\Value\\CommandCheckIn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Symfony\\Authenticator\\InternalTokenAuthenticator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Symfony\\Authenticator\\JwtAuthenticator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Symfony\\Authenticator\\TokenAuthenticator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Symfony\\Authorization\\AccessResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Symfony\\LoggedAccountProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Symfony\\LogsDirProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Symfony\\TokenAccountHolderProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Symfony\\User\\LoggedCustomer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Validation\\Error\\FieldValidationFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Validation\\Error\\InvalidPropertyArgumentError.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Validation\\SchemaValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Application\\Validation\\Validator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Console\\CronCommand.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Console\\FlushCommandBuffer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Console\\PruneCommand.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Console\\RefreshXeroOAuthTokenCommand.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Console\\SaveDisabledRateLimitedTokenIdsCommand.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Ara\\Ara.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Ara\\AraCdnEnqueuer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Authorization\\AccessValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Authorization\\AccessValidatorRegistry.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Authorization\\Context\\AccessContext.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Authorization\\Context\\BillingAccessContext.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Authorization\\Context\\LogAccessContext.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Authorization\\Context\\ObjectStorageAccessContext.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Authorization\\Context\\OriginAccessContext.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Authorization\\Context\\SslAccessContext.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Authorization\\CustomerContextStore.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Billing\\Biller.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Billing\\BillerProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Billing\\Contact\\ContactMigrator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Billing\\Contact\\XeroContactMigrator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Billing\\Converter\\VatRateDbConverter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Billing\\Exception\\CouldNotCreateInvoice.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Billing\\Exception\\InvalidInvoiceDate.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Billing\\Exception\\XeroInvoiceNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Billing\\Formatter\\VatRateFormatter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Billing\\InvoiceDueDateCalculator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Billing\\LegacyBiller.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Billing\\Validation\\InvoiceDateValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Billing\\Value\\BillingDescription.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Billing\\Value\\BillingPeriod.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Billing\\Value\\InvoiceDueDateOffset.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Billing\\Value\\VatRate.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\ByteUnit.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Cdn\\CdnRemover.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Cdn\\Configuration\\CdnOriginConfigurator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Cdn\\Configuration\\ObjectStorageOriginConfigurator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Cdn\\Configuration\\OriginConfigurator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Ceph\\AdminApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Ceph\\ClientApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Ceph\\CredentialsResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Ceph\\Exception\\BucketNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Ceph\\ObjectStorageCredentialsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Ceph\\PolicyConfigurator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\ChangeLog.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Command\\ProcessDeliveryData.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Command\\ProcessDeliveryDataHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Command\\PruneTable.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Command\\PruneTableHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Command\\RefreshOAuthToken.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Command\\RefreshOAuthTokenHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Command\\SaveCronLog.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Command\\SaveCronLogHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Command\\SaveDisabledRateLimitTokenIds.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Command\\SaveDisabledRateLimitTokenIdsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Continent\\Value\\ContinentCode.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Currency\\CurrencyConverter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Customer\\CustomerContext.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Customer\\CustomerContextProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Dto\\Cname\\Cname.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Dto\\Cname\\Cnames.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Dto\\CronLog.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Dto\\DeliveryData.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Dto\\Origin\\CdnOrigin.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Dto\\Origin\\ObjectStorageOrigin.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Dto\\Origin\\S3Origin.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Dto\\Origin\\StorageOrigin.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Dto\\Origin\\StreamOrigin.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Dto\\Origin\\UrlOrigin.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Dto\\PruneTableResult.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Dto\\PruneTableSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Dto\\TimeRange.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Dto\\TokenPair.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Encryption\\CredentialsEncryptor.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Encryption\\DataEncryptor.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Encryption\\Sodium\\Value\\EncryptedValue.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Encryption\\ValueEncryptor.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\AddOn\\AddOn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Alert\\Alert.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Alert\\AlertId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Api\\ApplicationToken.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Api\\BlockedIp.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Api\\PersonalToken.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Api\\PersonalTokenId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Api\\TeamMemberAccessConfiguration.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Api\\TeamMemberAccessConfigurationId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\AraQueue\\AraQueueItem.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Authentication\\OAuthToken.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Authentication\\Session.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Authentication\\SessionId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Cdn\\Cdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Cdn\\CdnHttp.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Cdn\\CdnHttpId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Cdn\\CdnHttpProtection.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Cdn\\CdnHttpProtectionId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Cdn\\CdnId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Cdn\\CdnLegacyId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Cdn\\CdnSsl.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Cdn\\CdnTraffic.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Cdn\\GeoProtectionCountry.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Cdn\\HotlinkProtectionDomain.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Cdn\\IgnoredQueryParameter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Cdn\\IpProtectionAddress.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Cdn\\StreamCdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Cdn\\TsunamiGroup.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Cname\\Cname.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Cname\\CnameId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Cname\\CnameLegacyId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Currency\\ExchangeRate.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Currency\\ExchangeRateId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\AccountFlags.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\AccountSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\AccountSettingsId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\AccountSignUpInvite.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\AccountSignUpInviteId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\Credentials.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\Customer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\CustomerCdnSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\CustomerCdnSettingsId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\CustomerId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\CustomerMail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\CustomerMailId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\CustomerMailingUnsubscribe.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\CustomerOriginSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\CustomerPaymentSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\CustomerPaymentSettingsId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\CustomerTraffic.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\CustomerTrafficId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\CustomerUuid.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\EmailAddressConfirmation.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\EmailAddressConfirmationId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\KayakoTicket.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\KayakoTicketId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\LoginLog.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\Note.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\NoteId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\PublicCustomerId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\ResetPasswordRequest.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\ScheduledSuspension.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Customer\\ScheduledSuspensionId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\CustomPlan\\Contract.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\CustomPlan\\ContractId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\CustomPlan\\CustomPlan.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\CustomPlan\\CustomPlanId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Datacenter\\DataCenterId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Datacenter\\Location.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Datacenter\\LocationId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Datacenter\\LocationUuid.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Datacenter\\Region.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Datacenter\\RegionId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Datacenter\\RegionLocation.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Datacenter\\RegionLocationId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Datacenter\\Server.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Datacenter\\ServerGroupId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Datacenter\\ServerId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Datacenter\\Status.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\DataManipulation\\Job.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\DataManipulation\\JobId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\DataManipulation\\Prefetch.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\DataManipulation\\Purge.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\DataManipulation\\PurgeAll.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Inquiry\\Inquiry.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Inquiry\\InquiryId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Invoice\\Country.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Invoice\\CountryId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Invoice\\Invoice.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Invoice\\InvoiceCustomer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Invoice\\InvoiceCustomerId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Invoice\\InvoiceId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Invoice\\InvoiceLine.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Invoice\\InvoiceLineId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Invoice\\XeroContact.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Mail\\MailLogDetails.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Mail\\MailLogId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\MonthlyTrafficPlan\\CustomerMonthlyTrafficPlan.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\MonthlyTrafficPlan\\CustomerMonthlyTrafficPlanId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\MonthlyTrafficPlan\\Group.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\MonthlyTrafficPlan\\GroupId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\MonthlyTrafficPlan\\MonthlyTrafficPlan.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\MonthlyTrafficPlan\\MonthlyTrafficPlanId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\ObjectStorage\\AccessKey.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\ObjectStorage\\AccessKeyId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\ObjectStorage\\RgwCluster.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\ObjectStorage\\RgwClusterId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\ObjectStorage\\RgwPricing.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\ObjectStorage\\RgwPricingId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\ObjectStorage\\RgwPrivateClusterMember.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\ObjectStorage\\RgwStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Origin\\AdminCredentials.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Origin\\LegacyStreamOriginId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Origin\\Origin.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Origin\\OriginFallback.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Origin\\OriginId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Origin\\StreamOrigin.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Origin\\StreamOriginId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Payment\\Payment.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Payment\\PaymentId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Payment\\PaymentRecipe.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Payment\\PaymentRecipeId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Payment\\PaymentUuid.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Plan\\Plan.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Plan\\PlanCdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Plan\\PlanId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Plan\\PlanLocation.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Promo\\PromoCode.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Promo\\PromoCodeId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Promo\\PromoCodeUsage.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Promo\\PromoCodeUsageId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Rate\\Rate.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\RawLogs\\RawLog.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\RealTimeLog\\RealTimeLog.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\RealTimeLog\\RealTimeLogCdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\RealTimeLog\\RealTimeLogField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\RealTimeLog\\RealTimeLogFieldCategory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\RealTimeLog\\RealTimeLogFieldCategoryId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\RealTimeLog\\RealTimeLogFieldId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\RealTimeLog\\RealTimeLogId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Request\\RemoteRequestLog.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Ssl\\Ssl.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Ssl\\SslDomain.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Statistics\\ActualStatistics.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Statistics\\ActualStatisticsId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Statistics\\Statistics.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Statistics\\StatisticsId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Statistics\\Usage.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Stats\\DatacenterTraffic.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Storage\\Server.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Storage\\Storage.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Storage\\StorageAddOn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Storage\\StorageIdentifier.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Storage\\StoragePlan.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Storage\\StoragePlanId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Storage\\StorageServerId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Tariff\\Credit.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Tariff\\CreditHistory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Tariff\\Tariff.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Tariff\\TariffId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\Tariff\\TariffLog.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Entity\\TikTok\\ChangeRequest.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\Absurd.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\BucketStatsNotAvailable.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\CannotUpdatePaidInvoice.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\Cdn\\CdnOriginConfigurationFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\ClapDomainException.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\Currency\\CurrencyConversionFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\Customer\\CustomerIsSuspended.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\Customer\\CustomerIsTerminated.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\Customer\\CustomerMailNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\Customer\\CustomerNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\Customer\\CustomerPaymentSettingsNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\Encryption\\CredentialsEncryptionFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\ExchangeRateNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\InvalidBpsValue.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\MailLogNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\NotImplemented.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\OAuthTokenNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\ObjectStorage\\AccessKeyNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\ObjectStorage\\CredentialsCreationFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\ObjectStorage\\InvalidBucketName.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\ObjectStorage\\ObjectStorageConfigurationFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\ObjectStorage\\ObjectStorageUserNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\ObjectStorage\\PolicyConfigurationFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\ObjectStorage\\PolicyNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\Origin\\InvalidHostFormat.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\RgwClusterNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\SessionNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\SupportsErrorType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\SupportsExceptionLogging.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\SupportsFieldErrorResponse.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Exception\\SupportsHttpResponse.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Finder\\Cdn\\CdnTrafficSumFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Finder\\Cname\\CnameCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Finder\\Cname\\CnameFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Finder\\MailLogsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Finder\\ServerIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\GitLab\\GitLab.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Hurricane\\Credentials.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Hurricane\\HurricaneApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Hurricane\\Tsunami\\ActiveDdos.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Hurricane\\Tsunami\\FailedTarget.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Hurricane\\Tsunami\\KeyCertificatePair.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Hurricane\\Tsunami\\ProtectionLevel.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Hurricane\\Tsunami\\SuccessfulTarget.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Hurricane\\Tsunami\\Target.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Hurricane\\Tsunami\\TsunamiConfig.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Hurricane\\Tsunami\\TsunamiRequestFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Hurricane\\Tsunami\\TsunamiResult.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\JWT\\SignInTokenGenerator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Logger\\CronLogger.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\MC\\Dto\\Anycast.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\MC\\Dto\\Location.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\MC\\MCApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Messaging\\AuthorizedCommand.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Messaging\\Command.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Messaging\\CommandBuffer\\CommandBufferExecutor.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Messaging\\CommandBuffer\\CommandBufferUpdater.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Messaging\\CommandBuffer\\Dto\\BufferedCommand.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Messaging\\CommandBuffer\\Finder\\BufferedCommandFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Messaging\\CommandBuffer\\Value\\Topic.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Messaging\\CommandBuffer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Messaging\\CommandBus.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Messaging\\CommandEnvelope.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Messaging\\CommandHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Messaging\\CommandProducer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Messaging\\Exception\\CommandClassNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Messaging\\Exception\\CommandInterfaceNotImplemented.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Messaging\\Exception\\FlushFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Messaging\\Query.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Messaging\\QueryBus.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Messaging\\QueryHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Messaging\\SerializableCommand.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Netcraft\\Api.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Netcraft\\Value\\Threat.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Netcraft\\Value\\Threats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Netcraft\\Value\\ThreatType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\NxgApi\\Command\\SendProxyRequest.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\NxgApi\\Command\\SendProxyRequestHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\NxgApi\\Dto\\NxgApiProxyRequest.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\NxgApi\\Dto\\SslCertificate.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\NxgApi\\Exception\\NxgApiRequestFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\NxgApi\\Exception\\NxgRequestFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\NxgApi\\NxgApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\NxgApi\\NxgApiProxy.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Origin\\Exception\\OriginIsInvalid.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Origin\\Exception\\OriginNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Origin\\Exception\\OriginUrlIsForbidden.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Origin\\Exception\\OriginUrlIsInvalid.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Origin\\OriginRemover.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Origin\\OriginResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\Dto\\Deal.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\Dto\\Lead.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\Dto\\NewDealData.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\Dto\\NewLeadData.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\Dto\\NewPersonData.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\Dto\\Person.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\Dto\\SearchDealsScope.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\Dto\\SearchLeadsScope.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\Dto\\UpdatedDealData.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\Enum\\DealSearchField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\Enum\\LeadSearchField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\Exception\\CannotAddDeal.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\Exception\\CannotAddLead.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\Exception\\CannotAddPerson.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\Exception\\CannotArchiveLead.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\Exception\\CannotDeleteLead.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\Exception\\CannotDeletePerson.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\Exception\\CannotSearchDeals.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\Exception\\CannotSearchLeads.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\Exception\\CannotUpdateDeal.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\PipedriveApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\Value\\DealCustomFieldId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\Value\\DealId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\Value\\FieldNotSet.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\Value\\LeadId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\Value\\LeadLabelId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\Value\\PipelineId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pipedrive\\Value\\StageId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Plan\\CustomPlanTerminator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Plan\\MonthlyPlanTerminator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Plan\\PlanHelper.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Postmark\\Contract\\PostmarkMessageId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Postmark\\CustomerMailLimiter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Postmark\\Exception\\CannotGetMailDetails.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Postmark\\Exception\\MailCouldNotBeSent.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Postmark\\MailLogger.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Postmark\\Postmark.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Postmark\\SetupCustomerMail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Postmark\\Value\\MailDetails.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Postmark\\Value\\MailStatus.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Postmark\\Value\\MailTemplates\\CustomPlanInvoice.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Postmark\\Value\\MailTemplates\\CustomPlanInvoiceWithPaymentLink.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Postmark\\Value\\MailTemplates\\CustomPlanPaymentLink.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Postmark\\Value\\PostmarkMail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Postmark\\Value\\PostmarkTemplateId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Postmark\\Value\\SentMail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Postmark\\Value\\Signature.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Postmark\\Value\\TemplateVariables.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Profiling\\PerformanceProfiler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Pruner\\TablePruner.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\PushZone\\PushZoneApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Query\\FindMailLogs\\FindMailLogs.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Query\\FindMailLogs\\FindMailLogsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Query\\FindMailLogs\\Input\\Filter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Query\\FindMailLogs\\Input\\SelectionField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Query\\FindMailLogs\\Output\\MailLog.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Query\\FindMailLogs\\Output\\MailLogs.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Query\\GetAuthenticatedCustomerFromPersonalToken.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Query\\GetAuthenticatedCustomerFromPersonalTokenHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Query\\GetCustomerFromApplicationToken.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Query\\GetCustomerFromApplicationTokenHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Query\\GetCustomerFromSessionToken.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Query\\GetCustomerFromSessionTokenHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\RateLimit\\RateLimitChecker.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\RateLimit\\RateLimitCheckResult.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\RateLimit\\RateLimitedBy.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\RateLimit\\RateLimiter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\RateLimit\\RateLimitId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\RateLimit\\RateLimitResult.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Repository\\AraQueueRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Repository\\Cdn\\CdnHttpRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Repository\\Cdn\\CdnRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Repository\\Cname\\CnameRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Repository\\Customer\\AccountFlagsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Repository\\Customer\\AccountSettingsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Repository\\Customer\\CustomerCdnSettingsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Repository\\Customer\\CustomerMailRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Repository\\Customer\\CustomerPaymentSettingsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Repository\\Customer\\KayakoTicketRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Repository\\Customer\\NoteRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Repository\\CustomerRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Repository\\ExchangeRateRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Repository\\MailLogDetailsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Repository\\MonthlyTrafficPlan\\MonthlyTrafficPlanRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Repository\\OAuthTokenRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Repository\\ObjectStorage\\AccessKeyRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Repository\\ObjectStorage\\RgwClusterRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Repository\\ObjectStorage\\RgwPricingRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Repository\\Origin\\OriginRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Repository\\RealTimeLog\\RealTimeLogCdnRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Repository\\RealTimeLog\\RealTimeLogRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Repository\\SessionRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Repository\\SslRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Repository\\Storage\\StorageRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Repository\\Tariff\\TariffRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Repository\\TeamMember\\TeamMemberAccessConfigurationRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Resolver\\AffectedCustomerResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Resolver\\CustomerMaxAgeResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Resolver\\RgwClusterResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Server\\Exception\\ResolvingServerIdsFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Server\\GroupServerIdResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Session\\SessionFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Session\\SessionInvalidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Slack\\Slack.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Slack\\SlackChannelLinkBuilder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Slack\\Value\\SlackChannel.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Slack\\Value\\SlackChannelName.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Slack\\Value\\SlackMessage.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Stats\\StatsGroupBy.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Stats\\StatsProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Stats\\StatsProviderBag.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Stats\\StatsSourceSelector.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\TeamMember\\TeamMemberAccessConfigurator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\TeamMember\\TeamMemberAccessibleOriginsResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Validation\\CdnAccessValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Validation\\OriginUrlValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Validation\\StorageAccessValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Validation\\TeamMemberAccessValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\AccessRestriction\\AccessType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\AccessRestriction\\ResourceRestriction.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\AccessRestriction\\ResourceRestrictionType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\AggregationUnit.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\BitsPerSecond.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Bytes.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Cidr.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\ClientExceptionResponseSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Customer\\EmailAddress.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Customer\\Name.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Customer\\PlainPassword.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Customer\\ScheduledSuspensionStatus.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Customer\\SuspensionReason.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Customer\\UserRole.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Datacenter\\LocationType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\EncryptedValue.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Enums\\EnumSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Enums\\EnumValues.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Enums\\NullableEnum.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Enums\\NullableIntEnum.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Enums\\NullableStringEnum.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\ErrorType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Fingerprint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\HttpHeader.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Identifier\\IdentifierMapper.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Identifier\\IntegerIdentifier.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Identifier\\StringIdentifier.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Identifier\\UuidIdentifier.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\IpAddress.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\ItemSubject.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Label.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Messaging\\CommandId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Note.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\OAuthServiceName.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\ObjectStorage\\BucketName.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\ObjectStorage\\DiskType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\ObjectStorage\\EncryptedObjectStorageCredentials.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\ObjectStorage\\ObjectStorageCredentials.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\ObjectStorage\\ObjectStorageUsage.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\ObjectStorage\\ObjectStorageUserName.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Origin\\BaseDirectory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Origin\\CdnOrigins.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Origin\\ConnectionType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Origin\\Host.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Origin\\ObjectStorageType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Origin\\OriginPriority.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Origin\\Port.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Origin\\Ssl.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Origin\\Status.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\OriginScheme.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\PrefetchMode.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\RequestSource.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Seconds.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\SignInToken.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\SortDirection.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\TableAlias.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\Timestamp.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Value\\XeroType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Domain\\Xero\\OpenIdAuthenticator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\EventListener\\ConsoleCommandListener.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\EventListener\\ConsoleCommandStartTimeHolder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\EventListener\\ConsoleMonitorListener.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Ara\\FakeAra.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Ara\\HttpAra.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Ara\\Provider\\AraStatsProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Billing\\BillerFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Billing\\FakeBiller.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Billing\\GbpXeroBiller.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Billing\\UsdXeroBiller.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Billing\\Xero\\InvoiceItemFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Billing\\Xero\\InvoiceItemTrackingFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Billing\\Xero\\TrackingCategory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Ceph\\FakeRgwAdminApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Ceph\\FakeS3ClientApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Ceph\\RgwAdminApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Ceph\\S3ClientApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Ceph\\S3ClientFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\ClickHouse\\CephHttpClientFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\ClickHouse\\ClickHouseAsyncClient.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\ClickHouse\\ClickHouseCephClient.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\ClickHouse\\ClickHouseCephClientFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\ClickHouse\\ClickHouseClient.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\ClickHouse\\ClickHouseClientFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\ClickHouse\\ClickHouseLiveStreamingClient.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\ClickHouse\\ClickHouseLiveStreamingClientFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\ClickHouse\\Exception\\HostResolutionFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\ClickHouse\\HttpClientFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\ClickHouse\\LiveStreamingHttpClientFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\ClickHouse\\Provider\\ClickHouseLiveStreamingStatsProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\ClickHouse\\Provider\\ClickHouseStatsProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\ClickHouse\\QueryLogger.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\ClickHouse\\RealTimeLog\\ClickHouseClient.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\ClickHouse\\RealTimeLog\\ClickHouseClientFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\ClickHouse\\RealTimeLog\\HttpClientFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Console\\NoLockAcquirer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Console\\SemaphoreLockAcquirer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Currency\\BrickCurrencyConverter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Currency\\ExchangeRateProviderFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Currency\\FakeCurrencyConverter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\DbalCommandProducer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\DbalConnectionConstructor.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Doctrine\\Dbal\\DbalConnection.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\EntityManagerConstructor.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Factory\\OpenIdClientFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Finder\\Cdn\\DbalCdnTrafficSumFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Finder\\Cname\\DbalCnameCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Finder\\Cname\\DoctrineCnameFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Finder\\DbalMailLogsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Finder\\DbalServerIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\GitLab\\Endpoint\\V4TriggerPipeline.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\GitLab\\HttpGitLab.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Http\\DbalRemoteRequestLogger.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Http\\Dto\\RemoteRequestLog.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Http\\LoggerPlugin.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Http\\RemoteRequestLogBuffer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Http\\RemoteRequestLogFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Http\\RemoteRequestLogger.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Http\\UriObfuscator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\HttpClient\\Authentication\\Authentication.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\HttpClient\\Authentication\\BasicAuthentication.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\HttpClient\\Authentication\\BearerTokenAuthentication.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\HttpClient\\Authentication\\NoAuthentication.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\HttpClient\\Client.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\HttpClient\\Endpoint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\HttpClient\\HasHeaders.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\HttpClient\\HasPayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\HttpClient\\HasQueryParameters.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\HttpClient\\IsServerRequest.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\HttpClient\\RemoteCallFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Hurricane\\EnableTsunamiEndpoint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Hurricane\\Endpoint\\ActiveDdosListEndpoint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Hurricane\\Endpoint\\ChangeProtectionLevelEndpoint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Hurricane\\Endpoint\\DisableTsunamiEndpoint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Hurricane\\FakeHurricaneApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Hurricane\\HttpHurricaneApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Hurricane\\HurricaneClientProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Logger\\DbalCronLogger.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\MC\\HttpMCApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Messaging\\DbalCommandBufferUpdater.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Messaging\\Finder\\DbalBufferedCommandFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Netcraft\\Endpoint\\V1ExportThreads.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Netcraft\\HttpApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\NxgApi\\FakeNxgApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\NxgApi\\HttpNxgApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\NxgApi\\HttpNxgApiProxy.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\NxgApi\\OriginsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\NxgApi\\ResourceFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Pipedrive\\AddDealEndpoint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Pipedrive\\AddLeadEndpoint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Pipedrive\\AddPersonEndpoint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Pipedrive\\DeleteLeadEndpoint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Pipedrive\\DeletePersonEndpoint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Pipedrive\\HttpPipedriveApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Pipedrive\\SearchDealsEndpoint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Pipedrive\\SearchLeadsEndpoint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Pipedrive\\UpdateDealEndpoint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Pipedrive\\UpdateLeadEndpoint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Postmark\\DatabaseCustomerMailLimiter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Postmark\\DatabaseMailLogger.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Postmark\\FakePostmark.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Postmark\\SdkPostmark.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Profiling\\BlackfirePerformanceProfiler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Pruner\\DbalTablePruner.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\PushZone\\HttpPushZoneApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\RateLimit\\SymfonyRateLimiter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Repository\\Cdn\\DoctrineCdnHttpRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Repository\\Cdn\\DoctrineCdnRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Repository\\Cname\\DoctrineCnameRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Repository\\Customer\\DoctrineAccountFlagsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Repository\\Customer\\DoctrineAccountSettingsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Repository\\Customer\\DoctrineCustomerCdnSettingsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Repository\\Customer\\DoctrineCustomerMailRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Repository\\Customer\\DoctrineCustomerPaymentSettingsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Repository\\Customer\\DoctrineKayakoTicketRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Repository\\Customer\\DoctrineNoteRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Repository\\DoctrineAraQueueRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Repository\\DoctrineCustomerRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Repository\\DoctrineExchangeRateRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Repository\\DoctrineMailLogDetailsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Repository\\DoctrineOAuthTokenRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Repository\\DoctrineSessionRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Repository\\DoctrineSslRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Repository\\MonthlyTrafficPlan\\DoctrineMonthlyTrafficPlanRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Repository\\ObjectStorage\\DoctrineAccessKeyRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Repository\\ObjectStorage\\DoctrineRgwClusterRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Repository\\ObjectStorage\\DoctrineRgwPricingRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Repository\\Origin\\DoctrineOriginRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Repository\\RealTimeLog\\DoctrineRealTimeLogCdnRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Repository\\RealTimeLog\\DoctrineRealTimeLogRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Repository\\Storage\\DoctrineStorageRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Repository\\Tariff\\DoctrineTariffRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Repository\\TeamMember\\DoctrineTeamMemberAccessConfigurationRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Slack\\HttpSlack.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Symfony\\SymfonyLoggedAccountProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Tactician\\TacticianCommandBus.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Tactician\\TacticianQueryBus.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Validation\\DatabaseCdnAccessValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Validation\\DatabaseStorageAccessValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Validation\\DbalTeamMemberAccessValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Core\\Infrastructure\\Xero\\XeroOpenIdAuthenticator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\ClickHouse\\Rounder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\DateTime\\SafeDateTimeImmutable.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Doctrine\\Dbal\\Middleware\\Configuration\\ApplicationNamePostConnectConfiguration.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Doctrine\\Dbal\\Middleware\\Configuration\\ConnectionApplicationNameProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Doctrine\\Dbal\\Middleware\\Configuration\\PostConnectConfiguration.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Doctrine\\Dbal\\Middleware\\Configuration\\Value\\ApplicationName.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Doctrine\\Dbal\\Middleware\\PostConnectConfiguringDriver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Doctrine\\Dbal\\Middleware\\PostConnectConfiguringMiddleware.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Doctrine\\Dbal\\Types\\ArrayStringType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Doctrine\\Dbal\\Types\\IntervalType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Doctrine\\Dbal\\Types\\RawJsonType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Doctrine\\Dbal\\Types\\Types.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\GeoIp\\GeoIpDatabaseReader.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\JMS\\EnumHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\JMS\\SetHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\JMS\\SymfonyUuidHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Money\\ContextFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Money\\CurrencyCode.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Money\\MoneyFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Money\\MoneyHelper.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Money\\PricePerByteConverter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Reflection\\Reflection.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Symfony\\KernelContainerConfigurator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Symfony\\KernelRoutesConfigurator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Symfony\\UuidFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Tactician\\Callback\\DeferredCallbackBuffer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Tactician\\Middleware\\AuthorizationMiddleware.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Tactician\\Middleware\\ChangeLogMiddleware.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Tactician\\Middleware\\CommandBufferMiddleware.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Tactician\\Middleware\\DeferredCallbackMiddleware.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Tactician\\Middleware\\DoctrineClearMiddleware.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Tactician\\Middleware\\DoctrineTransactionMiddleware.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Tactician\\Middleware\\RequestLogBufferMiddleware.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Tactician\\Middleware\\WithMutex.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Utils\\StringTruncator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Validation\\FQDNValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\Validation\\Regex.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\ValueResolver\\IdentifierValueResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CoreLibrary\\ValueResolver\\IpAddressValueResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Currency\\Application\\Console\\FetchExchangeRates.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Currency\\Domain\\Api\\CurrencyApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Currency\\Domain\\Command\\FetchExchangeRates.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Currency\\Domain\\Command\\FetchExchangeRatesHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Currency\\Domain\\Exception\\FailedToFetchExchangeRate.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Currency\\Domain\\Repository\\ExchangeRateRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Currency\\Infrastructure\\Api\\Endpoint\\MidMarketConvertFrom.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Currency\\Infrastructure\\Api\\HttpXECurrencyApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Currency\\Infrastructure\\Repository\\DoctrineExchangeRateRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Console\\CleanupTeamMemberInvitations.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Console\\CustomersInDebtCommand.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Console\\DeleteTestingCustomerCommand.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Console\\ProcessScheduledSuspension.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Console\\SendFirstCustomerTrafficNotificationsCommand.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\AddFromSignUpInviteController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\ChangePasswordController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\ConfirmDeprecationNoticeController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\ConfirmEmailAddressController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\CreditBalanceController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\CreditBalanceHistoryController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\CreditInfoController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\CreditOperationsHistoryController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\CustomerInviteDetailController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\CustomerResendVerificationEmailController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\DetailController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\EditController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\EditCustomerSettingsController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\InternalDetailController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\InternalSuspendController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\PasswordResetRequestController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\ResendVerificationEmailController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\ResetPasswordController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\RoleController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\StreamingPanelLoginDataController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\SuspendController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\TeamMember\\AccessConfigurationListController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\TeamMember\\AddAccessConfigurationController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\TeamMember\\AddTeamMemberController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\TeamMember\\BulkSendTeamMemberSignUpInvitesController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\TeamMember\\DeleteAccessConfigurationController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\TeamMember\\DeleteTeamMemberController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\TeamMember\\EditAccessConfigurationListController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\TeamMember\\ResendTeamMemberInviteController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\TeamMember\\SuspendTeamMemberController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\TeamMember\\TeamMemberListController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\TeamMember\\UnsuspendTeamMemberController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\UnsubscribeFromMailingController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\UnsuspendController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Controller\\VerifyPasswordController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\AccountInfoSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\BillingSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\CdnSettingsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\ChangeCustomerPasswordSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\ConfigurationSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\CredentialsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\CreditBalanceHistorySchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\CreditInfoSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\CreditOperationSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\CreditOperationsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\CurrentCreditSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\DetailSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\Dto\\EditedCustomer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\EditCustomerSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\EmailAddressSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\InternalDetailSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\InternalSuspendCustomerSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\InvitedCustomerSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\InviteDetailSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\NewPasswordSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\NewTeamMemberCredentialsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\NewTeamMemberSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\ResourceRestrictionSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\RoleSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\SpendingStatsConfigurationSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\StreamingPanelLoginDataSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\SuspendCustomerSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\TeamMember\\AccessConfigurationSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\TeamMember\\AccessConfigurationsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\TeamMember\\AccessRestrictionsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\TeamMember\\EditAccessConfigurationsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\TeamMember\\NewTeamMemberAccessConfigurationSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\TeamMember\\SignUpInviteesSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\TeamMember\\SignUpInviteSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\TeamMemberAccessConfigurationSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\TeamMemberListSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\TeamMemberSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\TrialSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\UnsuspendCustomerSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\Validator\\SchemaPasswordPropertyValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Application\\Payload\\VerifyCustomerPasswordSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Contract\\Type\\MailingType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\ApiPasswordEncryptor.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\ClientLinkGenerator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\AddCustomerFromInvite.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\AddCustomerFromInviteHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\AddCustomerMail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\AddCustomerMailHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\AddNote.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\AddNoteHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\ChangeCustomerPassword.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\ChangeCustomerPasswordHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\ConfirmDeprecationNotice.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\ConfirmDeprecationNoticeHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\ConfirmEmailAddress.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\ConfirmEmailAddressHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\DeleteCustomerMail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\DeleteCustomerMailHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\DeleteTestingCustomers.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\DeleteTestingCustomersHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\EditCustomerSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\EditCustomerSettingsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\EditNote.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\EditNoteHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\NotifyOnFirstCustomerTraffic.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\NotifyOnFirstCustomerTrafficHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\Pipedrive\\AddLeadToPipedrive.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\Pipedrive\\AddLeadToPipedriveHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\Pipedrive\\ArchivePipedriveLeads.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\Pipedrive\\ArchivePipedriveLeadsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\Pipedrive\\ConvertPipedriveLeadsToWonDeal.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\Pipedrive\\ConvertPipedriveLeadsToWonDealHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\ProcessPasswordResetRequest.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\ProcessPasswordResetRequestHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\RemoveNote.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\RemoveNoteHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\ResendVerificationEmail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\ResendVerificationEmailHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\ResetPassword.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\ResetPasswordHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\ScheduleSuspendCustomer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\ScheduleSuspendCustomerHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\SendCustomersInDebtReport.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\SendCustomersInDebtReportHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\SendSignUpInvite.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\SendSignUpInviteHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\SendTwoFactorAuthSetupEmailNotification.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\SendTwoFactorAuthSetupEmailNotificationHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\SuspendCustomer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\SuspendCustomerHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\SuspendPendingSuspensions.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\SuspendPendingSuspensionsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\TeamMember\\AddTeamMember.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\TeamMember\\AddTeamMemberAccessConfiguration.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\TeamMember\\AddTeamMemberAccessConfigurationHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\TeamMember\\AddTeamMemberHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\TeamMember\\BulkSendTeamMemberSignUpInvite.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\TeamMember\\BulkSendTeamMemberSignUpInviteHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\TeamMember\\DeleteTeamMember.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\TeamMember\\DeleteTeamMemberAccessConfiguration.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\TeamMember\\DeleteTeamMemberAccessConfigurationHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\TeamMember\\DeleteTeamMemberHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\TeamMember\\EditTeamMemberAccessConfiguration.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\TeamMember\\EditTeamMemberAccessConfigurationHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\TeamMember\\EditTeamMemberAccessConfigurations.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\TeamMember\\EditTeamMemberAccessConfigurationsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\TeamMember\\RemoveTeamMembersWithExpiredInvite.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\TeamMember\\RemoveTeamMembersWithExpiredInviteHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\TeamMember\\ResendTeamMemberSignUpInvite.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\TeamMember\\ResendTeamMemberSignUpInviteHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\TeamMember\\SuspendTeamMember.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\TeamMember\\SuspendTeamMemberHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\TeamMember\\UnsuspendTeamMember.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\TeamMember\\UnsuspendTeamMemberHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\UnsubscribeCustomerFromMailing.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\UnsubscribeCustomerFromMailingHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\UnsuspendCustomer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\UnsuspendCustomerHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\UpdateCustomer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\UpdateCustomerCdnSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\UpdateCustomerCdnSettingsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\UpdateCustomerHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\UpdateCustomerProfileInfo.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\UpdateCustomerProfileInfoHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\VerifyCustomerPassword.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Command\\VerifyCustomerPasswordHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\CustomerIdFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Dto\\AccessConfiguration.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Dto\\BillingDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Dto\\BulkTeamInvite.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Dto\\CdnSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Dto\\CreditOperation.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Dto\\CustomerCdnSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Dto\\CustomerConfiguration.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Dto\\CustomerCreditInfo.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Dto\\CustomerDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Dto\\CustomerIdentifiers.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Dto\\CustomerInvite.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Dto\\CustomerPasswordChange.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Dto\\CustomerPasswordVerify.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Dto\\CustomerSuspensionDetails.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Dto\\CustomerUnsuspensionDetails.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Dto\\InternalAccountInfo.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Dto\\InternalCustomerDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Dto\\InvitedCustomer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Dto\\NewPassword.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Dto\\NewTeamMember.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Dto\\SpendingStatsConfiguration.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Dto\\StreamingPanelLoginData.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Dto\\SuspendDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Dto\\TeamInvite.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Dto\\TeamMember.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Dto\\TrialDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\EmailAddressConfirmationLinkSender.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Enum\\CustomerBillingStatus.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Enum\\CustomerSlug.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Enum\\Rating.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\AccountFlagsNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\AccountQuotaExceeded.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\AccountSignUpInviteNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\CannotAccessStreamingPanel.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\ChangePasswordRateLimitExceeded.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\CustomerOriginSettingsNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\CustomerPasswordChangeFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\CustomerProfileUpdateFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\CustomerSettingsNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\CustomerSuspensionFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\CustomerTrafficNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\CustomerUnsuspensionFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\EmailAddressConfirmationFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\EmailAddressConfirmationNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\EmailAddressIsAlreadyConfirmed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\InvalidCustomerSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\InvalidEstimatedTrafficRangeFormat.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\InvalidPasswordResetRequest.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\InvalidTeamMemberAccessConfiguration.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\InvitationSignUpFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\InviteCountLimitExceeded.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\NoteNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\PaymentAmountIsTooSmall.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\ResendFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\ScheduledSuspensionNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\SentMailRateLimitExceeded.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\SignUpFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\SignUpInviteWasUsed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\TeamMemberAccessConfigurationNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\UnableToAddTeamMember.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\UnableToDeleteTeamMember.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\UnableToInviteTeamMember.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\UnsubscribeFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Exception\\VerifyPasswordRateLimitExceeded.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Finder\\BillingRegionFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Finder\\CustomerCdnSettingsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Finder\\CustomerFlagsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Finder\\CustomerIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Finder\\CustomerInDebtFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Finder\\CustomerMailsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Finder\\CustomerPaymentSettingsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Finder\\CustomerPlanPriceFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Finder\\CustomerSettingsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Finder\\CustomersFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Finder\\LoginLogsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Finder\\RequestCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Finder\\SignUpInviteCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Finder\\TeamMemberAccessConfigurationFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Finder\\TeamMemberCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\KoxLinkGenerator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Limit\\InviteLimiter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Limit\\PasswordChangeRequestRateLimiter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Limit\\PasswordVerifyRequestRateLimiter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Limit\\ResetPasswordRequestLimiter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Mail\\CustomersInDebtNotification.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Mail\\SetupTeamMember2FANotification.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\PasswordHasher.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerCdnSettings\\FindCustomerCdnSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerCdnSettings\\FindCustomerCdnSettingsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerCdnSettings\\Input\\Filter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerCdnSettings\\Input\\SelectionField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerCdnSettings\\Output\\CustomerCdnSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerCdnSettings\\Output\\CustomerCdnSettingsCollection.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerFlags\\FindCustomerFlags.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerFlags\\FindCustomerFlagsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerFlags\\Input\\Filter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerFlags\\Input\\SelectionField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerFlags\\Output\\CustomerFlags.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerFlags\\Output\\CustomersFlags.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerMails\\FindCustomerMails.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerMails\\FindCustomerMailsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerMails\\Input\\Filter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerMails\\Input\\SelectionField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerMails\\Output\\CustomerMail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerMails\\Output\\CustomerMails.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerPaymentSettings\\FindCustomerPaymentSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerPaymentSettings\\FindCustomerPaymentSettingsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerPaymentSettings\\Input\\Filter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerPaymentSettings\\Input\\SelectionField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerPaymentSettings\\Output\\CustomerPaymentSetting.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerPaymentSettings\\Output\\CustomerPaymentSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomers\\FindCustomers.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomers\\FindCustomersHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomers\\Input\\Filter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomers\\Input\\OrderField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomers\\Input\\SelectionField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomers\\Output\\Customer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomers\\Output\\Customers.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerSettings\\FindCustomerSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerSettings\\FindCustomerSettingsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerSettings\\Input\\Filter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerSettings\\Input\\SelectionField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerSettings\\Output\\CustomerSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerSettings\\Output\\CustomerSettingsCollection.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerTeamMembers.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindCustomerTeamMembersHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindLoginLogs\\FindLoginLogs.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindLoginLogs\\FindLoginLogsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindLoginLogs\\Input\\Filter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindLoginLogs\\Input\\SelectionField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindLoginLogs\\Output\\LoginLog.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindLoginLogs\\Output\\LoginLogs.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindTeamMemberAccessConfigurations\\FindTeamMemberAccessConfigurations.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindTeamMemberAccessConfigurations\\FindTeamMemberAccessConfigurationsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindTeamMemberAccessConfigurations\\Input\\Filter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindTeamMemberAccessConfigurations\\Input\\SelectionField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindTeamMemberAccessConfigurations\\Output\\AccessConfiguration.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\FindTeamMemberAccessConfigurations\\Output\\AccessConfigurations.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\GetCreditHistoryForCustomer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\GetCreditHistoryForCustomerHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\GetCreditOperationsHistory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\GetCreditOperationsHistoryHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\GetCustomerDetailById.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\GetCustomerDetailByIdHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\GetCustomerInvite.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\GetCustomerInviteHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\GetCustomerStreamingPanelLoginDataInfo.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\GetCustomerStreamingPanelLoginDataInfoHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\GetInternalCustomerDetailById.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Query\\GetInternalCustomerDetailByIdHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Repository\\AccountFlagsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Repository\\AccountSettingsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Repository\\AccountSignUpInviteRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Repository\\CreditHistoryRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Repository\\CreditOperationsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Repository\\CustomerCdnSettingsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Repository\\CustomerMailingUnsubscribeRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Repository\\CustomerOriginSettingsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Repository\\CustomerPaymentSettingsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Repository\\CustomerRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Repository\\CustomerTrafficRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Repository\\EmailAddressConfirmationRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Repository\\PersonalTokenRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Repository\\ResetPasswordRequestRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Repository\\ScheduledSuspensionRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Repository\\TeamMemberAccessConfigurationRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\ResetPasswordRequestsInvalidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Resolver\\CustomerBillingStatusResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Resolver\\PricingTypeResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Resolver\\SpendingStatsConfigurationResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Resolver\\TeamMemberAccessibleCdnsResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\SetupAccount.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\SignUpInviteSender.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\SuspendCustomerCdns.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\SuspendRgwAccess.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\SuspendTeamMembers.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\TeamMemberAccessConfigurator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\TeamMemberQuotaValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\TokenHasher.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\TokenInvalidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\TwoFactorAuthSetupNotifier.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\UnsubscribeLinkGenerator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\UnsuspendCustomerCdns.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\UnsuspendRgwAccess.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\UnsuspendTeamMembers.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Validation\\AccountAccessValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Validation\\SignUpInviteValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Value\\CreditOperationType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Value\\CustomerInDebtCategory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Value\\EmailAddressConfirmationHash.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Value\\EncryptedPassword.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Value\\EstimatedTraffic.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Value\\MailType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Value\\PasswordHash.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Value\\PricingType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Value\\ResetPasswordHash.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Value\\SignUpInviteHash.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Value\\TeamMemberRole.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Value\\TokenType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Value\\UnsubscribeHash.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Domain\\Value\\UuidHash.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Factory\\CustomerFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Factory\\DbalSequenceCustomerIdFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Finder\\Dbal\\DbalCustomersFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Finder\\DbalBillingRegionFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Finder\\DbalCustomerCdnSettingsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Finder\\DbalCustomerFlagsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Finder\\DbalCustomerIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Finder\\DbalCustomerInDebtFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Finder\\DbalCustomerMailsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Finder\\DbalCustomerPaymentSettingsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Finder\\DbalCustomerPlanPriceFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Finder\\DbalCustomerSettingsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Finder\\DbalLoginLogsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Finder\\DbalRequestCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Finder\\DbalSignUpInviteCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Finder\\DbalTeamMemberAccessConfigurationFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Finder\\DbalTeamMemberCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Limit\\DbalResetPasswordRequestLimiter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Repository\\DoctrineAccountFlagsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Repository\\DoctrineAccountSettingsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Repository\\DoctrineAccountSignUpInviteRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Repository\\DoctrineCreditHistoryRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Repository\\DoctrineCreditOperationsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Repository\\DoctrineCustomerCdnSettingsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Repository\\DoctrineCustomerMailingUnsubscribeRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Repository\\DoctrineCustomerOriginSettingsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Repository\\DoctrineCustomerPaymentSettingsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Repository\\DoctrineCustomerRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Repository\\DoctrineCustomerTrafficRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Repository\\DoctrineEmailAddressConfirmationRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Repository\\DoctrinePersonalTokenRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Repository\\DoctrineResetPasswordRequestRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Repository\\DoctrineScheduledSuspensionRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Customer\\Infrastructure\\Repository\\DoctrineTeamMemberAccessConfigurationRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Controller\\ActiveCustomPlanListController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Controller\\AddContractController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Controller\\AddCustomPlanController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Controller\\CloseCustomPlanController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Controller\\ContractDetailController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Controller\\ContractPaymentDetailController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Controller\\ContractsOverviewController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Controller\\CustomerCustomPlanHistoryController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Controller\\DeleteContractController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Controller\\EditContractController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Controller\\EditCustomPlanController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Controller\\NextContractController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Controller\\UsageController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\ActiveCustomPlanListSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\ActiveCustomPlanSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\AlternativeMailsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\AutoGenerateSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\AutoRenewalSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\BandwidthStatsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\CloseCustomPlanSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\ContractBaseSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\ContractDetailsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\ContractSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\ContractsOverviewSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\CustomerCustomPlanHistorySchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\CustomPlanContractDetailSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\CustomPlanSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\EditContractSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\EditCustomPlanSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\InvoiceConfigurationSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\InvoiceLineSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\InvoiceLinesSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\MailGreetingSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\NewContractSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\NewCustomPlanSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\NotesSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\PaymentConfigurationSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\PlanCustomerSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\PlanGuardianSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\PlanPeriodSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\Request\\NextContractScopeSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\Response\\ContractOverviewSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\Response\\Customer\\PaymentSettingsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\Response\\NextContractSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\Response\\UsageSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\ServicePeriodSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Application\\Payload\\TrafficStatsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\ActiveCustomPlanComparator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Command\\AddContract.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Command\\AddContractHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Command\\AddCustomPlan.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Command\\AddCustomPlanHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Command\\CloseCustomPlan.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Command\\CloseCustomPlanHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Command\\DeleteContract.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Command\\DeleteContractHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Command\\EditContract.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Command\\EditContractHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Command\\EditCustomPlan.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Command\\EditCustomPlanHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\ContractMailSender.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\ActiveCustomPlan.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\ActiveCustomPlans.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\AlternativeMails.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\BandwidthStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\CloseCustomPlanData.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\Contract\\NextContract.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\Contract\\NextContractScope.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\ContractBase.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\ContractDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\ContractDetails.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\ContractForOverview.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\ContractsForOverview.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\Customer\\PaymentSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\CustomerCustomPlanHistory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\CustomPlanDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\CustomPlanStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\DescriptionLines.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\Guardian.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\InvoiceConfiguration.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\InvoiceLine.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\InvoiceLines.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\MailGreeting.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\NewContract.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\NewCustomPlan.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\Notes.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\ObjectStorageStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\PaymentConfiguration.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\PaymentInterval.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\PlanPeriod.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\PlanVolume.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\ServicePeriod.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\TrafficStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\UpdatedContract.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Dto\\UpdatedCustomPlan.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\EstimatedTrafficCalculator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Exception\\CannotAddContract.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Exception\\CannotAddCustomPlan.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Exception\\CannotCloseCustomPlan.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Exception\\CannotDeleteContractWithInvoice.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Exception\\ContractNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Exception\\CouldNotCreateInvoice.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Exception\\CustomPlanNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Exception\\InvalidServicePeriod.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Factory\\ContractFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Factory\\CustomPlanFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Factory\\InvoiceDetailsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Factory\\InvoiceFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Factory\\PlanFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Finder\\AlternativeMailsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Finder\\CustomPlanCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\InvoiceUpdater.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\MonthlyRevenueCalculator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\NextContractGenerator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Query\\GetActiveCustomPlanList.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Query\\GetActiveCustomPlanListHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Query\\GetContractDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Query\\GetContractDetailHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Query\\GetContractPaymentDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Query\\GetContractPaymentDetailHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Query\\GetCustomerCustomPlanHistory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Query\\GetCustomerCustomPlanHistoryHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Query\\GetCustomPlansOverview.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Query\\GetCustomPlansOverviewHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Query\\GetNextContract.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Query\\GetNextContractHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Query\\GetUsage.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Query\\GetUsageHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Repository\\ContractRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Repository\\CustomPlanRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Resolver\\CustomPlanBandwidthPercentileResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Resolver\\NextContractResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Validation\\InvoiceConfigurationValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Validation\\ServicePeriodOverlappingValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Validation\\ServicePeriodValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Value\\BillingUnit.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Value\\ContractState.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Value\\OperationCode.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Domain\\Value\\PaymentPlan.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Infrastructure\\Finder\\DbalCustomPlanCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Infrastructure\\Finder\\DoctrineAlternativeMailsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Infrastructure\\Repository\\DoctrineContractRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\CustomPlan\\Infrastructure\\Repository\\DoctrineCustomPlanRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Application\\Controller\\AddLocationController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Application\\Controller\\BillingRegionController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Application\\Controller\\ListController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Application\\Controller\\StatusController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Application\\Payload\\DatacenterListSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Application\\Payload\\DatacenterRegionsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Application\\Payload\\DatacenterSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Application\\Payload\\LocationSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Application\\Payload\\NewLocationSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Application\\Payload\\Response\\BillingRegionsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Application\\Payload\\Response\\RegionSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Application\\Payload\\StatusesSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Application\\Payload\\StatusSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Console\\UpdateLocationStatus.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Console\\UpdateLocationsType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Cache\\GroupServerIdCache.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Command\\CreateLocation.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Command\\CreateLocationHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Command\\UpdateLocationStatus.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Command\\UpdateLocationStatusHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Command\\UpdateLocationsType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Command\\UpdateLocationsTypeHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Contract\\StatusId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Dto\\BillingRegions.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Dto\\DatacenterDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Dto\\DatacenterRegions.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Dto\\DatacenterStatus.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Dto\\EnabledDatacenters.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Dto\\Location.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Dto\\NewLocation.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Dto\\Server.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Enum\\StatusCode.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Enum\\StatusLabel.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Exception\\DataCentersNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Exception\\LocationNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Exception\\PopNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Finder\\DatacenterDetailFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Finder\\DataCentersFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Finder\\DatacenterStatusFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Query\\FindDataCenters\\FindDataCenters.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Query\\FindDataCenters\\FindDataCentersHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Query\\FindDataCenters\\Input\\SelectionField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Query\\FindDataCenters\\Output\\DataCenter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Query\\FindDataCenters\\Output\\DataCenters.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Query\\FindDatacenterStatuses.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Query\\FindDatacenterStatusesHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Query\\GetBillingRegions.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Query\\GetBillingRegionsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Query\\GetDatacenters.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Query\\GetDatacentersHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Repository\\LocationRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Repository\\StatusRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Updater\\StatusUpdater.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Domain\\Value\\CityCode.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Infrastructure\\Cache\\RedisGroupServerIdCache.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Infrastructure\\Finder\\DbalDatacenterDetailFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Infrastructure\\Finder\\DbalDataCentersFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Infrastructure\\Finder\\DbalDatacenterStatusFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Infrastructure\\Repository\\DoctrineLocationRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Infrastructure\\Repository\\DoctrineStatusRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Datacenter\\Infrastructure\\Updater\\DbalStatusUpdater.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Application\\Container\\DataProviderCompilerPass.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Application\\Container\\FieldFactoryCompilerPass.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Application\\Container\\TypeCompilerPass.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Application\\Controller\\GraphQLController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Application\\Controller\\SchemaController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Application\\Runtime\\DataLoaderPromiseAdapter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Application\\Runtime\\DefaultFieldResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Application\\Runtime\\ErrorFormatter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Application\\Runtime\\SchemaFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Application\\Runtime\\Server.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Application\\Runtime\\SyncPromiseAdapter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\ArrayDataLoader.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\DataLoader\\DataLoader.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\DataLoader\\DataLoaderFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\DataLoader\\DataLoaderPromiseAdapterProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\DataLoader\\DataLoaderRegistry.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\DataProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\DataProviderBag.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\DataProviderQuery.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Exception\\DataProviderNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\FieldId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Billing\\BillingOverviewDataProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Cdn\\CdnDataLoaderFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Cdn\\StreamCdnDataLoaderFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\CdnSettings\\CdnSettingsDataLoaderFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Country\\CountryDataLoaderFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Customer\\CustomerDataLoaderFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\CustomerCdnSettings\\CustomerCdnSettingsDataLoaderFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\CustomerFlags\\CustomerFlagsDataLoaderFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\CustomerMail\\CustomerMailDataLoaderFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\CustomerPaymentSettings\\CustomerPaymentSettingsDataLoaderFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\CustomerSettings\\CustomerSettingsDataLoaderFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Income\\IncomeDataProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\LoginLog\\LoginLogDataLoaderFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Billing\\EditCredit.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Billing\\EditCreditInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Billing\\RecountCreditMutationFieldFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Cdn\\CdnPayloadDto.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Cdn\\CdnPayloadField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Cdn\\EditCdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Cdn\\EditCdnInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Cdn\\EditCdnPayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Cdn\\EditStreamCdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Cdn\\EditStreamCdnInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Cdn\\EditStreamCdnPayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Cdn\\EditStreamCdnPayloadField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Cdn\\Input\\CacheInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Cdn\\MutateCdnPayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Cdn\\MutateStreamCdnPayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Cdn\\StreamCdnPayloadDto.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CdnSettings\\CdnSettingsPayloadDto.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CdnSettings\\CdnSettingsPayloadField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CdnSettings\\EditCdnSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CdnSettings\\EditCdnSettingsInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CdnSettings\\EditCdnSettingsPayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CdnSettings\\MutateCdnSettingsInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CdnSettings\\MutateCdnSettingsPayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Customer\\CustomerPayloadDto.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Customer\\CustomerPayloadField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Customer\\EditCustomer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Customer\\EditCustomerInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Customer\\EditCustomerPayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Customer\\MutateCustomerInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Customer\\MutateCustomerPayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerCdnSettings\\CustomerCdnSettingsPayloadDto.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerCdnSettings\\CustomerCdnSettingsPayloadField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerCdnSettings\\EditCustomerCdnSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerCdnSettings\\EditCustomerCdnSettingsInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerCdnSettings\\EditCustomerCdnSettingsPayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerCdnSettings\\MutateCustomerCdnSettingsInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerCdnSettings\\MutateCustomerCdnSettingsPayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerMail\\AddCustomerMail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerMail\\AddCustomerMailInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerMail\\CustomerMailPayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerMail\\CustomerMailPayloadDto.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerMail\\CustomerMailPayloadField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerMail\\RemoveCustomerMail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerPaymentSettings\\CustomerPaymentSettingsPayloadDto.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerPaymentSettings\\CustomerPaymentSettingsPayloadField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerPaymentSettings\\EditCustomerPaymentSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerPaymentSettings\\EditCustomerPaymentSettingsInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerPaymentSettings\\EditCustomerPaymentSettingsPayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerPaymentSettings\\MutateCustomerPaymentSettingsInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerPaymentSettings\\MutateCustomerPaymentSettingsPayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerSettings\\CustomerSettingsPayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerSettings\\CustomerSettingsPayloadDto.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerSettings\\CustomerSettingsPayloadField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerSettings\\EditCustomerSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerSettings\\EditCustomerSettingsInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Mutation.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Note\\AddNote.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Note\\AddNoteInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Note\\EditNote.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Note\\EditNoteInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Note\\NotePayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Note\\NotePayloadDto.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Note\\NotePayloadField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Note\\RemoveNote.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\ScheduleCustomerSuspension\\ScheduleCustomerSuspension.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\ScheduleCustomerSuspension\\ScheduleCustomerSuspensionInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\SendSignUpInvite\\SendSignUpInvite.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\SendSignUpInvite\\SendSignUpInviteInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\TeamMemberAccessConfiguration\\AddConfiguration.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\TeamMemberAccessConfiguration\\AddConfigurationInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\TeamMemberAccessConfiguration\\AddConfigurationPayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\TeamMemberAccessConfiguration\\ConfigurationPayloadDto.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\TeamMemberAccessConfiguration\\ConfigurationPayloadField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\TeamMemberAccessConfiguration\\DeleteConfiguration.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\TeamMemberAccessConfiguration\\EditConfiguration.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\TeamMemberAccessConfiguration\\EditConfigurationInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\TeamMemberAccessConfiguration\\EditConfigurationPayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\TeamMemberAccessConfiguration\\MutateConfigurationInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\TeamMemberAccessConfiguration\\MutateConfigurationPayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Note\\NoteDataLoaderFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Origin\\OriginDataLoaderFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Billing\\BillingOverviewFieldFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Cdn\\CdnEdge.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Cdn\\CdnFieldFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Cdn\\CdnOrdering.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Cdn\\CdnsConnection.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Cdn\\CdnsFieldFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Cdn\\CdnsFilterInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Cdn\\CdnSort.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Cdn\\StreamCdnFieldFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\CdnSettings\\CdnSettingsFieldFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Customer\\CustomerEdge.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Customer\\CustomerFieldFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Customer\\CustomerOrdering.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Customer\\CustomersConnection.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Customer\\CustomersFieldFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Customer\\CustomersFilterInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Customer\\CustomerSort.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\CustomerCdnSettings\\CustomerCdnSettingsFieldFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\DataCenter\\DataCentersFieldFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Income\\DailyIncomeFieldFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Income\\RevenueFieldFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Kayako\\KayakoTicketsFieldFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\MailLog\\MailLogConnection.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\MailLog\\MailLogEdge.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\MailLog\\MailLogsFieldFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\MailLog\\MailLogsFilterInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Notes\\NotesFieldFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Origin\\OriginConnection.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Origin\\OriginEdge.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Origin\\OriginOrdering.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Origin\\OriginsFieldFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Origin\\OriginsFilterInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Origin\\OriginSort.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Origin\\StreamOriginsFieldFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Payment\\PaymentsFieldFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Payment\\PaymentsFilterInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Plan\\PlanEdge.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Plan\\PlansConnection.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Plan\\PlansFieldFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Plan\\PlansFilterInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Query.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\RealTimeLog\\RealTimeLogQueryFieldFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\RealTimeLog\\RealTimeLogsQueryFieldFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Stats\\OverviewChartStatsFieldFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Stats\\OverviewStatsFieldFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Stats\\OverviewStatsFilterInput.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\TeamMemberAccessConfiguration\\ConfigurationFieldFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\Query\\TeamMemberAccessConfiguration\\ConfigurationsFieldsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Type\\TeamMemberAccessConfiguration\\TeamMemberAccessConfigurationDataLoaderFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Validation\\InvalidArgument.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\DataResolution\\Validation\\OperationAssertion.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Exception\\InvalidType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Finder\\IncomeFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Operation\\FieldSelectionExtractor.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Operation\\PathNormalize.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Builder\\EnumBuilder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Factory\\FieldFactoryBag.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Factory\\MutationFieldFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Factory\\QueryFieldFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Billing\\BillingOverview.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Billing\\Dto\\BillingOverview.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Cdn\\Cdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Cdn\\StreamCdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Cdn\\StreamProtocol.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\CdnSettings\\CdnSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Connection\\Connection.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Connection\\ConnectionFieldsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Connection\\ConnectionType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Connection\\Dto\\Connection.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Connection\\Dto\\Edge.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Connection\\Dto\\PageInfo.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Connection\\Edge.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Connection\\EdgeType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Connection\\Node.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Connection\\PageInfo.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Connection\\SetupEdgeNode.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Country\\Country.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Customer\\Customer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Customer\\CustomerRating.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\CustomerCdnSettings\\CustomerCdnSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\CustomerCdnSettings\\PrefetchMode.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\CustomerFlags\\CustomerFlags.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\CustomerMail\\CustomerMail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\CustomerMail\\Dto\\CustomerMail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\CustomerMail\\MailType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\CustomerPaymentSettings\\CustomerPaymentSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\CustomerPaymentSettings\\MonthlyPlanGroup.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\CustomerSettings\\CustomerSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\DataCenter\\DataCenter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Dto\\DataTransferObject.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Dto\\DefaultDtoMapperProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Income\\Dto\\Income.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Income\\Income.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Income\\Revenue.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Kayako\\KayakoDepartment.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Kayako\\KayakoStatus.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Kayako\\KayakoTicket.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\LoginLog\\LoginLog.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\MailLog\\MailLog.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Money\\Currency.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Money\\Money.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Notes\\Note.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Ordering\\Direction.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Ordering\\Ordering.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Ordering\\Sort.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Origin\\Origin.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Origin\\OriginScheme.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Origin\\StreamOrigin.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Payment\\Payment.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Payment\\RequestSource.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Plan\\Plan.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Plan\\PlanType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Plan\\PricingPlan.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\RealTimeLog\\LogCdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\RealTimeLog\\LoggingField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\RealTimeLog\\LogPath.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\RealTimeLog\\LogScope.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\RealTimeLog\\OutputFormat.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\RealTimeLog\\RealTimeLog.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\RealTimeLog\\TtlConfig.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Scalar\\ArrayType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\SelectionFieldsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Stats\\CdnStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Stats\\ChartData.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Stats\\ChartSeries.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Stats\\ContinentStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Stats\\CustomerStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Stats\\DataCenterStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Stats\\DataSource.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Stats\\OverviewSubType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Stats\\OverviewType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Stats\\Stats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Stats\\StatsGrouping.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Stats\\StatsSortBy.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\Stats\\TimeSeries.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\TeamMemberAccessConfiguration\\AccessType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\TeamMemberAccessConfiguration\\Configuration.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\TeamMemberAccessConfiguration\\Restriction.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\TeamMemberAccessConfiguration\\RestrictionType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Domain\\Schema\\Type\\TypeBag.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\GraphQL\\Infrastructure\\Finder\\DbalIncomeFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Inquiry\\Application\\Controller\\RequestAssistanceController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Inquiry\\Application\\Controller\\SendInquiryController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Inquiry\\Application\\Payload\\InquiryBodySchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Inquiry\\Application\\Payload\\NewSendInquirySchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Inquiry\\Application\\Payload\\RequestAssistanceSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Inquiry\\Domain\\Command\\RequestAssistance.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Inquiry\\Domain\\Command\\RequestAssistanceHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Inquiry\\Domain\\Command\\SendInquiry.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Inquiry\\Domain\\Command\\SendInquiryHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Inquiry\\Domain\\Mail\\InquiryMail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Inquiry\\Domain\\Repository\\InquiryRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Inquiry\\Domain\\Value\\Department.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Inquiry\\Domain\\Value\\InquiryBody.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Inquiry\\Infrastructure\\Repository\\DoctrineInquiryRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Console\\SendUnsent.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Controller\\AddController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Controller\\ContactDetailController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Controller\\CountriesListController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Controller\\DownloadController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Controller\\EditContactDetailController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Controller\\ListController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Controller\\ListUnpaidController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Controller\\ReconciliationDataListController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Payload\\CommonContactDetailSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Payload\\ContactDetailSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Payload\\CountriesSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Payload\\CountrySchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Payload\\Dto\\NewInvoice.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Payload\\Dto\\ReconciliationExternalPaymentIdsScope.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Payload\\EditContactDetailSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Payload\\InvoiceCustomerSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Payload\\InvoiceDetailSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Payload\\InvoiceItemSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Payload\\InvoiceListSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Payload\\NewInvoiceSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Payload\\ReconciliationDataDetailSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Payload\\ReconciliationDataListSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Payload\\ReconciliationExternalPaymentIdsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Payload\\UnpaidInvoiceDetailSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Payload\\UnpaidInvoiceRequestScopeSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Payload\\UnpaidInvoiceSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Application\\Payload\\UnpaidInvoicesSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Command\\AddInvoice.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Command\\AddInvoiceHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Command\\SendUnsentInvoices.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Command\\SendUnsentInvoicesHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Command\\UpdateContactDetailForCustomer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Command\\UpdateContactDetailForCustomerHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Dto\\Address.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Dto\\ContactDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Dto\\DownloadableInvoice.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Dto\\GbpEquivalentConversion.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Dto\\InvoiceDetails.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Dto\\InvoiceItemDetails.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Dto\\UnpaidInvoiceRequestScope.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Exception\\CouldNotCreateInvoice.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Exception\\CountryNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Exception\\GbpConversionFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Exception\\InvalidVatNumber.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Exception\\InvoiceCustomerNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Exception\\InvoiceLineCreationFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Exception\\InvoiceNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Exception\\NoCountryAssignedToCustomer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Exception\\XeroContactCouldNotBeCreated.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Exception\\XeroContactNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Finder\\CountriesFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Finder\\InvoiceCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Finder\\InvoiceNumberFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Finder\\XeroContactCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\InvoiceFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\InvoiceLineFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\InvoiceSender.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Query\\FindCountries\\FindCountries.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Query\\FindCountries\\FindCountriesHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Query\\FindCountries\\Input\\Filter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Query\\FindCountries\\Input\\SelectionField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Query\\FindCountries\\Output\\Countries.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Query\\FindCountries\\Output\\Country.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Query\\FindInvoicesForCustomer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Query\\FindInvoicesForCustomerHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Query\\FindInvoicesForExternalPaymentIds.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Query\\FindInvoicesForExternalPaymentIdsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Query\\FindUnpaidInvoices\\GetAllUnpaid.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Query\\FindUnpaidInvoices\\GetAllUnpaidHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Query\\FindUnpaidInvoices\\Input\\Filter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Query\\FindUnpaidInvoices\\Output\\UnpaidInvoice.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Query\\FindUnpaidInvoices\\Output\\UnpaidInvoices.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Query\\GetContactDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Query\\GetContactDetailHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Query\\GetCountries.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Query\\GetCountriesHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Query\\GetDownloadableInvoice.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Query\\GetDownloadableInvoiceHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Repository\\CountryRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Repository\\InvoiceCustomerRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Repository\\InvoiceRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Repository\\XeroContactRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Validation\\EuVatNumberValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Validation\\VatNumberChecker.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Value\\BankAccountId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Value\\BrandingThemeId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Value\\CountryIsoCode.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Value\\InvoiceNumber.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Value\\InvoiceNumberPrefix.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Value\\InvoiceStatus.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Value\\LineAmountType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Value\\Quantity.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Value\\UnitPrice.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Value\\VatNumber.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Value\\XeroContactName.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\Value\\XeroTaxType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Domain\\XeroContactNameResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Infrastructure\\Finder\\DbalCountriesFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Infrastructure\\Finder\\DbalInvoiceCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Infrastructure\\Finder\\DbalInvoiceNumberFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Infrastructure\\Finder\\DoctrineXeroContactCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Infrastructure\\Repository\\DoctrineCountryRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Infrastructure\\Repository\\DoctrineInvoiceCustomerRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Infrastructure\\Repository\\DoctrineInvoiceRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Infrastructure\\Repository\\DoctrineXeroContactRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Infrastructure\\Validation\\DdeboerEuVatNumberValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Invoice\\Infrastructure\\Validation\\FakeEuVatNumberValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Application\\Controller\\DetailController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Application\\Controller\\ListController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Application\\Controller\\PurgeAllJobController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Application\\Controller\\SchedulePrefetchJobController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Application\\Controller\\SchedulePurgeJobController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Application\\Controller\\ScheduleTagPurgeJobController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Application\\Payload\\CdnReference.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Application\\Payload\\IgnoredPathsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Application\\Payload\\JobBaseSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Application\\Payload\\JobDetailSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Application\\Payload\\JobsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Application\\Payload\\PathCountSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Application\\Payload\\PrefetchJobSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Application\\Payload\\PurgeAllJobSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Application\\Payload\\PurgeJobSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Application\\Payload\\ScheduleJobSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Application\\Payload\\SchedulePrefetchSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Application\\Payload\\SchedulePurgeSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Application\\Payload\\ScheduleTagJobSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Application\\Payload\\UpstreamHostSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Console\\PruneCommand.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Command\\CompleteJob.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Command\\CompleteJobHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Command\\PruneCompletedJobs.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Command\\PruneCompletedJobsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Command\\PurgeAll.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Command\\PurgeAllHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Command\\ScheduleJob.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Command\\ScheduleJobHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Command\\SchedulePurgeTagJob.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Command\\SchedulePurgeTagJobHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Dto\\PrefetchJob.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Dto\\PurgeJob.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Exception\\JobNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Exception\\JobSchedulingNotAllowed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Exception\\PurgeAllDisabled.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Exception\\RateLimitExceeded.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Exception\\UnableToScheduleJob.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Finder\\CustomerScheduledJobCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Limit\\DatabaseJobRateLimiter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Limit\\JobRateLimiter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Notify\\JobChannelNotifier.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Query\\GetJobForCustomer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Query\\GetJobForCustomerHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Query\\GetJobsForCdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Query\\GetJobsForCdnHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Repository\\JobRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\UpstreamHostValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Validator\\PrefetchModeValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Value\\JobState.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Value\\JobType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Value\\Paths.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Domain\\Value\\PurgeTags.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Infrastructure\\Finder\\DbalCustomerScheduledJobCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Infrastructure\\Notify\\DbalJobChannelNotifier.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Job\\Infrastructure\\Repository\\DoctrineJobRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Kernel.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Application\\Controller\\ActiveCustomersInfoController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Application\\Controller\\AraClickHouseCompareController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Application\\Controller\\BillingRegionTrafficController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Application\\Controller\\CdnServerBandwidthController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Application\\Controller\\CdnStatusCodeCountController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Application\\Controller\\CommandBufferUnprocessedCountController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Application\\Controller\\CommandBufferUnprocessedLongestJobDurationController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Application\\Controller\\CustomerBandwidthController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Application\\Controller\\ObjectStorageEgressTrafficController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Application\\Controller\\QueuedJobsController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Application\\Controller\\ResourceBandwidthController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Application\\Controller\\RgwStatsStatusController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Application\\Controller\\ServerCityCodeCompareController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Application\\Controller\\ServerLocationController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Application\\Controller\\Stats\\CustomerBandwidthPerServerLocationController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Application\\Controller\\Stats\\DstAsWithoutHotStreamsTrafficController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Application\\Controller\\Stats\\SpendingStatsDiffExporterController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Application\\Controller\\Stats\\TrafficTikTokController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Application\\HttpFoundation\\MetricResponseCache.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Application\\HttpFoundation\\PrometheusResponseFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Application\\Payload\\CustomerBandwidthScopeSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Application\\Payload\\CustomerIdSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Application\\Payload\\CustomerTrafficDestinationSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Application\\Payload\\GetCustomerBandwidthPerServerSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Application\\Payload\\SpendingStatsComparatorScopeSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Console\\SaveAraClickHouseCompareMetricCommand.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Exception\\CannotGetObjectStorageEgressTraffic.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Exception\\CannotGetObjectStorageEgressTrafficLatestTimestamp.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Exception\\CannotGetQueuedJobOldestTimestamps.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Exception\\CannotGetQueuedJobPathCounts.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Exception\\FailedToGetCdnServerBandwidthMetric.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Finder\\ActiveCdnIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Finder\\ActiveCustomerFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Finder\\BillingRegionTrafficFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Finder\\CdnIdCustomerFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Finder\\CdnStatusCodeCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Finder\\CommandBufferFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Finder\\CustomerBandwidthFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Finder\\CustomerCdnsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Finder\\DstAsTrafficFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Finder\\DstAsTrafficLiveStreamingFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Finder\\LiveStreamingCdnServerBandwidthFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Finder\\ObjectStorageEgressTrafficFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Finder\\ObjectStorageEgressTrafficLatestTimestampFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Finder\\QueuedJobOldestTimestampFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Finder\\QueuedJobPathCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Finder\\RgwStatsCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Finder\\ServerLocationFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Finder\\ServerTrafficFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\CommandBuffer\\CommandBufferLongestUnprocessedJobDuration.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\CommandBuffer\\CommandBufferUnprocessedTopicCount.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\Counter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\Customer\\BillingRegionTraffic.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\Customer\\CustomerInfo.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\Customer\\DstAsBps.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\Customer\\DstAsWithoutHotStreamsBps.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\Customer\\ResourceTraffic.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\Customer\\ServerLocationCustomerTraffic.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\DataManipulation\\QueuedJobOldestTimestamp.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\DataManipulation\\QueuedJobPathCount.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\Exporter\\Timestamp.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\Gauge.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\Metric.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\ObjectStorage\\RgwStatsCount.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\Registry.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\Server\\CompareServerCityCodeTrafficBytes.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\Server\\ServerLocation.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\Stats\\AraRequestCount.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\Stats\\AraTraffic.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\Stats\\CdnServerBandwidthMetric.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\Stats\\ClickHouseRequestCount.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\Stats\\ClickHouseTraffic.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\Stats\\CompareStatsMetric.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\Stats\\CustomerBandwidth.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\Stats\\ObjectStorageEgressTraffic.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\Stats\\ObjectStorageEgressTrafficLatestTimestamp.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\Stats\\SpendingStatsLatestTimestamp.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\Stats\\SpendingStatsTraffic.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\Stats\\TotalTraffic.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Metric\\StatusCode\\CdnStatusCodeCount.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\MetricFactory\\ActiveCustomerMetricsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\MetricFactory\\BillingRegionTrafficMetricFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\MetricFactory\\CdnServerBandwidthMetricsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\MetricFactory\\CdnStatusCodeCountMetricsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\MetricFactory\\CommandBufferUnprocessedCountMetricsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\MetricFactory\\CommandBufferUnprocessedLongestDurationMetricsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\MetricFactory\\CompareAraClickHouseMetricsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\MetricFactory\\CompareServerCityCodeMetricsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\MetricFactory\\CustomerBandwidthMetricsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\MetricFactory\\CustomerBandwidthPerServerLocationMetricsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\MetricFactory\\DstAsWithoutHotStreamsTrafficMetricsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\MetricFactory\\ExporterMetricsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\MetricFactory\\Origin\\ObjectStorageEgressTrafficMetricsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\MetricFactory\\QueuedJobsMetricsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\MetricFactory\\ResourceTrafficMetricFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\MetricFactory\\RgwStatsStatusMetricsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\MetricFactory\\ServerLocationMetricsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\MetricFactory\\Stats\\SpendingStatsComparatorMetricsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\MetricFactory\\TrafficTikTokMetricsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Repository\\OriginRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Value\\BillingRegionTraffic.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Value\\CdnServerBandwidth.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Value\\CustomerBandwidth.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Value\\CustomerBandwidthScope.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Value\\CustomerCdns.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Value\\CustomerReference.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Value\\CustomerTrafficDestinationScope.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Value\\GetCustomerBandwidthPerServerScope.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Value\\QueuedJobPathCount.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Value\\ServerLocation.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Value\\SpendingStatsComparatorScope.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Domain\\Value\\TimeOffset.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Infrastructure\\Finder\\ClickHouseCdnStatusCodeCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Infrastructure\\Finder\\ClickhouseCephObjectStorageEgressTrafficFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Infrastructure\\Finder\\ClickhouseCephObjectStorageEgressTrafficLatestTimestampFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Infrastructure\\Finder\\ClickHouseDstAsTrafficFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Infrastructure\\Finder\\ClickHouseDstAsTrafficLiveStreamingFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Infrastructure\\Finder\\ClickHouseLiveStreamingCdnServerBandwidthFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Infrastructure\\Finder\\ClickHouseServerTrafficFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Infrastructure\\Finder\\DbalActiveCdnIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Infrastructure\\Finder\\DbalBillingRegionTrafficFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Infrastructure\\Finder\\DbalCdnIdCustomerFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Infrastructure\\Finder\\DbalCommandBufferFinderFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Infrastructure\\Finder\\DbalCustomerBandwidthFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Infrastructure\\Finder\\DbalCustomerCdnsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Infrastructure\\Finder\\DbalQueuedJobOldestTimestampFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Infrastructure\\Finder\\DbalQueuedJobPathCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Infrastructure\\Finder\\DbalRgwStatsCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Infrastructure\\Finder\\DbalServerLocationFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Infrastructure\\Finder\\DoctrineActiveCustomerFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Monitoring\\Overkill\\Infrastructure\\Repository\\DoctrineOriginRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\MonthlyTrafficPlan\\Application\\Controller\\InternalListController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\MonthlyTrafficPlan\\Application\\Payload\\MonthlyTrafficPlanListSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\MonthlyTrafficPlan\\Application\\Payload\\MonthlyTrafficPlanSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\MonthlyTrafficPlan\\Domain\\AvailableTrafficVolumeCalculator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\MonthlyTrafficPlan\\Domain\\Dto\\ActivePeriod.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\MonthlyTrafficPlan\\Domain\\Exception\\CustomerMonthlyTrafficPlanIsTerminated.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\MonthlyTrafficPlan\\Domain\\Exception\\CustomerMonthlyTrafficPlanNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\MonthlyTrafficPlan\\Domain\\Exception\\MonthlyTrafficPlanNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\MonthlyTrafficPlan\\Domain\\MonthlyTrafficPlanLogger.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\MonthlyTrafficPlan\\Domain\\Query\\GetMonthlyTrafficPlanList.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\MonthlyTrafficPlan\\Domain\\Query\\GetMonthlyTrafficPlanListHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\MonthlyTrafficPlan\\Domain\\Repository\\CustomerMonthlyTrafficPlanRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\MonthlyTrafficPlan\\Domain\\Repository\\MonthlyPlanGroupRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\MonthlyTrafficPlan\\Domain\\Repository\\MonthlyTrafficPlanRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\MonthlyTrafficPlan\\Infrastructure\\Repository\\DoctrineCustomerMonthlyTrafficPlanRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\MonthlyTrafficPlan\\Infrastructure\\Repository\\DoctrineMonthlyPlanGroupRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\MonthlyTrafficPlan\\Infrastructure\\Repository\\DoctrineMonthlyTrafficPlanRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Notes\\Domain\\Finder\\NotesFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Notes\\Domain\\Query\\FindNotes\\FindNotes.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Notes\\Domain\\Query\\FindNotes\\FindNotesHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Notes\\Domain\\Query\\FindNotes\\Input\\Filter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Notes\\Domain\\Query\\FindNotes\\Input\\SelectionField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Notes\\Domain\\Query\\FindNotes\\Output\\Note.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Notes\\Domain\\Query\\FindNotes\\Output\\Notes.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Notes\\Infrastructure\\Finder\\DbalNotesFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Console\\ChargeCustomers.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Console\\CollectUsageStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Console\\FillUsageStatsGap.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Controller\\CreateAccessKeyController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Controller\\CreateRegionKeyController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Controller\\DeleteAccessKeyController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Controller\\GetBillingController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Controller\\GetMultiSumController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Controller\\GetRgwPricingController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Controller\\GetStatsController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Controller\\ListAccessKeysController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Controller\\ListClustersController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Controller\\RefreshAccessKeyPairController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Controller\\UserQuotaController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Docs\\ClusterSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Docs\\ClustersSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Docs\\Stats\\GetStatsSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Docs\\Stats\\StatsSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Docs\\Stats\\StatsSetSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Payload\\AccessKeyDetailSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Payload\\AccessKeySchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Payload\\AccessKeysListSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Payload\\ClusterQuotaSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Payload\\ClusterQuotasSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Payload\\ClusterSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Payload\\ClustersSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Payload\\PoliciesSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Payload\\PolicySchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Payload\\RefreshAccessKeyPairSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Payload\\Request\\GetStatsPayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Payload\\Request\\GetStatsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Payload\\Response\\BillingSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Payload\\Response\\BillingsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Payload\\Response\\RgwPricingListSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Payload\\Response\\RgwPricingSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Payload\\Response\\StatsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Payload\\Response\\StatsSetSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Payload\\Response\\SumSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Application\\Payload\\Response\\SumsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Billing\\BillingCalculator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Billing\\RgwPricingResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\BucketsByHostResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Command\\ChargeCustomers.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Command\\ChargeCustomersHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Command\\ClearAccessKeyPolicy.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Command\\ClearAccessKeyPolicyHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Command\\CollectUsageStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Command\\CollectUsageStatsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Command\\CreateAccessKey.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Command\\CreateAccessKeyHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Command\\CreateRegionKey.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Command\\CreateRegionKeyHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Command\\DeleteAccessKey.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Command\\DeleteAccessKeyHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Command\\EraseAccessKey.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Command\\EraseAccessKeyHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Command\\FillStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Command\\FillStatsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Command\\RefreshAccessKeyPair.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Command\\RefreshAccessKeyPairHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Dto\\AccessKeyDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Dto\\BucketsByHost.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Dto\\BucketScope.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Dto\\UserQuota.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Exception\\AccessKeyNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Exception\\AccessKeyPairRefreshFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Exception\\AccessKeyRemovalFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Exception\\BillingAccessDenied.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Exception\\BucketsNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Exception\\CreateAccessKeyFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Exception\\FailedToGetUserQuota.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Exception\\InvalidRequestCount.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Exception\\InvalidStatsScope.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Exception\\NoPolicyActionsSet.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Finder\\AccessKeysCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Finder\\CurrentRgwStatsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Finder\\CustomerIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Payment\\ObjectStorageUsageCharger.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Provider\\ObjectCountStatsProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Provider\\RequestsStatsProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Provider\\StatsProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Provider\\TrafficStatsProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Provider\\UsedSpaceStatsProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Query\\FindAccessKeys.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Query\\FindAccessKeysHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Query\\FindRgwClusters.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Query\\FindRgwClustersHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Query\\GetBilling.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Query\\GetBillingHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Query\\GetMultiSum.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Query\\GetMultiSumHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Query\\GetRgwPricing.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Query\\GetRgwPricingHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Query\\GetStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Query\\GetStatsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Query\\GetUserQuota.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Query\\GetUserQuotaHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Repository\\AccessKeysRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Repository\\ObjectStorageOriginRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Repository\\RgwStatsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\StatisticsCollector.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\UsedSpaceResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Validation\\AccessKeyLabelValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Validation\\AccessKeyValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Validation\\BillingAccessValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Validation\\DatabaseAccessKeyValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Validation\\IntervalAggregationValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Validation\\ObjectStorageAccessValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Value\\AccessType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Value\\Action.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Value\\Aggregation.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Value\\Billing.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Value\\BillingWithEstimate.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Value\\BucketLifecycle\\BucketLifecycleConfiguration.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Value\\BucketLifecycle\\Prefix.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Value\\BucketLifecycle\\Rule.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Value\\BucketLifecycle\\RuleId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Value\\BucketLifecycle\\RuleStatus.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Value\\NewAccessKey.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Value\\OriginTtlConfigs.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Value\\Policy.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Value\\PolicyEffect.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Value\\RequestCount.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Value\\RgwPricing.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Value\\StatementId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Value\\Stats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Value\\StatsScope.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Value\\StatsType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Value\\Sum.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Domain\\Value\\TtlConfig.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Infrastructure\\Finder\\DbalAccessKeysCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Infrastructure\\Finder\\DbalCurrentRgwStatsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Infrastructure\\Finder\\DbalCustomerIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Infrastructure\\Provider\\ClickHouseCephStatsProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Infrastructure\\Provider\\PostgresStatsProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Infrastructure\\Repository\\DoctrineAccessKeysRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Infrastructure\\Repository\\DoctrineObjectStorageOriginRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Infrastructure\\Repository\\DoctrineRgwStatsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\ObjectStorage\\Infrastructure\\Validation\\DbalAccessKeyLabelValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Console\\PruneSuspendedObjectStorageOrigins.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Console\\SynchronizeObjectStorageClusters.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Controller\\CheckSslController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Controller\\ConnectController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Controller\\CreateLogObjectStorageController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Controller\\CreateObjectStorageOriginController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Controller\\CreateS3OriginController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Controller\\CreateUrlOriginController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Controller\\DetailController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Controller\\EditObjectStorageOriginController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Controller\\EditOriginController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Controller\\ListController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Controller\\RemoveController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Controller\\SetOriginTimeoutController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Docs\\CdnReferenceSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Docs\\ObjectStorage\\ObjectStorageOriginDetailSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Docs\\ObjectStorage\\ObjectStorageUsageSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Docs\\OriginListSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Docs\\S3\\S3OriginDetailSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Docs\\Storage\\StorageOriginDetailSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Docs\\Url\\UrlOriginDetailSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Payload\\CdnReferenceSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Payload\\Dto\\EditedOrigin.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Payload\\Dto\\OriginCdns.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Payload\\Dto\\OriginTimeout.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Payload\\EditOriginSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Payload\\EditS3OriginSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Payload\\EditUrlOriginSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Payload\\ObjectStorage\\AccessKeyPolicySchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Payload\\ObjectStorage\\AccessKeysSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Payload\\ObjectStorage\\EditObjectStorageOriginSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Payload\\ObjectStorageOriginDetailSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Payload\\ObjectStorageOriginSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Payload\\ObjectStorageUsageSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Payload\\OriginCdnsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Payload\\OriginDetailSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Payload\\OriginFallbackConfigurationSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Payload\\OriginIdSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Payload\\OriginListSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Payload\\OriginSslCertificateSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Payload\\OriginTimeoutSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Payload\\S3OriginDetailSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Payload\\S3OriginSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Payload\\SslSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Payload\\StorageOriginDetailSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Payload\\UrlOriginDetailSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Application\\Payload\\UrlOriginSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\CdnsUpdater.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Ceph\\ClientApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\CheckOriginSslCertificate.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\CheckOriginSslCertificateHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\ConnectCdnsToOrigin.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\ConnectCdnsToOriginHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\CreateLogObjectStorage.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\CreateLogObjectStorageHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\CreateObjectStorageOrigin.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\CreateObjectStorageOriginHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\CreateOrigin.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\CreateOriginHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\DeleteObjectStorage.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\DeleteObjectStorageHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\EditObjectStorageOrigin.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\EditObjectStorageOriginHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\EditOrigin.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\EditOriginHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\LookupSharedOrigin.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\LookupSharedOriginHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\PruneSuspendedObjectStorageOrigins.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\PruneSuspendedObjectStorageOriginsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\RemoveOrigin.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\RemoveOriginHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\SendWelcomeToCdn77ObjectStorage.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\SendWelcomeToCdn77ObjectStorageHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\SetTimeoutToOrigin.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\SetTimeoutToOriginHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\SynchronizeObjectStorageClusters.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\SynchronizeObjectStorageClustersHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\UpdateCdns.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Command\\UpdateCdnsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\CredentialsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Dto\\Connection.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Dto\\FallbackConfiguration.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Dto\\ObjectStorageConnection.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Dto\\ObjectStorageOrigin.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Dto\\ObjectStorageOriginConfiguration.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Dto\\ObjectStorageOriginDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Dto\\OriginDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Dto\\OriginDetailList.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Dto\\S3Connection.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Dto\\S3OriginDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Dto\\SharedOriginInfo.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Dto\\StorageConnection.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Dto\\StorageOriginDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Dto\\UrlOriginDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Exception\\CreateLogObjectStorageFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Exception\\CreateObjectStorageFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Exception\\CreateOriginFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Exception\\DeleteObjectStorageFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Exception\\DuplicateOriginFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Exception\\EditOriginIsForbidden.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Exception\\InvalidS3Credentials.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Exception\\ObjectStorageBucketAlreadyExists.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Exception\\OriginConnectionFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Exception\\OriginCouldNotBeCreated.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Exception\\OriginRemovalFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Exception\\UpdateForbidden.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Factory\\ObjectStorageOriginFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Factory\\OriginDetailFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Fallback\\FallbackOriginConfigurator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Finder\\CurrentRgwStatsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Finder\\LogObjectStorageCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Finder\\OriginConnectionCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Finder\\OriginCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Finder\\OriginFallbackCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Finder\\OriginsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Finder\\RealTimeLogCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Finder\\StreamOriginsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Mail\\SharedOriginNotification.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Query\\FindOrigins\\Filter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Query\\FindOrigins\\FindOrigins.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Query\\FindOrigins\\FindOriginsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Query\\FindOrigins\\OrderField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Query\\FindOrigins\\Output\\Origin.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Query\\FindOrigins\\Output\\Origins.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Query\\FindOrigins\\SelectionField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Query\\FindStreamOrigins\\FindStreamOrigins.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Query\\FindStreamOrigins\\FindStreamOriginsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Query\\FindStreamOrigins\\Input\\SelectionField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Query\\FindStreamOrigins\\Output\\StreamOrigin.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Query\\FindStreamOrigins\\Output\\StreamOrigins.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Query\\GetOriginForCustomer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Query\\GetOriginForCustomerHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Query\\GetOriginListForCustomer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Query\\GetOriginListForCustomerHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Quota\\CreatedOriginQuotaChecker.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Repository\\CdnRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Repository\\OriginFallbackRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Repository\\OriginRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Ssl\\SslCertificateValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Updater.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Validation\\BucketNameValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Validation\\OriginAccessValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Validation\\OriginLabelValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Value\\AclType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Value\\ObjectStorageUsage.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Value\\OriginErrorType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Value\\S3AccessKey.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Value\\S3Credentials.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Value\\S3Region.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Value\\S3Secret.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Domain\\Value\\S3Type.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Infrastructure\\Ceph\\S3ClientApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Infrastructure\\Finder\\DbalCurrentRgwStatsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Infrastructure\\Finder\\DbalLogObjectStorageCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Infrastructure\\Finder\\DbalOriginCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Infrastructure\\Finder\\DbalOriginFallbackCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Infrastructure\\Finder\\DbalOriginsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Infrastructure\\Finder\\DbalRealTimeLogCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Infrastructure\\Finder\\DbalStreamOriginsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Infrastructure\\Finder\\DoctrineOriginConnectionCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Infrastructure\\Quota\\DatabaseCreatedOriginQuotaChecker.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Infrastructure\\Repository\\DoctrineCdnRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Infrastructure\\Repository\\DoctrineOriginFallbackRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Infrastructure\\Repository\\DoctrineOriginRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Infrastructure\\Validation\\DatabaseBucketNameValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Origin\\Infrastructure\\Validation\\DatabaseOriginLabelValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Controller\\AddCardController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Controller\\AddController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Controller\\CompletePaymentController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Controller\\CreditPaymentController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Controller\\CustomPlanPaymentController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Controller\\EditCreditCardController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Controller\\EditPaymentSettingsController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Controller\\MonthlyPlanPaymentController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Controller\\PaymentSettingsController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Controller\\PaymentSumPerDayController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Controller\\RechargeCustomPlanController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Controller\\RechargePaymentController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Controller\\RemoveCardInfoController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Payload\\BillingPeriodSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Payload\\DateRangeSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Payload\\Dto\\NewPayment.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Payload\\Dto\\NotificationData.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Payload\\Dto\\PaymentCard.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Payload\\Dto\\PaymentData.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Payload\\Dto\\RechargePaymentData.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Payload\\EditCreditCardSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Payload\\FailedPaymentSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Payload\\NewCreditCardSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Payload\\NewPaymentSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Payload\\NotificationPayloadSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Payload\\PaymentRecipeConfigurationSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Payload\\PaymentRecipeSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Payload\\PaymentRecipesSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Payload\\PaymentSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Payload\\PaymentSettings\\AutoRechargeSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Payload\\PaymentSettings\\AutoRenewalSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Payload\\PaymentSettings\\DetailSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Payload\\PaymentSettings\\EditSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Payload\\PaymentSumPerDaySchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Payload\\PaymentSumSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Payload\\RechargeCustomPlanSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Payload\\RechargePaymentSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Payload\\RemoveCardInfoSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Application\\Payload\\SuccessfulPaymentSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Console\\NotifyExpiringPaymentRecipes.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Console\\RenewMonthlyPlans.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\ActivePeriodCalculator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\BankAccountResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\AddCreditCard.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\AddCreditCardHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\AddCustomPlanPayment.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\AddCustomPlanPaymentHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\AddMonthlyPlanPayment.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\AddMonthlyPlanPaymentHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\AddPAYGPayment.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\AddPAYGPaymentHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\AddPayment.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\AddPaymentHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\AddWireTransferPayment.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\AddWireTransferPaymentHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\CompletePayment.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\CompletePaymentHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\DeclareWireTransferPayment.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\DeclareWireTransferPaymentHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\EditCreditCard.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\EditCreditCardHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\EditPaymentSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\EditPaymentSettingsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\NotifyExpiringPaymentRecipes.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\NotifyExpiringPaymentRecipesHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\RechargeCustomPlan.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\RechargeCustomPlanHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\RemoveCardInfo.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\RemoveCardInfoHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\RenewMonthlyPlan.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\RenewMonthlyPlanHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\RenewMonthlyPlans.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\RenewMonthlyPlansHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\SendMonthlyPlanRenewalNotification.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Command\\SendMonthlyPlanRenewalNotificationHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\CustomPlanCharger.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\CustomPlanPaymentCompleter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\CustomPlanPaymentProcessor.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Dto\\CustomPlanPaymentData.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Dto\\MonthlyPlanRenewal.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Dto\\NewCreditCard.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Dto\\PaymentClientData.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Dto\\PaymentDetails.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Dto\\PaymentRecipeConfiguration.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Dto\\PaymentRequestData.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Dto\\PaymentSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Dto\\PaymentSum.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Dto\\UpdatedCreditCard.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Dto\\UpdatedPaymentSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Dto\\WireTransferDTO.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Exception\\CustomerMonthlyTrafficPlanNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Exception\\DuplicateCreditCardFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Exception\\InvalidRechargeLimit.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Exception\\MonthlyPlanGroupNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Exception\\PaymentFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Exception\\PaymentNotAuthorized.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Exception\\PaymentNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Exception\\PaymentRecipeNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Exception\\PromoCodeNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Exception\\PromoCodeUsageNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Exception\\UnsupportedPaymentMethod.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Exception\\WireTransferNotCompleted.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Finder\\CustomerMonthlyTrafficPlanFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Finder\\PaymentCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Finder\\PaymentRecipeCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Finder\\PaymentsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Finder\\PaymentSumFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\MailingSolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\MinimumPriceResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\MonthlyTrafficPlanCharger.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Paygate\\Paygate.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Paygate\\PaygatePaymentProcessor.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Paygate\\PaymentProcessor.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\PAYGBonusCalculator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\PaymentCurrencyResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\PaymentCustomerResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\PromoCodeBonusCalculator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\PromoCodeUsageLogger.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\ProportionatePriceCalculator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Query\\FindPayments\\FindPayments.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Query\\FindPayments\\FindPaymentsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Query\\FindPayments\\Input\\Filter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Query\\FindPayments\\Input\\SelectionField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Query\\FindPayments\\Output\\Payment.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Query\\FindPayments\\Output\\Payments.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Query\\GetPaymentSettings.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Query\\GetPaymentSettingsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Query\\GetPaymentSumPerDay.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Query\\GetPaymentSumPerDayHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Repository\\CustomerMonthlyTrafficPlanRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Repository\\CustomPlanContractRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Repository\\InvoiceLineRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Repository\\InvoiceRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Repository\\MonthlyPlanGroupRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Repository\\PaymentRecipeRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Repository\\PaymentRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Repository\\PlanRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Repository\\PromoCodeRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Repository\\PromoCodeUsageRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Validator\\CreditCardLabelValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Validator\\MinimumAmountValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Value\\BankName.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Value\\CardBrand.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Value\\MailTemplates\\MonthlyPlanRenewalSuccessNotification.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Value\\MailTemplates\\RechargeFailedNotification.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Value\\MonthlyPlanPaymentData.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Value\\PAYGPaymentData.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Value\\PaymentData.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Value\\PaymentFailureReason.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Value\\PaymentMethod.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Value\\PaymentStatus.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\Value\\PaymentType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Domain\\VatRateResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Infrastructure\\Finder\\DbalCustomerMonthlyTrafficPlanFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Infrastructure\\Finder\\DbalPaymentsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Infrastructure\\Finder\\DbalPaymentSumFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Infrastructure\\Finder\\DoctrinePaymentCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Infrastructure\\Finder\\DoctrinePaymentRecipeCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Infrastructure\\Paygate\\SdkPaygate.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Infrastructure\\Repository\\DoctrineCustomerMonthlyTrafficPlanRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Infrastructure\\Repository\\DoctrineCustomPlanContractRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Infrastructure\\Repository\\DoctrineInvoiceLineRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Infrastructure\\Repository\\DoctrineInvoiceRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Infrastructure\\Repository\\DoctrineMonthlyPlanGroupRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Infrastructure\\Repository\\DoctrinePaymentRecipeRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Infrastructure\\Repository\\DoctrinePaymentRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Infrastructure\\Repository\\DoctrinePlanRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Infrastructure\\Repository\\DoctrinePromoCodeRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Payment\\Infrastructure\\Repository\\DoctrinePromoCodeUsageRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Application\\Controller\\AddMonthlyController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Application\\Controller\\ClosePlanController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Application\\Controller\\DetailController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Application\\Payload\\ClosePlanDataSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Application\\Payload\\Dto\\MonthlyPlanTermination.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Application\\Payload\\MonthlyPlanSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Application\\Payload\\NewMonthlyPlanSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Console\\AddLocations.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Command\\AddLocations.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Command\\AddLocationsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Command\\AddMonthlyPlan.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Command\\AddMonthlyPlanHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Command\\CloseMonthlyPlan.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Command\\CloseMonthlyPlanHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Dto\\Location.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Dto\\MonthlyPeriod.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Dto\\MonthlyPlanDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Dto\\NewMonthlyPlan.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Dto\\Revenue.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Exception\\CannotCloseMonthlyPlan.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Exception\\MonthlyPlanCouldNotBeCreated.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Exception\\PlanNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Factory\\MonthlyPlanFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Factory\\PlanFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Finder\\MonthlyPlanCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Finder\\PlansFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Finder\\PlansLocationsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Finder\\RevenueFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\MonthlyPlan\\MonthlyPlanConfigurator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\MonthlyPlan\\PlanConfigurator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\MonthlyPlanGroupUpgrader.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\PlanLocationsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Query\\FindPlans\\FindPlans.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Query\\FindPlans\\FindPlansHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Query\\FindPlans\\Input\\Filter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Query\\FindPlans\\Input\\SelectionField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Query\\FindPlans\\Output\\Plan.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Query\\FindPlans\\Output\\Plans.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Query\\GetMonthlyPlanForCustomer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Query\\GetMonthlyPlanForCustomerHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Query\\GetRevenue.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Query\\GetRevenueHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Repository\\PlanCdnRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Repository\\PlanLocationRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Repository\\PlanRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Domain\\Value\\PlanType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Infrastructure\\Finder\\DbalMonthlyPlanCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Infrastructure\\Finder\\DbalPlansFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Infrastructure\\Finder\\DbalPlansLocationsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Infrastructure\\Finder\\DbalRevenueFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Infrastructure\\Repository\\DoctrinePlanCdnRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Infrastructure\\Repository\\DoctrinePlanLocationRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Plan\\Infrastructure\\Repository\\DoctrinePlanRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Public\\Application\\Controller\\BuildController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Public\\Application\\Controller\\IpController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Public\\Application\\Controller\\TlsTestController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Public\\Application\\Payload\\BuildSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Public\\Application\\Payload\\ProtocolResultSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Public\\Application\\Payload\\ProtocolsResultSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Public\\Application\\Payload\\TlsTestResultsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Public\\Application\\Payload\\TlsTestSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Public\\Domain\\Command\\TriggerCdn77JobsBuild.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Public\\Domain\\Command\\TriggerCdn77JobsBuildHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Public\\Domain\\Dto\\GitLabPipeline.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Public\\Domain\\Dto\\LookupResult.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Public\\Domain\\Dto\\TlsTestResults.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Public\\Domain\\Exception\\TlsTestFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Public\\Domain\\Query\\GetTlsTestResults.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Public\\Domain\\Query\\GetTlsTestResultsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Public\\Domain\\Query\\LookupSignUpIp.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Public\\Domain\\Query\\LookupSignUpIpHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Public\\Domain\\TLS\\TlsTester.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Public\\Infrastructure\\TLS\\Endpoint\\InfoEndpoint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Public\\Infrastructure\\TLS\\HttpTlsTester.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Rate\\Application\\Controller\\ListController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Rate\\Application\\Payload\\RateSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Rate\\Application\\Payload\\RatesSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Rate\\Domain\\Dto\\LocationRate.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Rate\\Domain\\Finder\\CustomerRatesCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Rate\\Domain\\Query\\GetRatesForCustomer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Rate\\Domain\\Query\\GetRatesForCustomerHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Rate\\Domain\\Repository\\RateRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Rate\\Infrastructure\\Finder\\DoctrineRatesCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Rate\\Infrastructure\\Repository\\DoctrineRateRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Application\\Command\\GetRawLogForDownload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Application\\Command\\GetRawLogForDownloadHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Application\\Console\\DeactivateExpiredRawLogs.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Application\\Controller\\ActivateRawLogController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Application\\Controller\\DeactivateRawLogController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Application\\Controller\\DownloadController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Application\\Controller\\ListController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Application\\Controller\\SampleController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Application\\Controller\\SumController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Application\\Payload\\CdnRawLogSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Application\\Payload\\CdnRawLogsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Application\\Payload\\CdnReferenceSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Application\\Payload\\DownloadRawLogSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Application\\Payload\\Dto\\DownloadRawLogRequestScope.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Application\\Payload\\LogsSampleScopeSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Application\\Payload\\RawLogActivePeriodSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Application\\Payload\\SampleLogLineSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Application\\Payload\\SampleLogsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Application\\Payload\\SumSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Application\\Payload\\ToggleRawLogStatusSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Domain\\Command\\ActivateRawLog.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Domain\\Command\\ActivateRawLogHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Domain\\Command\\DeactivateExpiredRawLogs.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Domain\\Command\\DeactivateExpiredRawLogsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Domain\\Command\\DeactivateRawLog.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Domain\\Command\\DeactivateRawLogHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Domain\\Dto\\CdnRawLog.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Domain\\Dto\\LogLine.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Domain\\Dto\\LogsSampleScope.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Domain\\Dto\\RawLogFile.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Domain\\Dto\\RawLogFileAttributes.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Domain\\Exception\\ActivationFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Domain\\Exception\\ActivationStatusChangeFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Domain\\Exception\\ActiveRawLogNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Domain\\Exception\\RawLogFileNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Domain\\FileSystem\\SampleLogFileSystem.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Domain\\Finder\\LocationIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Domain\\Finder\\RawLogCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Domain\\Query\\FindRawLogsForCustomer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Domain\\Query\\FindRawLogsForCustomerHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Domain\\Query\\FindSampleLogs.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Domain\\Query\\FindSampleLogsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Domain\\Query\\GetRawLogsSum.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Domain\\Query\\GetRawLogsSumHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Domain\\RawLogEnabler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Domain\\RawLogFileSystem.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Domain\\Repository\\RawLogRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Infrastructure\\FileSystem\\BaseLogFileSystem.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Infrastructure\\FileSystem\\LocalRawLogFileSystem.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Infrastructure\\FileSystem\\LocalSampleLogFileSystem.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Infrastructure\\Finder\\DbalLocationIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Infrastructure\\Finder\\DbalRawLogCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\RawLog\\Infrastructure\\Repository\\DoctrineRawLogRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Router\\AvailableEndpointMethods.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Server\\Application\\Console\\InsertNewServers.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Server\\Application\\Console\\SynchronizeNewServersRateCommand.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Server\\Application\\Console\\UpdateServersType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Server\\Application\\Controller\\ServersController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Server\\Application\\Payload\\ServerIdsListSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Server\\Domain\\Command\\InsertNewServers.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Server\\Domain\\Command\\InsertNewServersHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Server\\Domain\\Command\\SynchronizeNewServersRate.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Server\\Domain\\Command\\SynchronizeNewServersRateHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Server\\Domain\\Command\\UpdateServersType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Server\\Domain\\Command\\UpdateServersTypeHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Server\\Domain\\Dto\\Pop.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Server\\Domain\\Dto\\Server.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Server\\Domain\\Exception\\MCApiRequestFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Server\\Domain\\Finder\\ServerIdsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Server\\Domain\\Query\\GetServerIdsForGroup.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Server\\Domain\\Query\\GetServerIdsForGroupHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Server\\Domain\\Repository\\ServerRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Server\\Domain\\Value\\Description.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Server\\Domain\\Value\\LocationId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Server\\Domain\\Value\\PopId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Server\\Domain\\Value\\ServerType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Server\\Infrastructure\\Finder\\DoctrineServerIdsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Server\\Infrastructure\\Repository\\DoctrineServerRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Service\\PushZone\\Api\\ApiClient.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Service\\PushZone\\Api\\ApiClientInterface.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Service\\PushZone\\Api\\Endpoint\\ChangeZoneParamsEndpoint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Service\\PushZone\\Api\\Endpoint\\CreateUserZoneEndpoint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Service\\PushZone\\Api\\Endpoint\\GetNewStatsDeleteEndpoint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Service\\PushZone\\Api\\Endpoint\\GetNewStatsDetailEndpoint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Service\\PushZone\\Api\\Endpoint\\GetNewStatsListEndpoint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Service\\PushZone\\Api\\Endpoint\\NewUpdateZoneListEndpoint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Service\\PushZone\\Api\\Endpoint\\Payload\\GenericPushZoneApiResponsePayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Service\\PushZone\\Api\\Endpoint\\Payload\\GetNewStatsDeletePayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Service\\PushZone\\Api\\Endpoint\\Payload\\GetNewStatsDetailRequestPayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Service\\PushZone\\Api\\Endpoint\\Payload\\GetNewStatsDetailResponsePayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Service\\PushZone\\Api\\Endpoint\\Payload\\GetNewStatsDetailResponsePayloadStatistic.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Service\\PushZone\\Api\\Endpoint\\Payload\\GetNewStatsListPayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Service\\PushZone\\Api\\Endpoint\\Payload\\NewZonePayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Service\\PushZone\\Api\\Endpoint\\Payload\\NewZoneResponsePayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Service\\PushZone\\Api\\Endpoint\\Payload\\RenameStoragePayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Service\\PushZone\\Api\\Endpoint\\Payload\\UpdateZoneListPayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Service\\PushZone\\Api\\Endpoint\\Payload\\UpdateZoneListPayloadEntry.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Service\\PushZone\\Api\\Endpoint\\Payload\\UpdateZonePayload.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Service\\PushZone\\Api\\EndpointInterface.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Service\\PushZone\\Api\\Exception\\PushZoneException.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Service\\PushZone\\Api\\Exception\\RequestFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Service\\PushZone\\Api\\Exception\\ResponseFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Service\\ServiceException.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Application\\Console\\ComparePrivateKeysUuidsInNxgapi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Application\\Controller\\SniAddController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Application\\Controller\\SniCheckController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Application\\Controller\\SniDeleteController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Application\\Controller\\SniDetailController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Application\\Controller\\SniEditController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Application\\Controller\\SniListController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Application\\Docs\\CdnReferenceSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Application\\Docs\\EditSslSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Application\\Docs\\NewSslSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Application\\Docs\\SslListSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Application\\Docs\\SslSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Application\\Payload\\CdnReferenceSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Application\\Payload\\CertificateCnamesSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Application\\Payload\\CheckedSslSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Application\\Payload\\CheckSslSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Application\\Payload\\EditSslSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Application\\Payload\\NewSslSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Application\\Payload\\SslListSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Application\\Payload\\SslSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Command\\CheckSsl.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Command\\CheckSslHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Command\\ComparePrivateKey.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Command\\ComparePrivateKeyHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Command\\CreateSsl.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Command\\CreateSslHandler.php": {"errors": 3, "warnings": 0, "messages": [{"message": "Expected 0 lines between same types of use statement, found 1.", "source": "SlevomatCodingStandard.Namespaces.UseSpacing.IncorrectLinesCountBetweenSameTypeOfUse", "severity": 5, "fixable": true, "type": "ERROR", "line": 24, "column": 1}, {"message": "Type Sentry\\captureException is not used in this file.", "source": "SlevomatCodingStandard.Namespaces.UnusedUses.UnusedUse", "severity": 5, "fixable": true, "type": "ERROR", "line": 25, "column": 1}, {"message": "Expected 1 line between different types of use statement, found 0.", "source": "SlevomatCodingStandard.Namespaces.UseSpacing.IncorrectLinesCountBetweenDifferentTypeOfUse", "severity": 5, "fixable": true, "type": "ERROR", "line": 25, "column": 1}]}, "src\\Ssl\\Domain\\Command\\DeleteSsl.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Command\\DeleteSslHandler.php": {"errors": 3, "warnings": 0, "messages": [{"message": "Expected 0 lines between same types of use statement, found 1.", "source": "SlevomatCodingStandard.Namespaces.UseSpacing.IncorrectLinesCountBetweenSameTypeOfUse", "severity": 5, "fixable": true, "type": "ERROR", "line": 21, "column": 1}, {"message": "Type Sentry\\captureException is not used in this file.", "source": "SlevomatCodingStandard.Namespaces.UnusedUses.UnusedUse", "severity": 5, "fixable": true, "type": "ERROR", "line": 22, "column": 1}, {"message": "Expected 1 line between different types of use statement, found 0.", "source": "SlevomatCodingStandard.Namespaces.UseSpacing.IncorrectLinesCountBetweenDifferentTypeOfUse", "severity": 5, "fixable": true, "type": "ERROR", "line": 22, "column": 1}]}, "src\\Ssl\\Domain\\Command\\EditSsl.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Command\\EditSslHandler.php": {"errors": 3, "warnings": 0, "messages": [{"message": "Expected 0 lines between same types of use statement, found 1.", "source": "SlevomatCodingStandard.Namespaces.UseSpacing.IncorrectLinesCountBetweenSameTypeOfUse", "severity": 5, "fixable": true, "type": "ERROR", "line": 30, "column": 1}, {"message": "Expected 1 line between different types of use statement, found 0.", "source": "SlevomatCodingStandard.Namespaces.UseSpacing.IncorrectLinesCountBetweenDifferentTypeOfUse", "severity": 5, "fixable": true, "type": "ERROR", "line": 31, "column": 1}, {"message": "Type Sentry\\captureException is not used in this file.", "source": "SlevomatCodingStandard.Namespaces.UnusedUses.UnusedUse", "severity": 5, "fixable": true, "type": "ERROR", "line": 33, "column": 1}]}, "src\\Ssl\\Domain\\Contract\\SslId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Contract\\SslLegacyId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Dto\\ComparePrivateKeyResult.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Dto\\EditedSsl.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Dto\\PublicCertificateInfo.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Dto\\SslCheckResult.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Dto\\SslDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Dto\\SslToCheck.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Enum\\SslType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Exception\\CertificateExpired.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Exception\\CertificatePairDoesNotMatch.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Exception\\FailedToUpdateSsl.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Exception\\InvalidCertificate.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Exception\\InvalidPrivateKey.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Exception\\SslAlreadyExists.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Exception\\SslNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Exception\\SslRemovalFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Factory\\SslFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Finder\\SslAssignedCdnIdsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Finder\\SslFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Parser\\PublicCertificateParser.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Query\\GetSsl.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Query\\GetSslHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Query\\GetSslList.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Query\\GetSslListHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Repository\\CdnRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Repository\\SslDomainRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Repository\\SslRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\SetupSslDomains.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Validation\\CertificateValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Validation\\SslAccessValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Value\\Certificate.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Value\\PrivateKey.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Value\\SslCname.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Domain\\Value\\SslErrorType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Infrastructure\\Finder\\DbalSslAssignedCdnIdsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Infrastructure\\Finder\\DbalSslFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Infrastructure\\Repository\\DoctrineCdnRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Infrastructure\\Repository\\DoctrineSslDomainRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ssl\\Infrastructure\\Repository\\DoctrineSslRepository.php": {"errors": 1, "warnings": 0, "messages": [{"message": "Type Webmozart\\Assert\\Assert is not used in this file.", "source": "SlevomatCodingStandard.Namespaces.UnusedUses.UnusedUse", "severity": 5, "fixable": true, "type": "ERROR", "line": 16, "column": 1}]}, "src\\Ssl\\Infrastructure\\Validation\\DatabaseSslAccessValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Application\\Controller\\SumController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Application\\Controller\\UsageController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Application\\Docs\\UsageDetailSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Application\\Payload\\UsageDetailSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Application\\Payload\\UsageHistorySchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Application\\Payload\\UsageSumSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Console\\CollectRemoteServerStatisticsCommand.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Domain\\Command\\CollectAllRemoteServersStatistics.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Domain\\Command\\CollectAllRemoteServersStatisticsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Domain\\Command\\CollectRemoteServerStatistics.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Domain\\Command\\CollectRemoteServerStatisticsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Domain\\Dto\\Usage.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Domain\\Exception\\CollectingServersStatisticsFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Domain\\Exception\\CollectingServerStatisticsFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Domain\\Exception\\StatisticsNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Domain\\Finder\\StatisticsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Domain\\Query\\FindHistoricalUsageStatisticsForPeriod.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Domain\\Query\\FindHistoricalUsageStatisticsForPeriodHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Domain\\Query\\GetCurrentStorageUsageStatistics.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Domain\\Query\\GetCurrentStorageUsageStatisticsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Domain\\Query\\GetUsageForCustomer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Domain\\Query\\GetUsageForCustomerHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Domain\\Repository\\ActualStatisticsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Domain\\Repository\\StatisticsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Domain\\StatisticsCollector.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Domain\\StatisticsCollectorInterface.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Infrastructure\\Finder\\DbalStatisticsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Infrastructure\\Repository\\DoctrineActualStatisticsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Statistics\\Infrastructure\\Repository\\DoctrineStatisticsRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Controller\\CacheStatsChartDataController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Controller\\CdnSumsController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Controller\\CountryDestination\\GetStatsController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Controller\\DatacentersBandwidthPercentileController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Controller\\DatacenterSumsController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Controller\\GetBandwidthPercentileController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Controller\\GetCacheStatsController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Controller\\GetCacheStatsForDatacenterController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Controller\\GetCacheStatsForResourceController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Controller\\GetCountrySpendingStatsController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Controller\\GetStatsController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Controller\\GetStatsForDatacentersController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Controller\\GetStatsForResourceController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Controller\\GetTikTokSpendingStatsController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Controller\\GetTikTokSpendingStatsWithoutRateController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Controller\\InternalCacheStatsController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Controller\\MultiSumController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Controller\\PopularObjectsController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Controller\\StatsChartDataController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Controller\\SumController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\BandwidthPercentileSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\BaseGetStatsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\CdnSumsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\ChartDataSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\ChartSeriesSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\ContinentPercentileSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\ContinentPercentilesSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\ContinentStatsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\ContinentSumsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\CountryDestination\\CountriesDestinationStatsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\CountryDestination\\CountryDestinationStatsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\CountryDestination\\CountryDetailSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\CountryDestination\\GetStatsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\CountryDestination\\RequestsStatsDataSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\CountryDestination\\StatsDataSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\CountryDestination\\StatusCodesStatsDataSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\CountryDestination\\TrafficStatsDataSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\DatacenterPercentileSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\DatacentersStatsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\DatacenterStatsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\DatacenterSumsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\GetBandwidthPercentileSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\GetCacheStatsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\GetMultiSumSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\GetStatsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\GetSumSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\MultiSumSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\PopularObjects\\GetPopularObjectsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\PopularObjects\\PopularObjectSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\PopularObjects\\PopularObjectsStatsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\RegionDetailSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\RegionTrafficSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\RequestQueryScopeSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\ResourcesStatsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\SpendingStats\\CountriesStatsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\SpendingStats\\CountryStatsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\SpendingStats\\GetCountrySpendingStatsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\SpendingStats\\GetRegionStatsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\SpendingStatsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\SpendingStatsWithoutRatesSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\StatsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\SumSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\TimestampStatsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\TimestampSumsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Application\\Payload\\Validation\\AggregationPropertyValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Console\\UpdateBandwidthPercentileCache.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Console\\UpdateDatacenterTraffic.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Aggregation\\AggregationResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Aggregation\\FrequencyCalculator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Aggregation\\RecordCountResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Aggregation\\Rounder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\BandwidthPercentileCalculator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Cache\\BandwidthPercentileCache.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\CacheStatsResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Chart\\ChartDataConverter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Chart\\Dto\\ChartData.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Chart\\Dto\\ChartSeries.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Chart\\Dto\\SeriesName.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Chart\\EmptyTimestampsResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\ClickHouse\\ResultGroupingCdn.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\ClickHouse\\ResultGroupingNone.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\ClickHouse\\ResultGroupingServer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\ClickHouse\\TableNameResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Command\\SaveBandwidthPercentile.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Command\\SaveBandwidthPercentileHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Command\\SaveDatacenterTraffic.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Command\\SaveDatacenterTrafficHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\DataTable\\Dto\\ContinentData.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\DataTable\\Dto\\DatacenterData.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\DataTable\\Dto\\TimestampTotals.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\DataTable\\TableDataConverter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\DataTable\\TimestampTotalsResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\CdnSums.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\ContinentStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\ContinentSums.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\Country.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\CountryDestination\\CountriesDestinationStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\CountryDestination\\CountryDestinationStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\CountryDestination\\CountryDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\CountryDestination\\RequestsStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\CountryDestination\\StatsData.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\CountryDestination\\StatusCodesStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\CountryDestination\\TrafficStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\CustomerStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\Datacenter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\DatacenterStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\DatacenterSums.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\PopularObject.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\PopularObjectsRequest.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\RegionDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\RegionLocation.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\RegionStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\ResourceStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\Scope\\BandwidthPercentileRequestScope.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\Scope\\CacheStatsRequestScope.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\Scope\\MultiSumRequestScope.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\Scope\\StatsRequestScope.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\ServerDatacenter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\SpendingStats\\CountriesStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\SpendingStats\\CountryStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Dto\\Stats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Exception\\CannotAggregateValuesForInterval.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Exception\\ChartDataConversionFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Exception\\FailedToGetStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Exception\\FailedToGetSum.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Exception\\FailedToResolveServerIds.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Exception\\FetchingPopularObjectsFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Exception\\InvalidAggregationUnit.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Exception\\StatsAccessNotAllowed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Exception\\TikTokRegionStatsAccessNotAllowed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Factory\\ContinentStatsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Factory\\CountrySpendingStatsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Factory\\CustomerStatsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Fetcher\\BandwidthPercentileFetcher.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Finder\\CdnIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Finder\\CustomerIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Finder\\CustomPlanCustomersIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Finder\\DataCenterTrafficFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Finder\\LatestDatacenterTrafficDateFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Finder\\RegionLocationFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Finder\\ServerCityCodeFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Finder\\ServerDatacenterFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Finder\\ServerIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Finder\\TikTokCountryTrafficFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\IntervalAggregationValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\OverviewCdnIdsResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\PopularObjects\\PopularObjectsReportApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Provider\\CountryDestinationLivestreamingStatsProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Provider\\CountryDestinationStatsProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\CountryDestination\\GetStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\CountryDestination\\GetStatsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\FindCustomerStats\\Input\\SelectionField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetBandwidthPercentile.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetBandwidthPercentileHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetCacheStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetCacheStatsChartData.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetCacheStatsChartDataHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetCacheStatsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetCdnSums.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetCdnSumsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetContinentCacheStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetContinentCacheStatsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetContinentStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetContinentStatsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetContinentSums.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetContinentSumsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetCountrySpendingStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetCountrySpendingStatsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetDatacentersBandwidthPercentile.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetDatacentersBandwidthPercentileHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetMultiSumForTypes.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetMultiSumForTypesHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetOverviewStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetOverviewStatsChartData.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetOverviewStatsChartDataHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetOverviewStatsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetPopularObjects.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetPopularObjectsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetStatsChartData.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetStatsChartDataHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetStatsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetSum.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetSumHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetTikTokRegionStats.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Query\\GetTikTokRegionStatsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Repository\\CountryRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Repository\\DatacenterTrafficRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Repository\\RegionRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Repository\\ServerRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Resolver\\CdnIdResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Resolver\\CommonStatsResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Resolver\\CustomerServerIdResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Resolver\\OverviewServerIdResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Resolver\\StatsResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Resolver\\SumResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\StatsEnricher.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\TikTokRegionStatsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\TikTokTrafficDaySumsResolver.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Validation\\CacheStatsAccessValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Validation\\TikTokRegionStatsAccessValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Value\\Aggregation.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Value\\BandwidthPercentileCacheKey.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Value\\CacheLifeTime.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Value\\ClickHouse\\Fields.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Value\\CountryDestinationStatsType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Value\\DailySumByDataCenter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Value\\DataCenterTrafficType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Value\\DataSource.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Value\\Fields.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Value\\GroupBy.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Value\\OverviewSubType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Value\\OverviewType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Value\\RegionCriteria.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Value\\RegionName.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Value\\RegionRate.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Value\\SortBy.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Value\\StatsType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Value\\StreamingFormatType.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Domain\\Value\\Sum.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Infrastructure\\Cache\\RedisBandwidthPercentileCache.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Infrastructure\\Finder\\DbalCdnIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Infrastructure\\Finder\\DbalCustomerIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Infrastructure\\Finder\\DbalCustomPlanCustomersIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Infrastructure\\Finder\\DbalDataCenterTrafficFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Infrastructure\\Finder\\DbalLatestDatacenterTrafficDateDateFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Infrastructure\\Finder\\DbalRegionLocationFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Infrastructure\\Finder\\DbalServerCityCodeFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Infrastructure\\Finder\\DbalServerDatacenterFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Infrastructure\\Finder\\DbalServerIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Infrastructure\\Finder\\DbalTikTokCountryTrafficFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Infrastructure\\PopularObjects\\HttpPopularObjectsReportApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Infrastructure\\Provider\\ClickHouseCountryDestinationLivestreamingStatsProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Infrastructure\\Provider\\ClickHouseCountryDestinationStatsProvider.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Infrastructure\\Repository\\DoctrineCountryRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Infrastructure\\Repository\\DoctrineDatacenterTrafficRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Infrastructure\\Repository\\DoctrineRegionRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Stats\\Infrastructure\\Repository\\DoctrineServerRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Application\\Controller\\AddController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Application\\Controller\\DeleteController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Application\\Controller\\DetailController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Application\\Controller\\EditController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Application\\Controller\\InternalCustomerListController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Application\\Controller\\ListController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Application\\Payload\\CredentialsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Application\\Payload\\DetailSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Application\\Payload\\EditStorageSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Application\\Payload\\InternalDetailSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Application\\Payload\\InternalListSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Application\\Payload\\ListSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Application\\Payload\\NewStorageSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Application\\Payload\\ServerSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Console\\UpdateRemoteServerCommand.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Command\\CreateStorage.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Command\\CreateStorageHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Command\\DeleteStorage.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Command\\DeleteStorageHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Command\\EditStorage.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Command\\EditStorageHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Dto\\EditedStorage.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Dto\\NewStorage.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Exception\\CouldNotCreateStorage.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Exception\\CouldNotDeleteStorage.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Exception\\SecretAllocationFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Exception\\ServersUpdateFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Exception\\ServerUpdateFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Exception\\StorageAlreadyDeleted.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Exception\\StorageNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Exception\\UserNameAllocationFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Factory\\StorageFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Finder\\StorageIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Finder\\StorageSecretFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Query\\FindAccountStorages.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Query\\FindAccountStoragesHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Query\\GetStorageByIdForCustomer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Query\\GetStorageByIdForCustomerHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Query\\UpdateStorageOnAllRemoteServers.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Query\\UpdateStorageOnAllRemoteServersHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Query\\UpdateStoragesOnRemoteServer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Query\\UpdateStoragesOnRemoteServerHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Repository\\StorageRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\ResurrectCustomerStorages.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\SecretAllocator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\SecretAllocatorInterface.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\UserNameAllocator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\UserNameAllocatorInterface.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Validation\\StorageLabelValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Value\\StoragePassword.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Value\\StorageSecret.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Value\\StorageUri.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Domain\\Value\\StorageUserName.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Infrastructure\\Finder\\DoctrineStorageIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Infrastructure\\Finder\\DoctrineStorageSecretFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Storage\\Infrastructure\\Repository\\DoctrineStorageRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\StorageLocation\\Application\\Docs\\LocationSchemaSpec.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\StorageLocation\\Application\\Payload\\LocationSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\StorageLocation\\Application\\Payload\\LocationsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\StorageLocation\\Domain\\Exception\\CannotFindStorageServer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\StorageLocation\\Domain\\Exception\\InvalidStorageServer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\StorageLocation\\Domain\\Repository\\StorageServerRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\StorageLocation\\Infrastructure\\Repository\\DoctrineStorageServerRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\StoragePlan\\Application\\Controller\\DetailController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\StoragePlan\\Application\\Controller\\InternalListController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\StoragePlan\\Application\\Controller\\UsageController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\StoragePlan\\Application\\Payload\\StoragePlanDetailSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\StoragePlan\\Application\\Payload\\StoragePlanSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\StoragePlan\\Application\\Payload\\StoragePlansSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\StoragePlan\\Application\\Payload\\UsageSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\StoragePlan\\Domain\\Dto\\StoragePlanDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\StoragePlan\\Domain\\Exception\\StoragePlanNotFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\StoragePlan\\Domain\\Query\\GetCustomerStoragePlanDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\StoragePlan\\Domain\\Query\\GetCustomerStoragePlanDetailHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\StoragePlan\\Domain\\Query\\GetStoragePlans.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\StoragePlan\\Domain\\Query\\GetStoragePlansHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\StoragePlan\\Domain\\Query\\GetStorageUsage.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\StoragePlan\\Domain\\Query\\GetStorageUsageHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\StoragePlan\\Domain\\Repository\\StorageAddOnRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\StoragePlan\\Domain\\Repository\\StoragePlanRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\StoragePlan\\Domain\\Value\\StorageUsage.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\StoragePlan\\Infrastructure\\Repository\\DoctrineStorageAddOnRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\StoragePlan\\Infrastructure\\Repository\\DoctrineStoragePlanRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Tariff\\Domain\\Command\\EditCredit.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Tariff\\Domain\\Command\\EditCreditHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Tariff\\Domain\\Command\\RecountCredit.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Tariff\\Domain\\Command\\RecountCreditHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Tariff\\Domain\\CreditAvailabilityEstimateCalculator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Tariff\\Domain\\CreditWithdrawalLogger.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Tariff\\Domain\\Dto\\CreditDetail.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Tariff\\Domain\\Exception\\CreditBalanceNotAvailable.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Tariff\\Domain\\Exception\\NoActiveTariffFound.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Tariff\\Domain\\Factory\\TariffFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Tariff\\Domain\\Finder\\TariffCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Tariff\\Domain\\Finder\\TariffStartDateFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Tariff\\Domain\\Query\\GetAvailableCredit.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Tariff\\Domain\\Query\\GetAvailableCreditHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Tariff\\Domain\\Repository\\TariffLogRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Tariff\\Domain\\Repository\\TariffRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Tariff\\Infrastructure\\Finder\\DbalTariffStartDateFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Tariff\\Infrastructure\\Finder\\DoctrineTariffCountFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Tariff\\Infrastructure\\Repository\\DoctrineTariffLogRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Tariff\\Infrastructure\\Repository\\DoctrineTariffRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Application\\Console\\SynchronizeKayakoTicketsCommand.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Domain\\Api\\KayakoApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Domain\\Command\\CreateTicket.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Domain\\Command\\CreateTicketHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Domain\\Command\\SynchronizeKayakoTickets.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Domain\\Command\\SynchronizeKayakoTicketsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Domain\\Enum\\DepartmentId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Domain\\Enum\\SortField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Domain\\Enum\\StatusId.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Domain\\Enum\\TicketPriority.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Domain\\Exception\\KayakoCallFailed.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Domain\\Finder\\CustomerIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Domain\\Finder\\KayakoTicketsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Domain\\Query\\FindKayakoTickets\\FindKayakoTickets.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Domain\\Query\\FindKayakoTickets\\FindKayakoTicketsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Domain\\Query\\FindKayakoTickets\\Input\\Filter.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Domain\\Query\\FindKayakoTickets\\Input\\SelectionField.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Domain\\Query\\FindKayakoTickets\\Output\\KayakoTicket.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Domain\\Query\\FindKayakoTickets\\Output\\KayakoTickets.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Domain\\Value\\NewTicket.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Domain\\Value\\Scope.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Domain\\Value\\Ticket.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Infrastructure\\Api\\CreateTicketEndpoint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Infrastructure\\Api\\GetTicketsEndpoint.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Infrastructure\\Api\\HttpKayakoApi.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Infrastructure\\Api\\KayakoResponseDeserializer.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Infrastructure\\Finder\\DbalCustomerIdFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\Ticket\\Infrastructure\\Finder\\DbalKayakoTicketsFinder.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\TikTok\\Application\\Controller\\ChangeRequestListController.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\TikTok\\Application\\Payload\\ChangeRequestSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\TikTok\\Application\\Payload\\ChangeRequestsSchema.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\TikTok\\Domain\\Authorization\\AccessValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\TikTok\\Domain\\Exception\\UnauthorizedAccess.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\TikTok\\Domain\\Query\\FindChangeRequests.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\TikTok\\Domain\\Query\\FindChangeRequestsHandler.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\TikTok\\Domain\\Repository\\ChangeRequestRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\TikTok\\Domain\\Value\\Action.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\TikTok\\Infrastructure\\Authorization\\DatabaseAccessValidator.php": {"errors": 0, "warnings": 0, "messages": []}, "src\\TikTok\\Infrastructure\\Repository\\DoctrineChangeRequestRepository.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\04\\Version20220420150600.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\04\\Version20220421114000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\04\\Version20220422202132.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\04\\Version20220425114029.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\05\\Version20220502093610.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\05\\Version20220502125932.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\05\\Version20220503111702.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\05\\Version20220511114009.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\05\\Version20220517081641.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\05\\Version20220519060943.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\05\\Version20220525064606.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\05\\Version20220526161453.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\05\\Version20220526180948.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\06\\Version20220602134352.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\06\\Version20220609114447.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\06\\Version20220613131242.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\06\\Version20220617133000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\06\\Version20220617134644.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\06\\Version20220620085514.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\06\\Version20220627091808.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\06\\Version20220629134556.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\07\\Version20220701143122.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\07\\Version20220720120820.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\07\\Version20220722133000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\07\\Version20220729125727.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\08\\Version20220802092545.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\08\\Version20220807134304.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\08\\Version20220810104245.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\08\\Version20220819084702.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\08\\Version20220822125652.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\08\\Version20220822162143.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\08\\Version20220823065830.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\08\\Version20220830060234.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\08\\Version20220830131638.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\09\\Version20220901173151.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\09\\Version20220913085129.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\09\\Version20220927101703.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\10\\Version20221010110346.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\10\\Version20221010140442.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\10\\Version20221017093235.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\10\\Version20221028151626.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\10\\Version20221029175305.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\11\\Version20221110141530.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\11\\Version20221118155740.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\11\\Version20221123121236.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\11\\Version20221125121500.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\11\\Version20221129110000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\12\\Version20221201154500.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\12\\Version20221202143000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2022\\12\\Version20221212170717.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\01\\Version20230105082916.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\01\\Version20230111123000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\01\\Version20230124144500.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\01\\Version20230125133901.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\01\\Version20230126140053.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\01\\Version20230130145118.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\02\\Version20230201163000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\02\\Version20230202145753.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\02\\Version20230206113424.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\02\\Version20230227100000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\03\\Version20230313154636.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\03\\Version20230316144425.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\03\\Version20230324124500.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\03\\Version20230324163802.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\03\\Version20230331100602.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\04\\Version20230403163000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\04\\Version20230406100000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\04\\Version20230406131500.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\04\\Version20230406152000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\04\\Version20230414111254.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\04\\Version20230419143000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\04\\Version20230425160000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\04\\Version20230428123500.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\05\\Version20230503134500.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\05\\Version20230509154146.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\05\\Version20230529130107.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\06\\Version20230602134629.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\06\\Version20230605143000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\06\\Version20230605173203.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\06\\Version20230606145129.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\06\\Version20230608080244.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\06\\Version20230613153730.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\06\\Version20230613171039.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\06\\Version20230624091521.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\06\\Version20230624094051.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\07\\Version20230717154000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\07\\Version20230717163000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\07\\Version20230720113000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\07\\Version20230727191026.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\07\\Version20230731061523.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\07\\Version20230731070558.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\08\\Version20230814135718.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\08\\Version20230815153000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\08\\Version20230824135055.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\08\\Version20230824143004.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\08\\Version20230830140000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\08\\Version20230831131532.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\09\\Version20230904090000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\09\\Version20230904103000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\09\\Version20230907104349.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\09\\Version20230907124440.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\09\\Version20230908045913.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\09\\Version20230908143000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\09\\Version20230911053004.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\09\\Version20230913134509.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\09\\Version20230914070848.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\09\\Version20230914143000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\10\\Version20231003123000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\10\\Version20231011084842.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\10\\Version20231011191909.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\10\\Version20231013112336.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\10\\Version20231019084356.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\10\\Version20231020110616.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\10\\Version20231026094137.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\11\\Version20231106113000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\11\\Version20231114110325.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\11\\Version20231121151252.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\11\\Version20231121152653.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\11\\Version20231123152500.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\11\\Version20231127103432.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\11\\Version20231127155500.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\11\\Version20231129120519.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\12\\Version20231205143500.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\12\\Version20231211162809.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\12\\Version20231212161046.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\12\\Version20231219121929.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\12\\Version20231220144719.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\12\\Version20231221103000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2023\\12\\Version20231227155000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\01\\Version20240105103000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\01\\Version20240115103000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\01\\Version20240125102112.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\02\\Version20240205140500.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\02\\Version20240207102500.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\02\\Version20240208164500.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\02\\Version20240208165000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\02\\Version20240229153000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\03\\Version20240320111642.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\03\\Version20240320112927.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\03\\Version20240328103000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\04\\Version20240412202733.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\04\\Version20240417122500.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\05\\Version20240522133633.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\05\\Version20240524103000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\05\\Version20240524122500.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\05\\Version20240526171039.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\05\\Version20240527124604.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\05\\Version20240528094744.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\05\\Version20240528133810.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\05\\Version20240529120214.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\05\\Version20240619144500.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\06\\Version20240603111344.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\06\\Version20240613100856.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\06\\Version20240617162000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\06\\Version20240619073102.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\06\\Version20240625123251.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\06\\Version20240628134157.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\07\\Version20240702082025.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\07\\Version20240702091652.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\07\\Version20240708131714.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\07\\Version20240715134142.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\07\\Version20240716130000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\07\\Version20240716140000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\07\\Version20240722101542.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\07\\Version20240729094042.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\07\\Version20240729122656.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\07\\Version20240730093304.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\07\\Version20240730103000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\08\\Version20240812135500.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\08\\Version20240813113803.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\08\\Version20240822152732.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\08\\Version20240826084902.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\08\\Version20240826135017.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\08\\Version20240828094621.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\09\\Version20240903080544.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\09\\Version20240909100000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\09\\Version20240919113329.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\09\\Version20240924150500.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\09\\Version20240924164000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\09\\Version20240925142318.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\09\\Version20240926122227.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\10\\Version20241002162847.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\10\\Version20241011124500.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\10\\Version20241031143941.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\11\\Version20241112160327.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\11\\Version20241117192713.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\11\\Version20241118131445.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\11\\Version20241121101010.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\11\\Version20241125161500.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\12\\Version20241204121500.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\12\\Version20241205172518.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\12\\Version20241206152506.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\12\\Version20241211123052.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\12\\Version20241213170428.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2024\\12\\Version20241217125134.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\01\\Version20250110122153.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\01\\Version20250110125437.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\01\\Version20250114113000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\01\\Version20250121122500.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\01\\Version20250131172406.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\02\\Version20250206134500.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\02\\Version20250209143254.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\02\\Version20250209153154.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\02\\Version20250212152309.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\02\\Version20250212190748.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\02\\Version20250214105000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\02\\Version20250219144500.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\03\\Version20250305142414.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\03\\Version20250310163745.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\03\\Version20250314110000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\03\\Version20250318134733.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\03\\Version20250318142408.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\03\\Version20250319100525.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\03\\Version20250320111311.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\03\\Version20250320112854.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\03\\Version20250320152147.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\03\\Version20250326210913.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\04\\Version20250404120815.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\04\\Version20250405150804.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\04\\Version20250410113000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\04\\Version20250414070918.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\04\\Version20250414094203.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\04\\Version20250414114701.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\04\\Version20250416070842.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\04\\Version20250423162135.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\04\\Version20250428134852.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\04\\Version20250429125640.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\05\\Version20250505113000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\05\\Version20250508155210.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\05\\Version20250512085504.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\05\\Version20250521131244.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\05\\Version20250526142415.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\05\\Version20250530105058.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\05\\Version20250530170500.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\06\\Version20250603135131.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\06\\Version20250610131029.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\06\\Version20250612165621.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\06\\Version20250616121333.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\06\\Version20250624173101.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\06\\Version20250626084750.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\06\\Version20250626134500.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\07\\Version20250708120247.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\07\\Version20250711105500.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\07\\Version20250716105102.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\07\\Version20250730103000.php": {"errors": 0, "warnings": 0, "messages": []}, "migrations\\2025\\08\\Version20250803161105.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Api\\Domain\\Query\\IsRequestRestrictedHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Api\\EventListener\\RequestListenerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Api\\Infrastructure\\Logger\\DbalRequestLoggerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Api\\Infrastructure\\Request\\SensitiveInformationObfuscatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Authentication\\Application\\Symfony\\Authorization\\AccessResolverTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Authentication\\Domain\\Command\\CreatePersonalTokenHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Authentication\\Domain\\Command\\DisableTwoFactorAuthenticationHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Authentication\\Domain\\Command\\EnableTwoFactorAuthenticationHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Authentication\\Domain\\Command\\VerifyCredentialsHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Authentication\\Domain\\Dto\\OneTimePasswordTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Authentication\\Domain\\Query\\GetTwoFactorAuthenticationSetupHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Authentication\\Domain\\TokenAccessValidatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Authentication\\Domain\\Value\\AuthorizationHeaderTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Authentication\\Domain\\Value\\TokenSecretTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Authentication\\Domain\\Value\\TokenTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\bootstrap.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Application\\Controller\\AddControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Application\\Controller\\DeleteControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Application\\Controller\\DetailControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Application\\Controller\\EditControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Application\\Controller\\ListControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Application\\Controller\\Log\\CreateRealTimeLogControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Application\\Controller\\Log\\ListActiveRealTimeLogControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Application\\Controller\\Log\\ListCustomerRealTimeLogControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Application\\Controller\\Log\\ListRealTimeLogFieldsControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Application\\Controller\\Log\\RealTimeLogDetailControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Application\\Controller\\ResponseHeaders\\EditControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Application\\Payload\\AccessProtection\\HotlinkProtectionSchemaTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Application\\Payload\\AccessProtection\\IpProtectionSchemaSerDeTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Application\\Payload\\Cache\\CacheSchemaTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Application\\Payload\\HttpsRedirect\\HttpsRedirectSchemaTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Application\\Payload\\OriginHeaders\\OriginHeadersSchemaTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Application\\Payload\\Redirect\\FollowRedirectSchemaTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Application\\Payload\\ResponseHeaders\\ResponseHeadersSchemaTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Application\\Payload\\Ssl\\SslSchemaTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\AccessProtection\\SetupGeoProtectionTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\AccessProtection\\SetupHotlinkProtectionTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\AccessProtection\\SetupIpProtectionTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\Command\\ChangeLocationGroupHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\Command\\DeleteSuspendedCdnsHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\Command\\EditStreamCdnHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\Command\\RemoveCdnHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\Command\\Tsunami\\DisableTsunamiHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\Command\\Tsunami\\EnableTsunamiHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\Command\\Tsunami\\UpdateTsunamiHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\Configuration\\CdnCnamesConfiguratorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\Configuration\\CdnHttpConfiguratorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\Configuration\\CdnSslConfiguratorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\Dto\\HttpsRedirectSettingsTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\Factory\\CdnFactoryTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\Finder\\CdnIdFinderTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\ForbiddenDatacentersMapTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\Query\\FindStreamCdnsForCustomerHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\QueryString\\SetupIgnoredQueryParamsTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\Tsunami\\CdnResourceTargetsResolverTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\Validation\\MaxAgeValidatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\Validation\\RealTimeLogFieldsValidatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\Value\\Forman\\DataSinkTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\Value\\Log\\FiltersTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\Value\\Log\\LoggedRequestsPricingTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\Value\\Log\\LoggingFieldsTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\Value\\Log\\LogPathTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\Value\\Log\\RealTimeLogConfigurationTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\Value\\Parameters\\CustomOriginHeadersTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\Value\\Parameters\\CustomResponseHeadersTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\Value\\Parameters\\FollowRedirectTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Domain\\Value\\SecureTokenConfigTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Infrastructure\\Factory\\DbalCdnResourceIdFactoryTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Infrastructure\\Quota\\DatabaseCreatedCdnQuotaCheckerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Infrastructure\\Quota\\DatabaseDeletedCdnQuotaCheckerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cdn\\Infrastructure\\Repository\\DoctrineCdnRepositoryTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cname\\Application\\Controller\\ListControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cname\\Domain\\Command\\AddCnameForCdnHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cname\\Domain\\Dto\\CnameDnsRecordTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Cname\\Domain\\Validation\\CnameValidatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Application\\Console\\Output\\ConsoleOutputBufferTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Application\\Console\\Value\\LockNameTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Application\\Controller\\DocumentationJsonControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Application\\Controller\\InternalDocumentationJsonControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Application\\EventHandler\\Handler\\MethodNotAllowedHttpExceptionHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Application\\EventHandler\\Handler\\NotFoundHttpExceptionHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Application\\Payload\\ErrorsSchemaTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Application\\Payload\\FieldsErrorsSchemaTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Application\\Payload\\SchemaPropertyValidatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Application\\Request\\ControllerSchemaSerializerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Application\\Response\\ExceptionHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Application\\Symfony\\Authenticator\\InternalTokenAuthenticatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Application\\Symfony\\Authenticator\\JwtAuthenticatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Application\\Symfony\\Authenticator\\TokenAuthenticatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Application\\Symfony\\User\\LoggedCustomerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Billing\\Converter\\VatRateDbConverterTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Billing\\Formatter\\VatRateFormatterTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Billing\\InvoiceDueDateCalculatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Billing\\Validation\\InvoiceDateValidatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Billing\\Value\\BillingDescriptionTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Billing\\Value\\BillingPeriodTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Billing\\Value\\VatRateTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Cdn\\Configuration\\CdnOriginConfiguratorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Ceph\\PolicyConfiguratorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Command\\RefreshOAuthTokenHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\CommandBufferTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Dto\\Cname\\CnameTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Dto\\Origin\\S3OriginTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Dto\\Origin\\StorageOriginTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Dto\\Origin\\StreamOriginTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Dto\\Origin\\UrlOriginTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Dto\\TimeRangeTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Encryption\\CredentialsEncryptorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Encryption\\ValueEncryptorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\AddOn\\AddOnTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Api\\ApplicationTokenTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Api\\PersonalTokenTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Authentication\\OAuthTokenTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Cdn\\CdnHttpProtectionTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Cdn\\CdnHttpTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Cdn\\CdnTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Cdn\\HotlinkProtectionDomainTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Cname\\CnameTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Customer\\AccountFlagsTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Customer\\AccountSettingsTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Customer\\CredentialsTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Customer\\CustomerIdTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Customer\\CustomerOriginSettingsTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Customer\\CustomerPaymentSettingsTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Customer\\CustomerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\CustomPlan\\ContractTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Datacenter\\LocationTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\DataManipulation\\PrefetchTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\DataManipulation\\PurgeAllTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\DataManipulation\\PurgeTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Inquiry\\InquiryTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Invoice\\CountryTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Invoice\\InvoiceCustomerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\MonthlyTrafficPlan\\CustomerMonthlyTrafficPlanTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\MonthlyTrafficPlan\\MonthlyTrafficPlanTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Origin\\OriginTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Payment\\PaymentRecipeTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Payment\\PaymentTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Plan\\PlanLocationTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Plan\\PlanTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Promo\\PromoCodeTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Promo\\PromoCodeUsageTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\RawLogs\\RawLogTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Storage\\ServerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Storage\\StorageTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Tariff\\CreditTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Tariff\\TariffLogTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\Tariff\\TariffTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Entity\\TikTok\\ChangeRequestTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Plan\\CustomPlanTerminatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Plan\\MonthlyPlanTerminatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\RateLimit\\RateLimitCheckerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Resolver\\AffectedCustomerResolverTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Stats\\StatsProviderBagTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Stats\\StatsSourceSelectorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\TeamMember\\TeamMemberAccessConfiguratorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Validation\\OriginUrlValidatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Value\\AccessRestriction\\ResourceRestrictionTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Value\\BytesTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Value\\CidrTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Value\\Customer\\EmailAddressTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Value\\Customer\\NameTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Value\\Customer\\SuspensionReasonTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Value\\Enums\\EnumValuesTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Value\\Identifier\\IdentifierMapperTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Value\\IpAddressTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Value\\ObjectStorage\\BucketNameTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Value\\Origin\\BaseDirectoryTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Domain\\Value\\SecondsTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Infrastructure\\Ara\\HttpAraTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Infrastructure\\Currency\\BrickCurrencyConverterTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Infrastructure\\Currency\\ExchangeRateProviderFactoryTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Infrastructure\\Finder\\Cname\\DoctrineCnameFinderTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Infrastructure\\Http\\UriObfuscatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Infrastructure\\NxgApi\\HttpNxgApiTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Infrastructure\\NxgApi\\ResourceFactoryTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Infrastructure\\RateLimit\\SymfonyRateLimiterTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Infrastructure\\Symfony\\SymfonyLoggedAccountProviderTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Infrastructure\\Validation\\DatabaseCdnAccessValidatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Core\\Infrastructure\\Validation\\DbalTeamMemberAccessValidatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CoreLibrary\\ClickHouse\\RounderTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CoreLibrary\\Doctrine\\Dbal\\Middleware\\Configuration\\Value\\ApplicationNameTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CoreLibrary\\Doctrine\\Dbal\\Types\\IntervalTypeTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CoreLibrary\\Ds\\SetSerDeTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CoreLibrary\\Money\\MoneyHelperTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CoreLibrary\\Utils\\StringTruncatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CoreLibrary\\Validation\\FQDNValidatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CoreLibrary\\Validation\\RegexTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CoreLibrary\\ValueResolver\\ArgumentMetaDataFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CoreLibrary\\ValueResolver\\IdentifierValueResolverTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CoreLibrary\\ValueResolver\\IpAddressValueResolverTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Application\\Controller\\AddFromSignUpInviteControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Application\\Controller\\AddTeamMemberControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Application\\Controller\\ChangePasswordControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Application\\Controller\\CreditBalanceControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Application\\Controller\\DeleteTeamMemberControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Application\\Controller\\DetailControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Application\\Controller\\InternalDetailControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Application\\Payload\\DetailSchemaTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Application\\Payload\\TeamMember\\AccessRestrictionSchemaTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Application\\Payload\\Validator\\SchemaPasswordPropertyValidatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Application\\Payload\\Value\\EstimatedTrafficTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Domain\\AccountQuotaTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Domain\\ApiPasswordEncryptorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Domain\\ClientLinkGeneratorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Domain\\Command\\AddLeadToPipedriveSerDeTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Domain\\Command\\ProcessPasswordResetRequestHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Domain\\Command\\ScheduleSuspendCustomerHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Domain\\Command\\TeamMember\\DeleteTeamMemberSerDeTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Domain\\Command\\UnsuspendCustomerHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Domain\\Command\\VerifyCustomerPasswordHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Domain\\Dto\\CustomerCreditInfoTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Domain\\EmailAddressConfirmationLinkSenderTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Domain\\Enum\\CustomerSlugTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Domain\\Limit\\PasswordChangeRequestRateLimiterTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Domain\\Limit\\PasswordVerifyRequestRateLimiterTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Domain\\PasswordHasherTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Domain\\ResetPasswordRequestsInvalidatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Domain\\Resolver\\CustomerBillingStatusResolverTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Domain\\Resolver\\PricingTypeResolverTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Domain\\SignInTokenGeneratorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Domain\\TeamMemberAccessConfiguratorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Domain\\Validation\\SignUpInviteValidatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Domain\\Value\\EncryptedPasswordTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Domain\\Value\\PasswordHashTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Customer\\Infrastructure\\Factory\\CustomerFactoryTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CustomPlan\\Application\\Controller\\AddContractControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CustomPlan\\Application\\Controller\\AddCustomPlanControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CustomPlan\\Application\\Payload\\ActiveCustomPlanSchemaTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CustomPlan\\Application\\Payload\\ContractSchemaTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CustomPlan\\Application\\Payload\\CustomPlanContractDetailSchemaTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CustomPlan\\Domain\\ActiveCustomPlanComparatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CustomPlan\\Domain\\Dto\\DescriptionLinesTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CustomPlan\\Domain\\Dto\\InvoiceLineTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CustomPlan\\Domain\\Dto\\MailGreetingTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CustomPlan\\Domain\\Dto\\ServicePeriodTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CustomPlan\\Domain\\EstimatedTrafficCalculatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CustomPlan\\Domain\\Factory\\InvoiceFactoryTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CustomPlan\\Domain\\Factory\\PlanFactoryTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CustomPlan\\Domain\\MonthlyRevenueCalculatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CustomPlan\\Domain\\Query\\GetContractPaymentDetailHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CustomPlan\\Domain\\Query\\GetCustomPlansOverviewHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CustomPlan\\Domain\\Validation\\InvoiceConfigurationValidatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CustomPlan\\Domain\\Validation\\ServicePeriodOverlappingValidatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\CustomPlan\\Domain\\Validation\\ServicePeriodValidatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Datacenter\\Application\\Controller\\StatusControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Datacenter\\Domain\\Command\\CreateLocationHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Datacenter\\Domain\\Command\\UpdateLocationStatusHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Datacenter\\Domain\\Command\\UpdateLocationsTypeHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Datacenter\\Domain\\Enum\\StatusCodeTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Datacenter\\Domain\\Query\\GetDatacentersHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\doctrine-orm-object-manager-bootstrap.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\GraphQL\\Application\\Controller\\SchemaControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\GraphQL\\Application\\Runtime\\DefaultFieldResolverTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\GraphQL\\Application\\Runtime\\SchemaFactoryTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CdnSettings\\EditCdnSettingsTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerCdnSettings\\EditCustomerCdnSettingsTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerMail\\AddCustomerMailTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerMail\\RemoveCustomerMailTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerPaymentSettings\\EditCustomerPaymentSettingsTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\CustomerSettings\\EditCustomerSettingsTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\Note\\AddNoteTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\ScheduleCustomerSuspension\\ScheduleCustomerSuspensionTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\GraphQL\\Domain\\DataResolution\\Type\\Mutation\\SendSignUpInvite\\SendSignUpInviteTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Cdn\\CdnsTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\GraphQL\\Domain\\DataResolution\\Type\\Query\\CdnSettings\\CdnSettingsTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Customer\\CustomersTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\GraphQL\\Domain\\DataResolution\\Type\\Query\\CustomerCdnSettings\\CustomerCdnSettingsTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\GraphQL\\Domain\\DataResolution\\Type\\Query\\DataCenter\\DataCentersTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Kayako\\KayakoTicketsTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\GraphQL\\Domain\\DataResolution\\Type\\Query\\MailLog\\MailLogsTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Notes\\NotesTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Origin\\OriginsTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Payment\\PaymentsTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\GraphQL\\Domain\\DataResolution\\Type\\Query\\Plan\\PlansTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\GraphQL\\Domain\\DataResolution\\Type\\Query\\RealTimeLog\\RealTimeLogQueryFieldFactoryTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\GraphQL\\Domain\\DataResolution\\Type\\Query\\RealTimeLog\\RealTimeLogsQueryFieldFactoryTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\HandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\HttpMocker.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Invoice\\Domain\\Command\\AddInvoiceHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Invoice\\Domain\\Dto\\GbpEquivalentConversionTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Invoice\\Domain\\Dto\\InvoiceDetailsTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Invoice\\Domain\\InvoiceFactoryTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Invoice\\Domain\\Validation\\VatNumberCheckerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Invoice\\Domain\\Value\\InvoiceNumberTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Invoice\\Domain\\Value\\QuantityTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Invoice\\Domain\\Value\\UnitPriceTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Invoice\\Domain\\Value\\VatNumberTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Invoice\\Domain\\Value\\XeroContactNameTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Job\\Application\\Controller\\DetailControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Job\\Application\\Controller\\ListControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Job\\Application\\Controller\\SchedulePrefetchJobControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Job\\Application\\Controller\\SchedulePurgeJobControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Job\\Console\\PruneCommandTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Job\\Domain\\Command\\PurgeAllHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Job\\Domain\\Command\\ScheduleJobHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Job\\Domain\\Limit\\DatabaseJobRateLimiterTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Job\\Domain\\UpstreamHostValidatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Job\\Domain\\Value\\PathsTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Job\\Domain\\Value\\PurgeTagsTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Job\\Infrastructure\\Notify\\DbalJobChannelNotifierTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Monitoring\\Overkill\\Application\\HttpFoundation\\PrometheusResponseFactoryTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\MonthlyTrafficPlan\\Domain\\AvailableTrafficVolumeCalculatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\MonthlyTrafficPlan\\Domain\\Query\\GetMonthlyTrafficPlanListHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Notifications\\Domain\\Command\\NotifyOnFirstCustomerTrafficHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ObjectStorage\\Domain\\BucketsByHostResolverTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ObjectStorage\\Domain\\Command\\CreateAccessKeyHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ObjectStorage\\Domain\\Dto\\BucketsByHostTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ObjectStorage\\Domain\\Query\\FindAccessKeysHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ObjectStorage\\Domain\\Resolver\\RgwPricingResolverTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ObjectStorage\\Domain\\Service\\BillingCalculatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ObjectStorage\\Domain\\StatisticsCollectorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ObjectStorage\\Domain\\Validation\\DatabaseAccessKeyValidatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ObjectStorage\\Domain\\Validation\\IntervalAggregationValidatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ObjectStorage\\Domain\\Value\\AccessTypeTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ObjectStorage\\Domain\\Value\\AggregationTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ObjectStorage\\Domain\\Value\\BucketLifecycle\\BucketLifecycleConfigurationTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ObjectStorage\\Domain\\Value\\PolicyTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Origin\\Application\\Controller\\AddUrlOriginControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Origin\\Application\\Controller\\DetailControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Origin\\Application\\Controller\\EditOriginControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Origin\\Application\\Controller\\ListControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Origin\\Application\\Payload\\ObjectStorage\\EditObjectStorageOriginSchemaTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Origin\\Application\\Payload\\UrlOriginSchemaTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Origin\\Domain\\Command\\ConnectCdnsToOriginHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Origin\\Domain\\Command\\CreateObjectStorageOriginHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Origin\\Domain\\Command\\CreateOriginHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Origin\\Domain\\Command\\DeleteObjectStorageSerDeTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Origin\\Domain\\Command\\EditObjectStorageOriginHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Origin\\Domain\\Command\\LookupSharedOriginSerDeTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Origin\\Domain\\Command\\SendWelcomeToCdn77ObjectStorageSerDeTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Origin\\Domain\\Command\\SetTimeoutToOriginHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Origin\\Domain\\Command\\UpdateCdnsSerDeTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Origin\\Domain\\Validation\\OriginAccessValidatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Origin\\Domain\\Value\\PortTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Origin\\Domain\\Value\\S3CredentialsTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Origin\\Infrastructure\\Quota\\DatabaseCreatedOriginQuotaCheckerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Api\\ApplicationTokenFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Api\\BlockedIpFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Api\\PersonalTokenFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Api\\SessionFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Api\\TeamMemberAccessConfigurationFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Cdn\\CdnFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Cdn\\CdnHttpFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Cdn\\CdnHttpProtectionFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Cdn\\GeoProtectionCountryFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Cdn\\HotlinkProtectionDomainFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Cdn\\IpProtectionAddressFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Cdn\\RealTimeLogFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Cdn\\RealTimeLogFieldCategoryFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Cdn\\RealTimeLogFieldFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Country\\CountryFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Currency\\ExchangeRateFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Customer\\AccountFlagsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Customer\\AccountSettingsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Customer\\AccountSignUpInviteFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Customer\\CustomerFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Customer\\CustomerMailFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Customer\\CustomerPaymentSettingsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Customer\\NoteFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Customer\\ScheduledSuspensionFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\CustomerCdnSettings\\CustomerCdnSettingsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\CustomerMonthlyTrafficPlan\\CustomerMonthlyTrafficPlanFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\DataCenter\\DataCenterFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\DataCenter\\ServerFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\DataCenter\\StatusFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\EntityFixtureFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\EntityFixtureFactoryRegistry.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\MailLog\\MailLogFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\MonthlyTrafficPlan\\GroupFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\MonthlyTrafficPlan\\MonthlyTrafficPlanFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\ObjectStorage\\RgwClusterFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Origin\\CustomerOriginSettingsFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Origin\\OriginFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Payment\\PaymentFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Plan\\PlanFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\RawLog\\RawLogFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\ORMFixtures\\Ticket\\TicketFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Payment\\Domain\\ActivePeriodCalculatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Payment\\Domain\\Command\\RemoveCardInfoHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Payment\\Domain\\Command\\RenewMonthlyPlanHandlerSerDeTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Payment\\Domain\\Command\\RenewMonthlyPlanHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Payment\\Domain\\Command\\RenewMonthlyPlansHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Payment\\Domain\\Dto\\PaymentRequestDataTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Payment\\Domain\\Dto\\WireTransferDTOTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Payment\\Domain\\MonthlyTrafficPlanChargerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Payment\\Domain\\Paygate\\PaygatePaymentProcessorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Payment\\Domain\\PAYGBonusCalculatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Payment\\Domain\\PromoCodeUsageLoggerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Payment\\Domain\\ProportionatePriceCalculatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Payment\\Domain\\Validator\\MinimumAmountValidatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Payment\\Domain\\Value\\MonthlyPlanPaymentDataTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Payment\\Domain\\Value\\PAYGPaymentDataTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Payment\\Domain\\VatRateResolverTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Payment\\Infrastructure\\Finder\\DbalCustomerMonthlyTrafficPlanFinderTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Plan\\Domain\\Dto\\MonthlyPeriodTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Plan\\Domain\\Factory\\MonthlyPlanFactoryTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Plan\\Domain\\MonthlyPlan\\MonthlyPlanConfiguratorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Plan\\Infrastructure\\Repository\\DoctrinePlanRepositoryTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\RawLog\\Domain\\Command\\ActivateRawLogHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\RawLog\\Domain\\Command\\DeactivateRawLogHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\RawLog\\Infrastructure\\FileSystem\\LocalSampleLogFileSystemTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\RawLog\\Infrastructure\\Repository\\DoctrineRawLogRepositoryTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Router\\AvailableEndpointMethodsTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Server\\Domain\\Dto\\PopTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Service\\PushZone\\Api\\ApiClientTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Service\\PushZone\\Api\\Endpoint\\GetNewStatsDeleteEndpointTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Service\\PushZone\\Api\\Endpoint\\GetNewStatsDetailEndpointTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Service\\PushZone\\Api\\Endpoint\\GetNewStatsListEndpointTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Service\\PushZone\\Api\\Endpoint\\Payload\\GetNewStatsDeletePayloadTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Service\\PushZone\\Api\\Endpoint\\Payload\\GetNewStatsDetailRequestPayloadTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Service\\PushZone\\Api\\Endpoint\\Payload\\UpdateZoneListPayloadTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Ssl\\Application\\Controller\\SniAddControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Ssl\\Application\\Controller\\SniDeleteControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Ssl\\Application\\Controller\\SniListControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Ssl\\Application\\Payload\\SslSchemaTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Ssl\\Domain\\Command\\CheckSslHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Ssl\\Domain\\Command\\ComparePrivateKeyHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Ssl\\Domain\\Command\\CreateSslHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Ssl\\Domain\\Command\\DeleteSslHandlerTest.php": {"errors": 2, "warnings": 0, "messages": [{"message": "Type Cdn77\\Api\\Core\\Domain\\Authorization\\CustomerContextStore is not used in this file.", "source": "SlevomatCodingStandard.Namespaces.UnusedUses.UnusedUse", "severity": 5, "fixable": true, "type": "ERROR", "line": 7, "column": 1}, {"message": "Type Cdn77\\Api\\Core\\Domain\\Customer\\CustomerContext is not used in this file.", "source": "SlevomatCodingStandard.Namespaces.UnusedUses.UnusedUse", "severity": 5, "fixable": true, "type": "ERROR", "line": 8, "column": 1}]}, "tests\\Ssl\\Domain\\Command\\EditSslHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Ssl\\Domain\\Parser\\PublicCertificateParserTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Ssl\\Domain\\Validation\\CertificateValidatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Ssl\\Domain\\Value\\SslCnameTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Ssl\\Infrastructure\\Validation\\DatabaseSslAccessValidatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Statistics\\Application\\Controller\\UsageControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Statistics\\Console\\CollectRemoteServerStatisticsCommandTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Statistics\\Console\\CollectRemoteServerStatisticsEndpointTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Statistics\\Domain\\StatisticsCollectorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Application\\Controller\\GetTikTokSpendingStatsControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Application\\Payload\\ContinentSumsSchemaTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Application\\Payload\\SpendingStatsSchemaTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Application\\Payload\\Validation\\AggregationPropertyValidatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Domain\\Aggregation\\AggregationResolverTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Domain\\Aggregation\\FrequencyCalculatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Domain\\Aggregation\\RecordCountResolverTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Domain\\Aggregation\\RounderTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Domain\\BandwidthPercentileCalculatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Domain\\Chart\\ChartDataConverterTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Domain\\Chart\\EmptyTimestampsResolverTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Domain\\DataTable\\TableDataConverterTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Domain\\DataTable\\TimestampTotalsResolverTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Domain\\Dto\\RegionDetailTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Domain\\Dto\\ResourceStatsTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Domain\\Dto\\StatsTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Domain\\Factory\\ContinentStatsFactoryTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Domain\\Factory\\CountrySpendingStatsFactoryTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Domain\\Factory\\CustomerStatsFactoryTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Domain\\Fetcher\\BandwidthPercentileFetcherTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Domain\\IntervalAggregationValidatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Domain\\Query\\GetCdnSumsHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Domain\\Query\\GetContinentSumsHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Domain\\Query\\GetDatacentersBandwidthPercentileHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Domain\\Resolver\\CommonStatsResolverTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Domain\\StatsEnricherTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Domain\\Validation\\CacheStatsAccessValidatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Domain\\Value\\AggregationTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Stats\\Infrastructure\\Finder\\DbalServerIdFinderTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Storage\\Application\\Controller\\AddControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Storage\\Application\\Controller\\DeleteControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Storage\\Application\\Controller\\DetailControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Storage\\Application\\Controller\\ListControllerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Storage\\Console\\UpdateRemoteServerCommandTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Storage\\Console\\UpdateRemoteServerEndpointTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Storage\\Domain\\Command\\CreateStorageHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Storage\\Domain\\Command\\DeleteStorageHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Storage\\Domain\\Factory\\StorageFactoryTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Storage\\Domain\\SecretAllocatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Storage\\Domain\\UserNameAllocatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Storage\\Domain\\Value\\StorageSecretTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Storage\\Domain\\Value\\StorageUriTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Storage\\Domain\\Value\\StorageUserNameTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\StubFactory\\CdnHttpFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\StubFactory\\CountryFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\StubFactory\\DatacenterFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\StubFactory\\OriginFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Tariff\\Domain\\CreditAvailabilityEstimateCalculatorTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Tariff\\Domain\\Factory\\TariffFactoryTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Tariff\\Domain\\Query\\GetAvailableCreditHandlerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\TestCase.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\TestSuiteComplianceTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Ticket\\Infrastructure\\Api\\KayakoResponseDeserializerTest.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Utils\\FakerFactory.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Utils\\FlushAndClear.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Utils\\GraphQLAssertion.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Utils\\NoopPropertyAccessor.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Utils\\SQLAssertion.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Utils\\TokenGenerator.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Utils\\TraitsSetupAndTearDown.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Utils\\WithBrowser.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Utils\\WithFixtures.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Utils\\WithKernel.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Utils\\WithPostgres.php": {"errors": 0, "warnings": 0, "messages": []}, "tests\\Utils\\WithSerializer.php": {"errors": 0, "warnings": 0, "messages": []}, "bootstrap.php": {"errors": 0, "warnings": 0, "messages": []}, "composer-dependency-analyser.php": {"errors": 0, "warnings": 0, "messages": []}, "rector.php": {"errors": 0, "warnings": 0, "messages": []}, "sailor.php": {"errors": 0, "warnings": 0, "messages": []}}}