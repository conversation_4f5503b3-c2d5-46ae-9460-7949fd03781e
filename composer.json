{"name": "cdn77/client-api-v3", "description": "Client API v3", "license": "proprietary", "type": "project", "require": {"php": "^8.3", "ext-dom": "*", "ext-ds": "*", "ext-filter": "*", "ext-intl": "*", "ext-json": "*", "ext-openssl": "*", "ext-pdo": "*", "ext-pdo_pgsql": "*", "ext-sodium": "*", "aws/aws-sdk-php": "3.352.7", "azjezz/psl": "^3.3.0", "bentools/iterable-functions": "^2.3.0", "blackfire/php-sdk": "^2.5.7", "brick/math": "^0.12.3", "brick/money": "^0.10.0", "calcinai/xero-php": "^2.7.0", "cdn77/ara-client": "^1.0", "cdn77/entity-fqn-extractor": "^0.3.0", "cdn77/functions": "^0.2.5", "cdn77/graphql-utils": "^0.2.4", "cdn77/logging-integration-bundle": "^0.5.4", "cdn77/nxg-api-client": "dev-master#30bdc66f", "cdn77/value-objects": "^0.2.1", "cuyz/valinor": "^1.17.0", "datacamp/paygate-sdk": "^0.12.0", "ddeboer/vatin": "^3.1.0", "defuse/php-encryption": "^2.4.0", "devizzent/cebe-php-openapi": "^1.1.4", "doctrine/dbal": "^4.3.2", "doctrine/doctrine-bundle": "^2.15.1", "doctrine/doctrine-migrations-bundle": "^3.4.2", "doctrine/migrations": "^3.9.2", "doctrine/orm": "^3.5.2", "doctrine/persistence": "^4.0.0", "eightpoints/guzzle-bundle": "^8.5.2", "endroid/qr-code": "^6.0.9", "facile-it/php-openid-client": "^0.3.5", "geoip2/geoip2": "^3.2.0", "guzzlehttp/guzzle": "^7.9.3", "guzzlehttp/psr7": "^2.7.1", "jms/serializer": "^3.32.5", "jms/serializer-bundle": "^5.5.1", "lbausch/ceph-radosgw-admin": "dev-pk/headers", "lcobucci/clock": "^3.3.1", "lcobucci/jwt": "^5.5.0", "league/tactician": "^1.1.0", "league/tactician-bundle": "^1.5.2", "mll-lab/graphql-php-scalars": "^6.4.1", "nelmio/cors-bundle": "^2.5.0", "nette/utils": "^4.0.8", "nyholm/psr7": "^1.8.2", "overblog/dataloader-php": "dev-keys-v15 as 0.6.0", "php-http/client-common": "^2.7.2", "php-http/guzzle7-adapter": "^1.1.0", "php-http/httplug": "^2.4.1", "php-http/promise": "^1.3.1", "predis/predis": "^2.4.0", "promphp/prometheus_client_php": "^2.14.1", "psr/clock": "^1.0.0", "psr/http-client": "^1.0.3", "psr/http-factory": "^1.1.0", "psr/http-message": "^2.0", "psr/log": "^3.0.2", "s1lentium/iptools": "^1.2.0", "sentry/sentry": "^4.14.2", "sentry/sentry-symfony": "^5.3.1", "simpod/clickhouse-client": "^0.8.3", "simpod/graphql-utils": "^0.7.4", "snc/redis-bundle": "^4.10.0", "spatie/dns": "^2.7.0", "spawnia/sailor": "^1.1.1", "spomky-labs/aes-key-wrap": "^7.0", "spomky-labs/otphp": "^11.3.0", "symfony/config": "^7.3.2", "symfony/console": "^7.3.2", "symfony/dependency-injection": "^7.3.2", "symfony/doctrine-bridge": "^7.3.2", "symfony/error-handler": "^7.3.2", "symfony/event-dispatcher": "^7.3.0", "symfony/finder": "^7.3.2", "symfony/framework-bundle": "^7.3.2", "symfony/http-client": "^7.3.2", "symfony/http-foundation": "^7.3.2", "symfony/http-kernel": "^7.3.2", "symfony/lock": "7.3.2", "symfony/monolog-bundle": "^3.10", "symfony/rate-limiter": "^7.3.2", "symfony/routing": "^7.3.2", "symfony/security-bundle": "^7.3.2", "symfony/security-core": "^7.3.2", "symfony/security-http": "^7.3.2", "symfony/stopwatch": "^7.3.0", "symfony/string": "^7.3.2", "symfony/twig-bundle": "^7.3.2", "symfony/uid": "^7.3.1", "symfony/yaml": "^7.3.2", "thecodingmachine/safe": "^3.3.0", "vlucas/phpdotenv": "^5.6.2", "webmozart/assert": "^1.11.0", "webonyx/graphql-php": "^15.22.2", "wildbit/postmark-php": "^7.0.0"}, "require-dev": {"ext-pcov": "*", "cdn77/coding-standard": "^7.4.2", "cdn77/phpstan-extension": "^0.1.9", "cdn77/test-utils": "^0.5.3", "dama/doctrine-test-bundle": "^8.3.1", "doctrine/sql-formatter": "^1.5.2", "ergebnis/composer-normalize": "^2.47.0", "ergebnis/phpunit-slow-test-detector": "^2.19.1", "fakerphp/faker": "^1.24.1", "goetas/jms-serializer-phpstan-extension": "^1.3.0", "hautelook/alice-bundle": "^2.15.1", "mockery/mockery": "^1.6.12", "nelmio/alice": "^3.14.2", "php-standard-library/phpstan-extension": "^2.0.0", "phpstan/extension-installer": "^1.4.3", "phpstan/phpstan": "^2.1.22", "phpstan/phpstan-deprecation-rules": "^2.0.3", "phpstan/phpstan-doctrine": "^2.0.4", "phpstan/phpstan-mockery": "^2.0.0", "phpstan/phpstan-phpunit": "^2.0.7", "phpstan/phpstan-strict-rules": "^2.0.6", "phpstan/phpstan-symfony": "^2.0.7", "phpstan/phpstan-webmozart-assert": "^2.0.0", "phpunit/phpunit": "^12.3.4", "qossmic/deptrac": "^2.0.5", "rector/rector": "^2.1.2", "roave/security-advisories": "dev-latest", "shipmonk/composer-dependency-analyser": "^1.8.3", "shipmonk/dead-code-detector": "^0.13.1", "shipmonk/phpstan-rules": "^4.1.5", "symfony/browser-kit": "^7.3.2", "symfony/property-access": "^7.3.2", "thecodingmachine/phpstan-safe-rule": "^1.4.1", "theofidry/alice-data-fixtures": "^1.9.0", "ticketswap/phpstan-error-formatter": "^1.1.5", "tomasvotruba/cognitive-complexity": "^1.0.0"}, "conflict": {"symfony/debug": "<3.3", "symfony/symfony": "*"}, "repositories": [{"type": "composer", "url": "https://composer.cdn77.eu"}, {"type": "vcs", "url": "https://github.com/simPod/dataloader-php"}, {"type": "vcs", "url": "https://github.com/p4veI/php-ceph-radosgw-admin"}, {"type": "composer", "url": "https://repo.packagist.org"}], "autoload": {"psr-4": {"Cdn77\\Api\\": "src/", "Generated\\Sailor\\": "graphql/Generated/Sailor/"}}, "autoload-dev": {"psr-4": {"Cdn77\\Api\\PHPStan\\": "phpstan/", "Cdn77\\Api\\Tests\\": "tests/"}}, "config": {"allow-plugins": {"composer/package-versions-deprecated": true, "dealerdirect/phpcodesniffer-composer-installer": true, "ergebnis/composer-normalize": true, "infection/extension-installer": true, "php-http/discovery": true, "phpstan/extension-installer": true}, "platform": {"php": "8.3"}, "preferred-install": {"*": "dist"}, "sort-packages": true}}