parameters:
	ignoreErrors:
		-
			message: '#^Unused Cdn77\\Api\\Authentication\\Domain\\Value\\AuthorizationHeader\:\:toString$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Authentication/Domain/Value/AuthorizationHeader.php

		-
			message: '#^Unused Cdn77\\Api\\Cdn\\Application\\Payload\\CdnSchema\:\:FieldFollowRedirect$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Cdn/Application/Payload/CdnSchema.php

		-
			message: '#^Unused Cdn77\\Api\\Cdn\\Application\\Payload\\CdnSchemaSummary\:\:FieldLegacyId$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Cdn/Application/Payload/CdnSchemaSummary.php

		-
			message: '#^Unused Cdn77\\Api\\Cdn\\Application\\Payload\\NewCdnSchema\:\:FieldCname$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Cdn/Application/Payload/NewCdnSchema.php

		-
			message: '#^Unused Cdn77\\Api\\Cdn\\Application\\Payload\\Quic\\QuicSchema\:\:FieldEnabled$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Cdn/Application/Payload/Quic/QuicSchema.php

		-
			message: '#^Unused Cdn77\\Api\\Cdn\\Application\\Payload\\Waf\\WafSchema\:\:FieldEnabled$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Cdn/Application/Payload/Waf/WafSchema.php

		-
			message: '#^Unused Cdn77\\Api\\Cdn\\Domain\\Value\\SuspensionReason\:\:Abuse$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Cdn/Domain/Value/SuspensionReason.php

		-
			message: '#^Unused Cdn77\\Api\\Cdn\\Domain\\Value\\SuspensionReason\:\:Malware$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Cdn/Domain/Value/SuspensionReason.php

		-
			message: '#^Unused Cdn77\\Api\\Cdn\\Domain\\Value\\SuspensionReason\:\:NewMedia$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Cdn/Domain/Value/SuspensionReason.php

		-
			message: '#^Unused Cdn77\\Api\\Cdn\\Domain\\Value\\SuspensionReason\:\:OnApp$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Cdn/Domain/Value/SuspensionReason.php

		-
			message: '#^Unused Cdn77\\Api\\Cdn\\Domain\\Value\\SuspensionReason\:\:Scam$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Cdn/Domain/Value/SuspensionReason.php

		-
			message: '#^Unused Cdn77\\Api\\Cdn\\Domain\\Value\\SuspensionReason\:\:isFraud$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Cdn/Domain/Value/SuspensionReason.php

		-
			message: '#^Unused Cdn77\\Api\\Cname\\Application\\Payload\\CnamesSchema\:\:toArray$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Cname/Application/Payload/CnamesSchema.php

		-
			message: '#^Unused Cdn77\\Api\\Cname\\Domain\\Finder\\DnsRecordsFinder\:\:findCnameRecordsForDomain$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Cname/Domain/Finder/DnsRecordsFinder.php

		-
			message: '#^Unused Cdn77\\Api\\Cname\\Infrastructure\\Finder\\SpatieDnsRecordsFinder\:\:findCnameRecordsForDomain$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Cname/Infrastructure/Finder/SpatieDnsRecordsFinder.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Application\\OpenApi\\Model\\Tags\:\:StorageLocation$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Core/Application/OpenApi/Model/Tags.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Application\\Payload\\ErrorsSchema\:\:hasErrors$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Application/Payload/ErrorsSchema.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Application\\Payload\\OAParamSchema\:\:getParametersSpec$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Application/Payload/OAParamSchema.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Currency\\CurrencyConverter\:\:convertAtDateToRational$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Currency/CurrencyConverter.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Alert\\Alert\:\:expiresAt$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Alert/Alert.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Api\\TeamMemberAccessConfiguration\:\:FieldCreatedAt$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Core/Domain/Entity/Api/TeamMemberAccessConfiguration.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Api\\TeamMemberAccessConfiguration\:\:FieldId$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Core/Domain/Entity/Api/TeamMemberAccessConfiguration.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Cdn\\TsunamiGroup\:\:customerId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Cdn/TsunamiGroup.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Cname\\Cname\:\:legacyId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Cname/Cname.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Currency\\ExchangeRate\:\:FieldUsdToBrl$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Core/Domain/Entity/Currency/ExchangeRate.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Currency\\ExchangeRate\:\:FieldUsdToCzk$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Core/Domain/Entity/Currency/ExchangeRate.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Currency\\ExchangeRate\:\:FieldUsdToEur$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Core/Domain/Entity/Currency/ExchangeRate.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Currency\\ExchangeRate\:\:FieldUsdToGbp$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Core/Domain/Entity/Currency/ExchangeRate.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Currency\\ExchangeRate\:\:getId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Currency/ExchangeRate.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Customer\\AccountSettings\:\:getId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Customer/AccountSettings.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Customer\\CustomerCdnSettings\:\:FieldMaxCdns$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Core/Domain/Entity/Customer/CustomerCdnSettings.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Customer\\CustomerCdnSettings\:\:FieldMaxTrialCdns$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Core/Domain/Entity/Customer/CustomerCdnSettings.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Customer\\CustomerId\:\:rik$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Customer/CustomerId.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Customer\\CustomerPaymentSettings\:\:getId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Customer/CustomerPaymentSettings.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Customer\\CustomerTraffic\:\:getId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Customer/CustomerTraffic.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Customer\\CustomerUuid\:\:getBigBang$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Customer/CustomerUuid.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Customer\\CustomerUuid\:\:isCRA$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Customer/CustomerUuid.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Customer\\CustomerUuid\:\:isMojo$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Customer/CustomerUuid.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Customer\\CustomerUuid\:\:rik$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Customer/CustomerUuid.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Customer\\KayakoTicket\:\:FieldCustomerId$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Core/Domain/Entity/Customer/KayakoTicket.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Customer\\KayakoTicket\:\:FieldId$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Core/Domain/Entity/Customer/KayakoTicket.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Customer\\KayakoTicket\:\:FieldLastUpdatedAt$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Core/Domain/Entity/Customer/KayakoTicket.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Customer\\ScheduledSuspension\:\:id$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Customer/ScheduledSuspension.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Datacenter\\DataCenterId\:\:prague$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Datacenter/DataCenterId.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Datacenter\\Region\:\:getCustomerId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Datacenter/Region.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Datacenter\\RegionLocation\:\:getId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Datacenter/RegionLocation.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Datacenter\\RegionLocation\:\:getLocationId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Datacenter/RegionLocation.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Datacenter\\RegionLocation\:\:isHidden$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Datacenter/RegionLocation.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Datacenter\\RegionLocation\:\:regionId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Datacenter/RegionLocation.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Inquiry\\Inquiry\:\:getId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Inquiry/Inquiry.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Invoice\\InvoiceCustomer\:\:getId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Invoice/InvoiceCustomer.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\ObjectStorage\\RgwCluster\:\:customerId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/ObjectStorage/RgwCluster.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\ObjectStorage\\RgwPricing\:\:customerId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/ObjectStorage/RgwPricing.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\ObjectStorage\\RgwPricing\:\:id$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/ObjectStorage/RgwPricing.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Origin\\Origin\:\:credentials$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Origin/Origin.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Origin\\OriginFallback\:\:FieldId$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Core/Domain/Entity/Origin/OriginFallback.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Origin\\OriginFallback\:\:FieldPrimaryOriginId$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Core/Domain/Entity/Origin/OriginFallback.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Payment\\Payment\:\:getSource$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Payment/Payment.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Promo\\PromoCode\:\:getId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Promo/PromoCode.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Promo\\PromoCodeUsage\:\:getId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Promo/PromoCodeUsage.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Rate\\Rate\:\:getCustomerId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Rate/Rate.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Statistics\\ActualStatistics\:\:getId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Statistics/ActualStatistics.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Statistics\\ActualStatistics\:\:getStorageSecret$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Statistics/ActualStatistics.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Statistics\\Statistics\:\:getId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Statistics/Statistics.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Statistics\\Statistics\:\:getStorageSecret$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Statistics/Statistics.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Storage\\StorageAddOn\:\:isCustom$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Storage/StorageAddOn.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Tariff\\Credit\:\:setBonus$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Tariff/Credit.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\Tariff\\Tariff\:\:getId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/Tariff/Tariff.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Entity\\TikTok\\ChangeRequest\:\:customerId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Entity/TikTok/ChangeRequest.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Exception\\ObjectStorage\\ObjectStorageUserNotFound\:\:forCustomerId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Exception/ObjectStorage/ObjectStorageUserNotFound.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Exception\\Origin\\InvalidHostFormat\:\:create$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Exception/Origin/InvalidHostFormat.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Hurricane\\Tsunami\\TsunamiRequestFailed\:\:targetResolutionFailed$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Hurricane/Tsunami/TsunamiRequestFailed.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Origin\\Exception\\OriginNotFound\:\:forCdn$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Origin/Exception/OriginNotFound.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Pipedrive\\Value\\DealCustomFieldId\:\:message$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Pipedrive/Value/DealCustomFieldId.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Pipedrive\\Value\\DealCustomFieldId\:\:monthlyTraffic$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Pipedrive/Value/DealCustomFieldId.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Pipedrive\\Value\\DealCustomFieldId\:\:project$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Pipedrive/Value/DealCustomFieldId.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Postmark\\Value\\PostmarkTemplateId\:\:fromOrNull$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Postmark/Value/PostmarkTemplateId.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Profiling\\PerformanceProfiler\:\:createProbe$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Profiling/PerformanceProfiler.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Profiling\\PerformanceProfiler\:\:endProbe$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Profiling/PerformanceProfiler.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Repository\\Cdn\\CdnRepository\:\:find$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Repository/Cdn/CdnRepository.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Repository\\MonthlyTrafficPlan\\MonthlyTrafficPlanRepository\:\:find$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Repository/MonthlyTrafficPlan/MonthlyTrafficPlanRepository.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Value\\AccessRestriction\\ResourceRestrictionType\:\:getSchemaSpec$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Value/AccessRestriction/ResourceRestrictionType.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Value\\Bytes\:\:zero$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Value/Bytes.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Value\\Customer\\SuspensionReason\:\:clientRequest$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Value/Customer/SuspensionReason.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Value\\Customer\\SuspensionReason\:\:endOfTrial$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Value/Customer/SuspensionReason.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Value\\Enums\\NullableEnum\:\:fromIntOrNull$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Value/Enums/NullableEnum.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Value\\Enums\\NullableIntEnum\:\:fromOrNull$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Value/Enums/NullableIntEnum.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Value\\Enums\\NullableStringEnum\:\:fromOrNull$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Value/Enums/NullableStringEnum.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Value\\Identifier\\StringIdentifier\:\:equals$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Value/Identifier/StringIdentifier.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Value\\ObjectStorage\\ObjectStorageUsage\:\:zero$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Value/ObjectStorage/ObjectStorageUsage.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Value\\Origin\\ObjectStorageType\:\:getReference$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Value/Origin/ObjectStorageType.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Domain\\Value\\Origin\\ObjectStorageType\:\:getSchemaSpec$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Domain/Value/Origin/ObjectStorageType.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Infrastructure\\Billing\\FakeBiller\:\:getType$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Infrastructure/Billing/FakeBiller.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Infrastructure\\Currency\\FakeCurrencyConverter\:\:convertAtDateToRational$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Infrastructure/Currency/FakeCurrencyConverter.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Infrastructure\\Profiling\\BlackfirePerformanceProfiler\:\:createProbe$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Infrastructure/Profiling/BlackfirePerformanceProfiler.php

		-
			message: '#^Unused Cdn77\\Api\\Core\\Infrastructure\\Profiling\\BlackfirePerformanceProfiler\:\:endProbe$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Core/Infrastructure/Profiling/BlackfirePerformanceProfiler.php

		-
			message: '#^Unused Cdn77\\Api\\CoreLibrary\\DateTime\\SafeDateTimeImmutable\:\:fromSaneValue$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/CoreLibrary/DateTime/SafeDateTimeImmutable.php

		-
			message: '#^Unused Cdn77\\Api\\CoreLibrary\\Doctrine\\Dbal\\Types\\ArrayStringType\:\:getName$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/CoreLibrary/Doctrine/Dbal/Types/ArrayStringType.php

		-
			message: '#^Unused Cdn77\\Api\\CoreLibrary\\Doctrine\\Dbal\\Types\\ArrayStringType\:\:requiresSQLCommentHint$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/CoreLibrary/Doctrine/Dbal/Types/ArrayStringType.php

		-
			message: '#^Unused Cdn77\\Api\\CoreLibrary\\Doctrine\\Dbal\\Types\\IntervalType\:\:getName$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/CoreLibrary/Doctrine/Dbal/Types/IntervalType.php

		-
			message: '#^Unused Cdn77\\Api\\CoreLibrary\\Doctrine\\Dbal\\Types\\RawJsonType\:\:getName$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/CoreLibrary/Doctrine/Dbal/Types/RawJsonType.php

		-
			message: '#^Unused Cdn77\\Api\\CoreLibrary\\Doctrine\\Dbal\\Types\\RawJsonType\:\:requiresSQLCommentHint$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/CoreLibrary/Doctrine/Dbal/Types/RawJsonType.php

		-
			message: '#^Unused Cdn77\\Api\\CoreLibrary\\Doctrine\\Dbal\\Types\\Types\:\:Interval$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/CoreLibrary/Doctrine/Dbal/Types/Types.php

		-
			message: '#^Unused Cdn77\\Api\\CustomPlan\\Domain\\Dto\\PaymentInterval\:\:yearly$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/CustomPlan/Domain/Dto/PaymentInterval.php

		-
			message: '#^Unused Cdn77\\Api\\CustomPlan\\Domain\\Exception\\CustomPlanNotFound\:\:failedToGetStats$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/CustomPlan/Domain/Exception/CustomPlanNotFound.php

		-
			message: '#^Unused Cdn77\\Api\\CustomPlan\\Domain\\Repository\\ContractRepository\:\:find$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/CustomPlan/Domain/Repository/ContractRepository.php

		-
			message: '#^Unused Cdn77\\Api\\CustomPlan\\Domain\\Repository\\CustomPlanRepository\:\:find$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/CustomPlan/Domain/Repository/CustomPlanRepository.php

		-
			message: '#^Unused Cdn77\\Api\\Customer\\Domain\\Repository\\CustomerTrafficRepository\:\:findForCustomer$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Customer/Domain/Repository/CustomerTrafficRepository.php

		-
			message: '#^Unused Cdn77\\Api\\Customer\\Domain\\Repository\\EmailAddressConfirmationRepository\:\:find$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Customer/Domain/Repository/EmailAddressConfirmationRepository.php

		-
			message: '#^Unused Cdn77\\Api\\Customer\\Domain\\Repository\\ScheduledSuspensionRepository\:\:get$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Customer/Domain/Repository/ScheduledSuspensionRepository.php

		-
			message: '#^Unused Cdn77\\Api\\Customer\\Domain\\Value\\EstimatedTraffic\:\:toString$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Customer/Domain/Value/EstimatedTraffic.php

		-
			message: '#^Unused Cdn77\\Api\\Customer\\Domain\\Value\\EstimatedTraffic\:\:unknown$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Customer/Domain/Value/EstimatedTraffic.php

		-
			message: '#^Unused Cdn77\\Api\\Customer\\Infrastructure\\Repository\\DoctrineScheduledSuspensionRepository\:\:get$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Customer/Infrastructure/Repository/DoctrineScheduledSuspensionRepository.php

		-
			message: '#^Unused Cdn77\\Api\\Datacenter\\Domain\\Dto\\Location\:\:fromPayload$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Datacenter/Domain/Dto/Location.php

		-
			message: '#^Unused Cdn77\\Api\\Datacenter\\Domain\\Dto\\Server\:\:fromPayload$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Datacenter/Domain/Dto/Server.php

		-
			message: '#^Unused Cdn77\\Api\\Datacenter\\Domain\\Repository\\LocationRepository\:\:find$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Datacenter/Domain/Repository/LocationRepository.php

		-
			message: '#^Unused Cdn77\\Api\\Datacenter\\Domain\\Repository\\LocationRepository\:\:findForId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Datacenter/Domain/Repository/LocationRepository.php

		-
			message: '#^Unused Cdn77\\Api\\GraphQL\\Domain\\Exception\\InvalidType\:\:notNullable$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/GraphQL/Domain/Exception/InvalidType.php

		-
			message: '#^Unused Cdn77\\Api\\GraphQL\\Domain\\Schema\\Type\\CustomerFlags\\CustomerFlags\:\:FieldCustomerId$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/GraphQL/Domain/Schema/Type/CustomerFlags/CustomerFlags.php

		-
			message: '#^Unused Cdn77\\Api\\Inquiry\\Domain\\Mail\\InquiryMail\:\:fromBody$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Inquiry/Domain/Mail/InquiryMail.php

		-
			message: '#^Unused Cdn77\\Api\\Invoice\\Domain\\Repository\\CountryRepository\:\:find$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Invoice/Domain/Repository/CountryRepository.php

		-
			message: '#^Unused Cdn77\\Api\\Invoice\\Domain\\Repository\\InvoiceCustomerRepository\:\:findForCustomer$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Invoice/Domain/Repository/InvoiceCustomerRepository.php

		-
			message: '#^Unused Cdn77\\Api\\Invoice\\Domain\\Value\\BankAccountId\:\:PayPalDeUsd$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Invoice/Domain/Value/BankAccountId.php

		-
			message: '#^Unused Cdn77\\Api\\Invoice\\Domain\\Value\\BankAccountId\:\:PayPalEur$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Invoice/Domain/Value/BankAccountId.php

		-
			message: '#^Unused Cdn77\\Api\\Invoice\\Domain\\Value\\XeroTaxType\:\:fromOrNull$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Invoice/Domain/Value/XeroTaxType.php

		-
			message: '#^Unused Cdn77\\Api\\Job\\Domain\\Exception\\UnableToScheduleJob\:\:temporarilyUnavailable$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Job/Domain/Exception/UnableToScheduleJob.php

		-
			message: '#^Unused Cdn77\\Api\\Job\\Domain\\Repository\\JobRepository\:\:find$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Job/Domain/Repository/JobRepository.php

		-
			message: '#^Unused Cdn77\\Api\\Monitoring\\Overkill\\Domain\\Metric\\Registry\:\:registerCounter$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Monitoring/Overkill/Domain/Metric/Registry.php

		-
			message: '#^Unused Cdn77\\Api\\ObjectStorage\\Application\\Controller\\GetBillingController\:\:RouteSummary$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/ObjectStorage/Application/Controller/GetBillingController.php

		-
			message: '#^Unused Cdn77\\Api\\ObjectStorage\\Application\\Controller\\GetRgwPricingController\:\:RouteSummary$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/ObjectStorage/Application/Controller/GetRgwPricingController.php

		-
			message: '#^Unused Cdn77\\Api\\ObjectStorage\\Application\\Controller\\UserQuotaController\:\:RouteSummary$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/ObjectStorage/Application/Controller/UserQuotaController.php

		-
			message: '#^Unused Cdn77\\Api\\ObjectStorage\\Application\\Payload\\Request\\GetStatsSchema\:\:Name$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/ObjectStorage/Application/Payload/Request/GetStatsSchema.php

		-
			message: '#^Unused Cdn77\\Api\\ObjectStorage\\Domain\\Value\\Policy\:\:equals$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/ObjectStorage/Domain/Value/Policy.php

		-
			message: '#^Unused Cdn77\\Api\\ObjectStorage\\Domain\\Value\\Policy\:\:hash$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/ObjectStorage/Domain/Value/Policy.php

		-
			message: '#^Unused Cdn77\\Api\\ObjectStorage\\Domain\\Value\\RequestCount\:\:createFromInt$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/ObjectStorage/Domain/Value/RequestCount.php

		-
			message: '#^Unused Cdn77\\Api\\Origin\\Application\\Payload\\EditOriginSchema\:\:FieldSsl$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Origin/Application/Payload/EditOriginSchema.php

		-
			message: '#^Unused Cdn77\\Api\\Origin\\Application\\Payload\\ObjectStorage\\EditObjectStorageOriginSchema\:\:FieldFallbackConfiguration$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Origin/Application/Payload/ObjectStorage/EditObjectStorageOriginSchema.php

		-
			message: '#^Unused Cdn77\\Api\\Origin\\Application\\Payload\\ObjectStorageOriginDetailSchema\:\:FieldObjectStorageType$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Origin/Application/Payload/ObjectStorageOriginDetailSchema.php

		-
			message: '#^Unused Cdn77\\Api\\Origin\\Application\\Payload\\ObjectStorageOriginSchema\:\:FieldFallbackConfiguration$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Origin/Application/Payload/ObjectStorageOriginSchema.php

		-
			message: '#^Unused Cdn77\\Api\\Origin\\Domain\\Dto\\Connection\:\:getIdentifier$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Origin/Domain/Dto/Connection.php

		-
			message: '#^Unused Cdn77\\Api\\Origin\\Domain\\Dto\\Connection\:\:getName$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Origin/Domain/Dto/Connection.php

		-
			message: '#^Unused Cdn77\\Api\\Origin\\Domain\\Dto\\Connection\:\:getType$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Origin/Domain/Dto/Connection.php

		-
			message: '#^Unused Cdn77\\Api\\Origin\\Domain\\Dto\\ObjectStorageConnection\:\:getName$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Origin/Domain/Dto/ObjectStorageConnection.php

		-
			message: '#^Unused Cdn77\\Api\\Origin\\Domain\\Dto\\ObjectStorageConnection\:\:getType$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Origin/Domain/Dto/ObjectStorageConnection.php

		-
			message: '#^Unused Cdn77\\Api\\Origin\\Domain\\Value\\ObjectStorageUsage\:\:fromApiResponse$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Origin/Domain/Value/ObjectStorageUsage.php

		-
			message: '#^Unused Cdn77\\Api\\Payment\\Application\\Payload\\PaymentRecipeConfigurationSchema\:\:FieldIsDefault$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Payment/Application/Payload/PaymentRecipeConfigurationSchema.php

		-
			message: '#^Unused Cdn77\\Api\\Payment\\Application\\Payload\\PaymentRecipeConfigurationSchema\:\:FieldLabel$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Payment/Application/Payload/PaymentRecipeConfigurationSchema.php

		-
			message: '#^Unused Cdn77\\Api\\Payment\\Domain\\Exception\\PaymentNotFound\:\:fromCustomerId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Payment/Domain/Exception/PaymentNotFound.php

		-
			message: '#^Unused Cdn77\\Api\\Payment\\Domain\\Repository\\PaymentRecipeRepository\:\:findByRecipeId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Payment/Domain/Repository/PaymentRecipeRepository.php

		-
			message: '#^Unused Cdn77\\Api\\Payment\\Domain\\Value\\PAYGPaymentData\:\:isAutoRecharge$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Payment/Domain/Value/PAYGPaymentData.php

		-
			message: '#^Unused Cdn77\\Api\\Payment\\Domain\\Value\\PaymentData\:\:paypal$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Payment/Domain/Value/PaymentData.php

		-
			message: '#^Unused Cdn77\\Api\\Payment\\Domain\\Value\\PaymentData\:\:stripe$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Payment/Domain/Value/PaymentData.php

		-
			message: '#^Unused Cdn77\\Api\\Plan\\Domain\\Repository\\PlanRepository\:\:find$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Plan/Domain/Repository/PlanRepository.php

		-
			message: '#^Unused Cdn77\\Api\\Plan\\Domain\\Value\\PlanType\:\:getReference$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Plan/Domain/Value/PlanType.php

		-
			message: '#^Unused Cdn77\\Api\\Service\\PushZone\\Api\\Endpoint\\Payload\\GenericPushZoneApiResponsePayload\:\:getError$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Service/PushZone/Api/Endpoint/Payload/GenericPushZoneApiResponsePayload.php

		-
			message: '#^Unused Cdn77\\Api\\Service\\PushZone\\Api\\Endpoint\\Payload\\GenericPushZoneApiResponsePayload\:\:isSuccessful$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Service/PushZone/Api/Endpoint/Payload/GenericPushZoneApiResponsePayload.php

		-
			message: '#^Unused Cdn77\\Api\\Service\\PushZone\\Api\\Endpoint\\Payload\\NewZoneResponsePayload\:\:getError$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Service/PushZone/Api/Endpoint/Payload/NewZoneResponsePayload.php

		-
			message: '#^Unused Cdn77\\Api\\Service\\PushZone\\Api\\Endpoint\\Payload\\NewZoneResponsePayload\:\:isSuccessful$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Service/PushZone/Api/Endpoint/Payload/NewZoneResponsePayload.php

		-
			message: '#^Unused Cdn77\\Api\\Service\\PushZone\\Api\\Endpoint\\Payload\\UpdateZoneListPayload\:\:getZones$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Service/PushZone/Api/Endpoint/Payload/UpdateZoneListPayload.php

		-
			message: '#^Unused Cdn77\\Api\\Service\\PushZone\\Api\\Endpoint\\Payload\\UpdateZoneListPayloadEntry\:\:getId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Service/PushZone/Api/Endpoint/Payload/UpdateZoneListPayloadEntry.php

		-
			message: '#^Unused Cdn77\\Api\\Service\\PushZone\\Api\\Endpoint\\Payload\\UpdateZoneListPayloadEntry\:\:getPassword$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Service/PushZone/Api/Endpoint/Payload/UpdateZoneListPayloadEntry.php

		-
			message: '#^Unused Cdn77\\Api\\Service\\PushZone\\Api\\Endpoint\\Payload\\UpdateZoneListPayloadEntry\:\:getUserId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Service/PushZone/Api/Endpoint/Payload/UpdateZoneListPayloadEntry.php

		-
			message: '#^Unused Cdn77\\Api\\Service\\PushZone\\Api\\Endpoint\\Payload\\UpdateZoneListPayloadEntry\:\:getUserName$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Service/PushZone/Api/Endpoint/Payload/UpdateZoneListPayloadEntry.php

		-
			message: '#^Unused Cdn77\\Api\\Service\\PushZone\\Api\\Endpoint\\Payload\\UpdateZoneListPayloadEntry\:\:getZoneName$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Service/PushZone/Api/Endpoint/Payload/UpdateZoneListPayloadEntry.php

		-
			message: '#^Unused Cdn77\\Api\\Statistics\\Application\\Payload\\UsageDetailSchema\:\:getNodes$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Statistics/Application/Payload/UsageDetailSchema.php

		-
			message: '#^Unused Cdn77\\Api\\Statistics\\Application\\Payload\\UsageDetailSchema\:\:getSpace$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Statistics/Application/Payload/UsageDetailSchema.php

		-
			message: '#^Unused Cdn77\\Api\\Statistics\\Application\\Payload\\UsageDetailSchema\:\:getTime$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Statistics/Application/Payload/UsageDetailSchema.php

		-
			message: '#^Unused Cdn77\\Api\\Statistics\\Application\\Payload\\UsageHistorySchema\:\:getHistoryUsage$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Statistics/Application/Payload/UsageHistorySchema.php

		-
			message: '#^Unused Cdn77\\Api\\Stats\\Application\\Payload\\BaseGetStatsSchema\:\:FieldStreamingFormatTypes$#'
			identifier: shipmonk.deadConstant
			count: 1
			path: src/Stats/Application/Payload/BaseGetStatsSchema.php

		-
			message: '#^Unused Cdn77\\Api\\Stats\\Domain\\DataTable\\TableDataConverter\:\:convertResourceStats$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Stats/Domain/DataTable/TableDataConverter.php

		-
			message: '#^Unused Cdn77\\Api\\Storage\\Application\\Payload\\CredentialsSchema\:\:getPassword$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Storage/Application/Payload/CredentialsSchema.php

		-
			message: '#^Unused Cdn77\\Api\\Storage\\Application\\Payload\\CredentialsSchema\:\:getUsername$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Storage/Application/Payload/CredentialsSchema.php

		-
			message: '#^Unused Cdn77\\Api\\Storage\\Application\\Payload\\DetailSchema\:\:getId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Storage/Application/Payload/DetailSchema.php

		-
			message: '#^Unused Cdn77\\Api\\Storage\\Application\\Payload\\DetailSchema\:\:getLabel$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Storage/Application/Payload/DetailSchema.php

		-
			message: '#^Unused Cdn77\\Api\\Storage\\Application\\Payload\\DetailSchema\:\:getServer$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Storage/Application/Payload/DetailSchema.php

		-
			message: '#^Unused Cdn77\\Api\\Storage\\Application\\Payload\\DetailSchema\:\:getUsername$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Storage/Application/Payload/DetailSchema.php

		-
			message: '#^Unused Cdn77\\Api\\Storage\\Domain\\Repository\\StorageRepository\:\:findForServer$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Storage/Domain/Repository/StorageRepository.php

		-
			message: '#^Unused Cdn77\\Api\\Storage\\Infrastructure\\Repository\\DoctrineStorageRepository\:\:findForServer$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/Storage/Infrastructure/Repository/DoctrineStorageRepository.php

		-
			message: '#^Unused Cdn77\\Api\\StorageLocation\\Application\\Payload\\LocationSchema\:\:getId$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/StorageLocation/Application/Payload/LocationSchema.php

		-
			message: '#^Unused Cdn77\\Api\\StorageLocation\\Application\\Payload\\LocationSchema\:\:getLocation$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/StorageLocation/Application/Payload/LocationSchema.php

		-
			message: '#^Unused Cdn77\\Api\\StorageLocation\\Application\\Payload\\LocationsSchema\:\:getLocations$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/StorageLocation/Application/Payload/LocationsSchema.php

		-
			message: '#^Unused Cdn77\\Api\\StorageLocation\\Domain\\Repository\\StorageServerRepository\:\:find$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/StorageLocation/Domain/Repository/StorageServerRepository.php

		-
			message: '#^Unused Cdn77\\Api\\StorageLocation\\Domain\\Repository\\StorageServerRepository\:\:findPublic$#'
			identifier: shipmonk.deadMethod
			count: 1
			path: src/StorageLocation/Domain/Repository/StorageServerRepository.php
