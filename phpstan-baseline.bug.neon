parameters:
	ignoreErrors:
		-
			message: '#^Method Cdn77\\Api\\Cdn\\Infrastructure\\Finder\\DbalStreamingPlaylistBypassEnabledStatusFinder\:\:findForCdns\(\) should return Ds\\Map\<Cdn77\\Api\\Core\\Domain\\Entity\\Cdn\\CdnId, bool\> but returns Ds\\Map\<Cdn77\\Api\\Core\\Domain\\Entity\\Cdn\\CdnId, mixed\>\.$#'
			identifier: return.type
			count: 1
			path: src/Cdn/Infrastructure/Finder/DbalStreamingPlaylistBypassEnabledStatusFinder.php

		-
			message: '#^Method Cdn77\\Api\\Monitoring\\Overkill\\Infrastructure\\Finder\\ClickhouseCephObjectStorageEgressTrafficLatestTimestampFinder\:\:findTimestamps\(\) should return Ds\\Map\<Cdn77\\Api\\Core\\Domain\\Value\\AggregationUnit, int\> but returns Ds\\Map\<Cdn77\\Api\\Core\\Domain\\Value\\AggregationUnit, mixed\>\.$#'
			identifier: return.type
			count: 1
			path: src/Monitoring/Overkill/Infrastructure/Finder/ClickhouseCephObjectStorageEgressTrafficLatestTimestampFinder.php

		-
			message: '#^Parameter \#1 \$data of class Cdn77\\Api\\Public\\Domain\\Dto\\TlsTestResults constructor expects Ds\\Map\<string, string\>, Ds\\Map\<mixed, mixed\> given\.$#'
			identifier: argument.type
			count: 1
			path: src/Public/Infrastructure/TLS/HttpTlsTester.php
