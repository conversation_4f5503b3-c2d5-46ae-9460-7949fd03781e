<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Operations;

/**
 * @extends \Spawnia\Sailor\Operation<\Generated\Sailor\FlopApi\Operations\AllAutonomousSystems\AllAutonomousSystemsResult>
 */
class AllAutonomousSystems extends \Spawnia\Sailor\Operation
{
    public static function execute(): AllAutonomousSystems\AllAutonomousSystemsResult
    {
        return self::executeOperation(
        );
    }

    protected static function converters(): array
    {
        static $converters;

        return $converters ??= [
        ];
    }

    public static function document(): string
    {
        return /* @lang GraphQL */ 'query AllAutonomousSystems {
          __typename
          autonomousSystems {
            __typename
            edges {
              __typename
              node {
                __typename
                label
                number
              }
            }
          }
        }';
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
