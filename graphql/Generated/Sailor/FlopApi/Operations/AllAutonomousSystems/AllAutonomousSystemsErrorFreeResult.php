<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Operations\AllAutonomousSystems;

class AllAutonomousSystemsErrorFreeResult extends \Spawnia\Sailor\ErrorFreeResult
{
    public AllAutonomousSystems $data;

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../../sailor.php');
    }
}
