<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Operations\AllAutonomousSystems;

class AllAutonomousSystemsResult extends \Spawnia\Sailor\Result
{
    public ?AllAutonomousSystems $data = null;

    protected function setData(\stdClass $data): void
    {
        $this->data = AllAutonomousSystems::fromStdClass($data);
    }

    /**
     * Useful for instantiation of successful mocked results.
     *
     * @return static
     */
    public static function fromData(AllAutonomousSystems $data): self
    {
        $instance = new static;
        $instance->data = $data;

        return $instance;
    }

    public function errorFree(): AllAutonomousSystemsErrorFreeResult
    {
        return AllAutonomousSystemsErrorFreeResult::fromResult($this);
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../../sailor.php');
    }
}
