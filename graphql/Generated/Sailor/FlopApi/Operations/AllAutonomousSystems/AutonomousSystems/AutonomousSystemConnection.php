<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Operations\AllAutonomousSystems\AutonomousSystems;

/**
 * @property array<int, \Generated\Sailor\FlopApi\Operations\AllAutonomousSystems\AutonomousSystems\Edges\AutonomousSystemEdge> $edges
 * @property string $__typename
 */
class AutonomousSystemConnection extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param array<int, \Generated\Sailor\FlopApi\Operations\AllAutonomousSystems\AutonomousSystems\Edges\AutonomousSystemEdge> $edges
     */
    public static function make($edges): self
    {
        $instance = new self;

        if ($edges !== self::UNDEFINED) {
            $instance->edges = $edges;
        }
        $instance->__typename = 'AutonomousSystemConnection';

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'edges' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Generated\Sailor\FlopApi\Operations\AllAutonomousSystems\AutonomousSystems\Edges\AutonomousSystemEdge))),
            '__typename' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../../../sailor.php');
    }
}
