<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Operations\AllAutonomousSystems;

/**
 * @property \Generated\Sailor\FlopApi\Operations\AllAutonomousSystems\AutonomousSystems\AutonomousSystemConnection $autonomousSystems
 * @property string $__typename
 */
class AllAutonomousSystems extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param \Generated\Sailor\FlopApi\Operations\AllAutonomousSystems\AutonomousSystems\AutonomousSystemConnection $autonomousSystems
     */
    public static function make($autonomousSystems): self
    {
        $instance = new self;

        if ($autonomousSystems !== self::UNDEFINED) {
            $instance->autonomousSystems = $autonomousSystems;
        }
        $instance->__typename = 'Query';

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'autonomousSystems' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Generated\Sailor\FlopApi\Operations\AllAutonomousSystems\AutonomousSystems\AutonomousSystemConnection),
            '__typename' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../../sailor.php');
    }
}
