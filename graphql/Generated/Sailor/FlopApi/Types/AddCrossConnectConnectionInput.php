<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property int|string $dataCenterId
 * @property float|int $mrcAmount
 * @property string $mrcCurrency
 * @property string $note
 * @property float|int $nrcAmount
 * @property string $nrcCurrency
 * @property array<int|string> $odfPortIds
 * @property int|string $ownerId
 * @property string $state
 * @property string $type
 * @property int|string $zSideId
 * @property string|null $assetNumber
 * @property int|string|null $dataCenterCircuitId
 * @property int|string|null $dataCenterOrderId
 * @property mixed|null $installedAt
 * @property int|string|null $kayakoTicketId
 * @property string|null $meetMeRoom
 * @property mixed|null $paidUntil
 * @property int|string|null $portId
 * @property int|string|null $zSideCircuitId
 * @property int|string|null $zSideServiceId
 */
class AddCrossConnectConnectionInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param int|string $dataCenterId
     * @param float|int $mrcAmount
     * @param string $mrcCurrency
     * @param string $note
     * @param float|int $nrcAmount
     * @param string $nrcCurrency
     * @param array<int|string> $odfPortIds
     * @param int|string $ownerId
     * @param string $state
     * @param string $type
     * @param int|string $zSideId
     * @param string|null $assetNumber
     * @param int|string|null $dataCenterCircuitId
     * @param int|string|null $dataCenterOrderId
     * @param mixed|null $installedAt
     * @param int|string|null $kayakoTicketId
     * @param string|null $meetMeRoom
     * @param mixed|null $paidUntil
     * @param int|string|null $portId
     * @param int|string|null $zSideCircuitId
     * @param int|string|null $zSideServiceId
     */
    public static function make(
        $dataCenterId,
        $mrcAmount,
        $mrcCurrency,
        $note,
        $nrcAmount,
        $nrcCurrency,
        $odfPortIds,
        $ownerId,
        $state,
        $type,
        $zSideId,
        $assetNumber = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $dataCenterCircuitId = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $dataCenterOrderId = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $installedAt = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $kayakoTicketId = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $meetMeRoom = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $paidUntil = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $portId = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $zSideCircuitId = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $zSideServiceId = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($dataCenterId !== self::UNDEFINED) {
            $instance->dataCenterId = $dataCenterId;
        }
        if ($mrcAmount !== self::UNDEFINED) {
            $instance->mrcAmount = $mrcAmount;
        }
        if ($mrcCurrency !== self::UNDEFINED) {
            $instance->mrcCurrency = $mrcCurrency;
        }
        if ($note !== self::UNDEFINED) {
            $instance->note = $note;
        }
        if ($nrcAmount !== self::UNDEFINED) {
            $instance->nrcAmount = $nrcAmount;
        }
        if ($nrcCurrency !== self::UNDEFINED) {
            $instance->nrcCurrency = $nrcCurrency;
        }
        if ($odfPortIds !== self::UNDEFINED) {
            $instance->odfPortIds = $odfPortIds;
        }
        if ($ownerId !== self::UNDEFINED) {
            $instance->ownerId = $ownerId;
        }
        if ($state !== self::UNDEFINED) {
            $instance->state = $state;
        }
        if ($type !== self::UNDEFINED) {
            $instance->type = $type;
        }
        if ($zSideId !== self::UNDEFINED) {
            $instance->zSideId = $zSideId;
        }
        if ($assetNumber !== self::UNDEFINED) {
            $instance->assetNumber = $assetNumber;
        }
        if ($dataCenterCircuitId !== self::UNDEFINED) {
            $instance->dataCenterCircuitId = $dataCenterCircuitId;
        }
        if ($dataCenterOrderId !== self::UNDEFINED) {
            $instance->dataCenterOrderId = $dataCenterOrderId;
        }
        if ($installedAt !== self::UNDEFINED) {
            $instance->installedAt = $installedAt;
        }
        if ($kayakoTicketId !== self::UNDEFINED) {
            $instance->kayakoTicketId = $kayakoTicketId;
        }
        if ($meetMeRoom !== self::UNDEFINED) {
            $instance->meetMeRoom = $meetMeRoom;
        }
        if ($paidUntil !== self::UNDEFINED) {
            $instance->paidUntil = $paidUntil;
        }
        if ($portId !== self::UNDEFINED) {
            $instance->portId = $portId;
        }
        if ($zSideCircuitId !== self::UNDEFINED) {
            $instance->zSideCircuitId = $zSideCircuitId;
        }
        if ($zSideServiceId !== self::UNDEFINED) {
            $instance->zSideServiceId = $zSideServiceId;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'dataCenterId' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'mrcAmount' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\FloatConverter),
            'mrcCurrency' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'note' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'nrcAmount' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\FloatConverter),
            'nrcCurrency' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'odfPortIds' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter))),
            'ownerId' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'state' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
            'type' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
            'zSideId' => new \Spawnia\Sailor\Convert\NonNullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'assetNumber' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'dataCenterCircuitId' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'dataCenterOrderId' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'installedAt' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ScalarConverter),
            'kayakoTicketId' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'meetMeRoom' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'paidUntil' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ScalarConverter),
            'portId' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'zSideCircuitId' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\IDConverter),
            'zSideServiceId' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\IDConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
