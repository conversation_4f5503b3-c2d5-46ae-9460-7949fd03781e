<?php declare(strict_types=1);

namespace Generated\Sailor\FlopApi\Types;

/**
 * @property array<string|null>|null $circuitIds
 * @property string|null $company
 * @property mixed|null $endAt
 * @property int|null $expectedDowntimeM
 * @property string|null $impact
 * @property string|null $impactType
 * @property array<string|null>|null $locations
 * @property string|null $referenceId
 * @property array<string|null>|null $serviceIds
 * @property string|null $shortSummary
 * @property mixed|null $startAt
 * @property string|null $summary
 */
class EditExternalMaintenanceAnnouncementInput extends \Spawnia\Sailor\ObjectLike
{
    /**
     * @param array<string|null>|null $circuitIds
     * @param string|null $company
     * @param mixed|null $endAt
     * @param int|null $expectedDowntimeM
     * @param string|null $impact
     * @param string|null $impactType
     * @param array<string|null>|null $locations
     * @param string|null $referenceId
     * @param array<string|null>|null $serviceIds
     * @param string|null $shortSummary
     * @param mixed|null $startAt
     * @param string|null $summary
     */
    public static function make(
        $circuitIds = 'Special default value that allows <PERSON> to differentiate between explicitly passing null and not passing a value at all.',
        $company = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $endAt = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $expectedDowntimeM = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $impact = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $impactType = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $locations = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $referenceId = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $serviceIds = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $shortSummary = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $startAt = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
        $summary = 'Special default value that allows Sailor to differentiate between explicitly passing null and not passing a value at all.',
    ): self {
        $instance = new self;

        if ($circuitIds !== self::UNDEFINED) {
            $instance->circuitIds = $circuitIds;
        }
        if ($company !== self::UNDEFINED) {
            $instance->company = $company;
        }
        if ($endAt !== self::UNDEFINED) {
            $instance->endAt = $endAt;
        }
        if ($expectedDowntimeM !== self::UNDEFINED) {
            $instance->expectedDowntimeM = $expectedDowntimeM;
        }
        if ($impact !== self::UNDEFINED) {
            $instance->impact = $impact;
        }
        if ($impactType !== self::UNDEFINED) {
            $instance->impactType = $impactType;
        }
        if ($locations !== self::UNDEFINED) {
            $instance->locations = $locations;
        }
        if ($referenceId !== self::UNDEFINED) {
            $instance->referenceId = $referenceId;
        }
        if ($serviceIds !== self::UNDEFINED) {
            $instance->serviceIds = $serviceIds;
        }
        if ($shortSummary !== self::UNDEFINED) {
            $instance->shortSummary = $shortSummary;
        }
        if ($startAt !== self::UNDEFINED) {
            $instance->startAt = $startAt;
        }
        if ($summary !== self::UNDEFINED) {
            $instance->summary = $summary;
        }

        return $instance;
    }

    protected function converters(): array
    {
        static $converters;

        return $converters ??= [
            'circuitIds' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter))),
            'company' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'endAt' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ScalarConverter),
            'expectedDowntimeM' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\IntConverter),
            'impact' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'impactType' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\EnumConverter),
            'locations' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter))),
            'referenceId' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'serviceIds' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ListConverter(new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter))),
            'shortSummary' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
            'startAt' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\ScalarConverter),
            'summary' => new \Spawnia\Sailor\Convert\NullConverter(new \Spawnia\Sailor\Convert\StringConverter),
        ];
    }

    public static function endpoint(): string
    {
        return 'flop';
    }

    public static function config(): string
    {
        return \Safe\realpath(__DIR__ . '/../../../../../sailor.php');
    }
}
