interface Component {
  device: Device!
  id: ID!
  modelName: String
  serialNumber: String
}

interface Connection {
  edges: [Edge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

interface Device {
  cabinet: Cabinet!
  comment: String
  distributor: String
  id: ID!
  invoiceUrl: String
  linkEdit: String!
  linkOverview: String!
  linkSsh: String!
  lldpRecords: [LldpRecord!]!
  mlag: Mlag
  modelName: String
  name: String!
  operatingSystem: OperatingSystem!
  owner: Customer!
  pollingEnabled: Boolean!
  portIps: [PortIp!]!
  ports: [Port!]!
  primaryIp: IPAddress!
  rackUnitSize: Int
  secondaryIp: IPAddress
  serialNumber: String
  serviceEndDate: Date
  uptime: Float!
}

interface Edge {
  cursor: String!
  node: Node!
}

interface NetworkPath implements Node {
  id: ID!
  name: String!
}

interface Node {
  id: ID!
}

interface Port implements Node {
  administrativeStatus: AdministrativeStatus!
  description: String!
  device: Device!
  id: ID!
  index: Int!
  linkOverview: String!
  name: String!
  operationalStatus: OperationalStatus!
  topologyType: TopologyType!
}

interface Sensor {
  lastValue: DataPoint
}

union MasterComponent = Cpu | FanTraySlot | PsuSlot

union PortsSubscriptionPayload = PortDescriptionChangePayload

union RouterSwitch = Router | Switch

type AddBgpCommunityListPayload {
  bgpCommunityList: BgpCommunityList!
}

type AddBirdCustomerBgpSettingPayload {
  customerBgpSetting: BirdCustomerBgpSetting!
}

type AddBirdHackPrefixListPayload {
  prefixList: BirdHackPrefixList!
}

type AddBirdHackRouterBgpSettingPayload {
  routerBgpSetting: BirdHackRouterBgpSetting!
}

type AddBirdPrefixListPayload {
  prefixList: BirdPrefixList!
}

type AddBirdRouterBgpSettingPayload {
  routerBgpSetting: BirdRouterBgpSetting!
}

type AddCrossConnectConnectionPayload {
  connection: CrossConnectConnection!
}

type AddCrossConnectContactPayload {
  contact: CrossConnectContact!
}

type AddCrossConnectOdfPayload {
  odf: CrossConnectOdf!
}

type AddDataPacketCustomerPayload {
  customer: Customer!
}

type AddDevicePollingSchedulePayload {
  schedule: DevicePollingSchedule!
}

type AddDwdmPayload {
  dwdm: Dwdm!
}

type AddLoadBalancerPayload {
  loadBalancer: LoadBalancer!
}

type AddNetworkLabelPayload {
  networkLabel: NetworkLabel!
}

type AddRouterPayload {
  router: Router!
}

type AddServerPayload {
  server: ServerDevice!
}

type AddSwitchPayload {
  switch: Switch!
}

type AddVirtualPopPayload {
  virtualPop: VirtualPop!
}

type AsPath {
  id: ID!
  name: String!
  regex: String!
}

type AsnGroup {
  asns: [UInt!]!
  id: ID!
  name: String!
}

type AutonomousSystem implements Node {
  cocoPacketIp: IPAddress
  id: ID!
  label: String!
  linkDetail: String!
  name: String
  number: ID!
  organizationName: String
  type: String
}

type AutonomousSystemConnection implements Connection {
  edges: [AutonomousSystemEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type AutonomousSystemEdge implements Edge {
  cursor: String!
  node: AutonomousSystem!
}

type Backbone implements NetworkPath & Node {
  id: ID!
  name: String!
}

type BgpCommunityList {
  asPathPattern: String!
  communities: [String!]!
  id: ID!
  name: String!
}

type BgpRoute {
  id: ID!
  prefix: String!
  nextHop: String!
}

type BgpSession {
  configuredHoldTime: Int!
  description: String
  device: Device!
  establishFailHint: String
  establishedTime: Int
  holdTime: Int!
  id: ID!
  keepaliveInterval: Int!
  localAs: AutonomousSystem
  localIp: IPAddress
  localRouterId: IPAddress!
  localTcpPort: Int
  maxAcceptedPrefixes: SpecialFloat!
  maxPrefixes: SpecialFloat!
  prefixesAccepted: Int!
  prefixesReceived: Int!
  prefixesSent: Int!
  priority: BgpSessionPriority!
  remoteAs: AutonomousSystem
  remoteIp: IPAddress!
  remoteRouterId: IPAddress!
  remoteTcpPort: Int
  routeMapInbound: String
  routeMapOutbound: String
  state: BgpSessionState!
  stateLastChangedAt: DateTimeTz!
  type: String!
}

type BirdCustomerBgpSetting {
  dataCenter: DataCenter!
  id: ID!
  meta: Json!
  prefixList: BirdPrefixList!
  remoteIp: IPAddress!
  server: Server
}

type BirdHackPrefix implements Node {
  additionalBgpCommunities: [String!]!
  approvalStatus: BirdPrefixApprovalStatus!
  id: ID!
  nextHop: IPAddress!
  prefix: Cidr!
  prefixList: BirdHackPrefixList!
}

type BirdHackPrefixConnection implements Connection {
  edges: [BirdHackPrefixEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type BirdHackPrefixEdge implements Edge {
  cursor: String!
  node: BirdHackPrefix!
}

type BirdHackPrefixList implements Node {
  additionalBgpCommunities: [String!]!
  autonomousSystem: AutonomousSystem!
  customer: Customer!
  dataCenters: [DataCenter!]!
  id: ID!
  name: String!
  note: String
  prefixes: [BirdHackPrefix!]!
}

type BirdHackPrefixListConnection implements Connection {
  edges: [BirdHackPrefixListEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type BirdHackPrefixListEdge implements Edge {
  cursor: String!
  node: BirdHackPrefixList!
}

type BirdHackRouterBgpSetting implements Node {
  id: ID!
  ipV4: IPAddress!
  ipV6: IPAddress!
  router: RouterSwitch!
}

type BirdHackRouterBgpSettingConnection implements Connection {
  edges: [BirdHackRouterBgpSettingEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type BirdHackRouterBgpSettingEdge implements Edge {
  cursor: String!
  node: BirdHackRouterBgpSetting!
}

type BirdPrefix {
  approvalStatus: BirdPrefixApprovalStatus!
  kayakoTicketId: ID
  prefixList: BirdPrefixList!
  prefix: String!
  id: ID!
}

type BirdPrefixList {
  asn: ID!
  autonomousSystem: AutonomousSystem!
  customer: Customer!
  id: ID!
  name: String!
  note: String
  prefixes: [BirdPrefix!]!
}

type BirdRouterBgpSetting {
  dataCenter: DataCenter!
  id: ID!
  ipV4: String!
  ipV6: String!
  meta: Json!
  router: RouterSwitch!
}

type Cabinet implements Node {
  dataCenter: DataCenter!
  id: ID!
  gelId: ID!
  name: String!
}

type CommandStatus implements Node {
  id: ID!
  commandName: String!
  createdAt: DateTimeTz!
  exceptionMessage: String
  payload: String!
  state: CommandState!
  updatedAt: DateTimeTz
  userIdentifier: String
}

type CommandStatusConnection implements Connection {
  edges: [CommandStatusEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type CommandStatusEdge implements Edge {
  cursor: String!
  node: CommandStatus!
}

type Commitment {
  cdr: UInt!
  contract: Contract!
  exchangeRate: Float!
  id: ID!
  period: DateTimeTz!
  price: Money!
}

type ConflictingSubnets {
  first: Subnet!
  second: Subnet!
}

type Continent {
  id: ID!
  name: String!
}

type Contract implements NetworkPath & Node {
  bandwidth95PercentileMetric: Bandwidth95PercentileMetric!
  billable: Boolean!
  cdr: UInt!
  commitment(month: DateTimeTz!): Commitment
  customer: Customer!
  expiresAt: Date
  id: ID!
  name: String!
  priceAmount: Float!
  priceCurrency: String!
  seriesColor: String
  type: ContractType!
}

type Country {
  code: String!
  id: ID!
  isoCode: String!
  name: String!
}

type Cpu implements Component {
  cores: [CpuCore!]!
  device: Device!
  id: ID!
  loadSensor: CpuLoadSensor!
  modelName: String
  serialNumber: String
}

type CpuCore implements Component {
  device: Device!
  id: ID!
  loadSensor: CpuCoreLoadSensor!
  modelName: String
  name: String!
  serialNumber: String
}

type CpuCoreLoadSensor implements Component {
  device: Device!
  id: ID!
  modelName: String
  serialNumber: String
}

type CpuLoadSensor implements Component {
  device: Device!
  id: ID!
  modelName: String
  serialNumber: String
}

type CrossConnectConnection {
  assetNumber: String
  dataCenter: DataCenter!
  dataCenterCircuitId: ID
  dataCenterOrderId: ID
  id: ID!
  installedAt: Date
  kayakoTicketId: ID
  meetMeRoom: String
  mrc: Money!
  note: String!
  nrc: Money!
  odfPorts: [CrossConnectOdfPort!]!
  ourCircuitId: ID!
  owner: CrossConnectContact!
  port: Port
  paidUntil: Date
  state: CrossConnectConnectionState!
  type: CrossConnectConnectionType!
  zSide: CrossConnectContact!
  zSideCircuitId: ID
  zSideServiceId: ID
}

type CrossConnectContact {
  id: ID!
  name: String!
  contactEmails: [String!]!
}

type CrossConnectOdf {
  cabinet: Cabinet!
  id: ID!
  name: String!
  portConnector: CrossConnectOdfPortConnector!
  portCount: Int!
  portNamePrefix: String!
  portNumberingStrategy: CrossConnectOdfPortNumberingStrategy!
  portType: CrossConnectOdfPortType!
}

type CrossConnectOdfPort {
  id: ID!
  connection: CrossConnectConnection
  connector: CrossConnectOdfPortConnector!
  name: String!
  odf: CrossConnectOdf!
  type: CrossConnectOdfPortType!
}

type CurrentBps {
  in: DataPoint!
  out: DataPoint!
}

type Customer {
  bgpCommunity: String
  id: ID!
  linkExternal: String
  name: String!
  origin: CustomerOrigin!
  originId: ID!
}

type CustomerConnection {
  edges: [CustomerEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type CustomerContractBandwidth {
  commitment: Commitment!
  contractPctl95Effect: Float!
  customer: Customer!
  id: ID!
  pctl95: Float!
}

type CustomerEdge {
  cursor: String!
  node: Customer!
}

type CustomerTraffic {
  customer: Customer!
  statisticsIn: TrafficStatistics
  statisticsOut: TrafficStatistics
}

type DataCenter implements Node {
  city: ID!
  continent: Continent!
  country: Country!
  gelId: ID!
  id: ID!
  name: String!
  seriesColor: String
  siteId: String!
  timeZone: String!
  virtualPop: VirtualPop!
}

type DataPoint {
  datetime: DateTimeTz!
  value: Float
}

type DevicePollingEntryLogEdge implements Edge {
  cursor: String!
  node: DevicePollingLogEntry!
}

type DevicePollingLogConnection implements Connection {
  edges: [DevicePollingEntryLogEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type DevicePollingLogEntry implements Node {
  id: ID!
  dataType: DevicePollingDataType!
  device: Device!
  loggedAt: DateTimeTz!
  message: String!
  status: DevicePollingResult!
  totalTime: Float!
  transport: DevicePollingTransport!
}

type DevicePollingSchedule {
  id: ID!
  dataType: DevicePollingDataType!
  cronExpression: String!
}

type DeviceVirtualIp {
  id: ID!
  ip: IPAddress!
  port: Port!
}

type DeviceVlan {
  id: ID!
  name: String!
  number: Int!
}

type Dwdm implements Device {
  cabinet: Cabinet!
  comment: String
  distributor: String
  id: ID!
  invoiceUrl: String
  linkEdit: String!
  linkOverview: String!
  linkSsh: String!
  lldpRecords: [LldpRecord!]!
  mlag: Mlag
  modelName: String
  name: String!
  operatingSystem: OperatingSystem!
  owner: Customer!
  pollingEnabled: Boolean!
  portIps: [PortIp!]!
  ports: [Port!]!
  primaryIp: IPAddress!
  rackUnitSize: Int
  secondaryIp: IPAddress
  serialNumber: String
  serviceEndDate: Date
  uptime: Float!
}

type DwdmPort implements Node & Port {
  administrativeStatus: AdministrativeStatus!
  description: String!
  device: Device!
  id: ID!
  index: Int!
  linkOverview: String!
  name: String!
  operationalStatus: OperationalStatus!
  topologyType: TopologyType!
}

type EditBgpCommunityListPayload {
  bgpCommunityList: BgpCommunityList!
}

type EditBirdCustomerBgpSettingPayload {
  customerBgpSetting: BirdCustomerBgpSetting!
}

type EditBirdHackPrefixListPayload {
  prefixList: BirdHackPrefixList!
}

type EditBirdHackPrefixPayload {
  prefix: BirdHackPrefix!
}

type EditBirdHackRouterBgpSettingPayload {
  routerBgpSetting: BirdHackRouterBgpSetting!
}

type EditBirdPrefixListPayload {
  prefixList: BirdPrefixList!
}

type EditBirdPrefixPayload {
  prefix: BirdPrefix!
}

type EditBirdRouterBgpSettingPayload {
  routerBgpSetting: BirdRouterBgpSetting!
}

type EditContractPayload {
  contract: Contract!
}

type EditCrossConnectConnectionPayload {
  connection: CrossConnectConnection!
}

type EditCrossConnectContactPayload {
  contact: CrossConnectContact!
}

type EditCrossConnectOdfPayload {
  odf: CrossConnectOdf!
}

type EditCrossConnectOdfPortPayload {
  odfPort: CrossConnectOdfPort!
}

type EditCustomerPayload {
  customer: Customer!
}

type EditDevicePollingSchedulePayload {
  schedule: DevicePollingSchedule!
}

type EditExternalMaintenanceAnnouncementPayload {
  announcement: ExternalMaintenanceAnnouncement!
}

type EditNetworkLabelPayload {
  networkLabel: NetworkLabel!
}

type EditUserSettingsPayload {
  userProfile: UserProfile!
}

type EditVirtualPopPayload {
  virtualPop: VirtualPop!
}

type Email {
  id: ID!
  from: String!
  subject: String!
  s3Path: String!
  attachments: [String!]!
}

type Event implements Node {
  context: Json!
  id: ID!
  name: String!
  payload: Json!
  recordedAt: DateTimeTz!
  userIdentifier: String
}

type EventConnection {
  edges: [EventEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type EventEdge {
  cursor: String!
  node: Event!
}

type ExecutePortConfigCommandsPayload {
  port: NetworkPort!
}

type ExternalMaintenance implements Node {
  id: ID!
  announcementType: MaintenanceAnnouncementType
  circuitIds: [String!]!
  company: String
  createdAt: DateTimeTz!
  email: Email!
  endAt: DateTimeTz
  announcements: [ExternalMaintenanceAnnouncement!]!
  expectedDowntimeM: Int
  impact: String
  impactType: MaintenanceImpactType
  locations: [String!]!
  referenceId: String
  serviceIds: [String!]!
  shortSummary: String
  startAt: DateTimeTz
  summary: String
}

type ExternalMaintenanceAcknowledgement {
  acknowledgedAt: DateTimeTz!
  acknowledgedBy: String!
}

type ExternalMaintenanceAnnouncement {
  id: ID!
  acknowledgement: ExternalMaintenanceAcknowledgement
  announcementType: MaintenanceAnnouncementType
  circuitIds: [String!]!
  company: String
  createdAt: DateTimeTz!
  email: Email!
  endAt: DateTimeTz
  expectedDowntimeM: Int
  impact: String
  impactType: MaintenanceImpactType
  locations: [String!]!
  serviceIds: [String!]!
  startAt: DateTimeTz
  summary: String
}

type ExternalMaintenanceEdge implements Edge {
  cursor: String!
  node: ExternalMaintenance!
}

type ExternalMaintenancesConnection implements Connection {
  edges: [ExternalMaintenanceEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type FabricModule implements Component {
  device: Device!
  id: ID!
  modelName: String
  serialNumber: String
}

type Fan implements Component {
  administrativeStatusSensor: FanAdministrativeStatusSensor
  device: Device!
  id: ID!
  loadSensor: FanLoadSensor
  modelName: String
  operationalStatusSensor: FanOperationalStatusSensor!
  serialNumber: String
  speedSensor: FanSpeedSensor
}

type FanAdministrativeStatusSensor implements Component {
  device: Device!
  id: ID!
  modelName: String
  serialNumber: String
}

type FanLoadSensor implements Component {
  device: Device!
  id: ID!
  modelName: String
  serialNumber: String
}

type FanOperationalStatusSensor implements Component {
  device: Device!
  id: ID!
  modelName: String
  serialNumber: String
}

type FanSpeedSensor implements Component {
  device: Device!
  id: ID!
  modelName: String
  serialNumber: String
}

type FanTray implements Component {
  device: Device!
  fans: [Fan!]!
  id: ID!
  modelName: String
  serialNumber: String
}

type FanTraySlot implements Component {
  device: Device!
  id: ID!
  modelName: String
  serialNumber: String
  tray: FanTray
}

type GenericComponent implements Component {
  device: Device!
  id: ID!
  modelName: String
  serialNumber: String
}

type Ip implements Node {
  id: ID!
  linkSsh: String!
  value: IPAddress!
}

type IpInSubnet {
  flags: Int!
  ip: String!
}

type Linecard implements Component {
  device: Device!
  id: ID!
  modelName: String
  serialNumber: String
}

type LinkPduToPortPayload {
  networkPort: NetworkPort!
}

type LinkServerToPortPayload {
  port: NetworkPort!
}

type LldpRecord {
  id: ID!
  local: NetworkPort!
  remote: NetworkPort!
}

type LoadBalancer implements Device {
  arpEnabled: Boolean!
  asn: UInt
  bgpEnabled: Boolean!
  cabinet: Cabinet!
  comment: String
  distributor: String
  id: ID!
  invoiceUrl: String
  linkEdit: String!
  linkOverview: String!
  linkSsh: String!
  lldpRecords: [LldpRecord!]!
  matchRules: [LoadBalancerMatchRule!]!
  mlag: Mlag
  modelName: String
  name: String!
  operatingSystem: OperatingSystem!
  owner: Customer!
  pollingEnabled: Boolean!
  portIps: [PortIp!]!
  ports: [Port!]!
  primaryIp: IPAddress!
  rackUnitSize: Int
  secondaryIp: IPAddress
  serialNumber: String
  serviceEndDate: Date

  "The content's schema is subject to change"
  sflowConfigurations: Json

  sflowStatus: SflowStatus
  uptime: Float!
  vlans: [DeviceVlan!]!
}

type LoadBalancerMatchRule implements Node {
  action: String!
  daisyChainPacketsCount: Int64
  fwdPacketsCount: Int64
  id: ID!
  name: String!
  nextHopMembers: [LoadBalancerNextHopGroupMember!]!
  status: LoadBalancerMatchRuleStatus!
  tcpResetPacketsCount: Int64
  virtualIp: IPAddress!
}

type LoadBalancerNextHopGroupMember implements Node {
  adminState: LoadBalancerNextHopGroupMemberAdminState!
  id: ID!
  ip: IPAddress!
  operState: LoadBalancerNextHopGroupMemberOperState!
  server: Server
}

type Mac implements Node {
  id: ID!
  ips: [Ip!]!
  oui: MacOui
  value: MacAddress!
}

type MacAddressTracking {
  from: DateTimeTz!
  mac: String!
  to: DateTimeTz
}

type MacOui {
  organizationName: String!
  oui: MacAddress!
}

type Maintenance {
  bgpSessions: [BgpSession!]!
  devices: [Device!]!
  endAt: DateTimeTz!
  expectedDowntime: Int!
  id: ID!
  impact: String
  impactType: MaintenanceImpactType
  kayakoTicketId: ID
  ports: [Port!]!
  summary: String!
  label: String! @deprecated(reason: "Use `summary` instead")
  referenceId: ID
  startAt: DateTimeTz!
}

type Mlag {
  configSane: String!
  domainId: String
  dualPrimaryDetectionState: String
  fastMacRedirectionEnabled: Boolean
  lastFailoverChangeTime: Float
  lastStateChangeTime: Float
  mlagState: String!
  negotiationStatus: String
  peerConfig: String
  peerLinkStatus: String
  peerMacRoutingSupported: Boolean
  peerState: String
  state: String!
  stateChanges: Int!
}

type Money {
  amount: Float!
  currency: String!
}

type Mutation {
  acknowledgeMaintenanceAnnouncement(id: ID!, input: AcknowledgeMaintenanceAnnouncementInput!): Boolean!
  addAsPath(name: String!, regex: String!): ID!
  addAsnGroup(asns: [UInt!]!, name: String!): ID!
  addBgpCommunityList(input: AddBgpCommunityListInput!): AddBgpCommunityListPayload!
  addBirdCustomerBgpSetting(input: AddBirdCustomerBgpSettingInput!): AddBirdCustomerBgpSettingPayload!
  addBirdHackPrefixList(input: AddBirdHackPrefixListInput!): AddBirdHackPrefixListPayload!
  addBirdHackPrefixes(input: AddBirdHackPrefixesInput!): Boolean!
  addBirdHackRouterBgpSetting(input: AddBirdHackRouterBgpSettingInput!): AddBirdHackRouterBgpSettingPayload!
  addBirdPrefixList(input: AddBirdPrefixListInput!): AddBirdPrefixListPayload!
  addBirdPrefixes(input: AddBirdPrefixInput!): Boolean!
  addBirdRouterBgpSetting(input: AddBirdRouterBgpSettingInput!): AddBirdRouterBgpSettingPayload!
  addCrossConnectConnection(input: AddCrossConnectConnectionInput!): AddCrossConnectConnectionPayload!
  addCrossConnectContact(input: AddCrossConnectContactInput!): AddCrossConnectContactPayload!
  addCrossConnectOdf(input: AddCrossConnectOdfInput!): AddCrossConnectOdfPayload!
  addDataPacketCustomer(input: AddDataPacketCustomerInput!): AddDataPacketCustomerPayload!
  addDevicePollingSchedule(input: AddDevicePollingScheduleInput!): AddDevicePollingSchedulePayload!
  addDwdm(input: AddDwdmInput!): AddDwdmPayload!
  addLoadBalancer(input: AddLoadBalancerInput!): AddLoadBalancerPayload!
  addMaintenance(impact: String, impactType: MaintenanceImpactType, summary: String!, timeRange: DateTimeRange!, expectedDowntime: Int, kayakoTicketId: ID): ID!
  addNetworkLabel(input: AddNetworkLabelInput!): AddNetworkLabelPayload!
  addRouter(input: AddRouterInput!): AddRouterPayload!
  addServer(input: AddServerInput!): AddServerPayload!
  addSwitch(input: AddSwitchInput!): AddSwitchPayload!
  addVirtualPop(input: AddVirtualPopInput!): AddVirtualPopPayload!
  assignDevicesToMaintenance(deviceIds: [ID!]!, maintenanceId: ID!): Boolean!
  assignPortsToMaintenance(portIds: [ID!]!, maintenanceId: ID!): Boolean!

  "Enqueue port description change"
  changePortDescription(id: ID!, newDescription: String!): Boolean!

  changePortsAccessVlan(vlanNumber: Int!, serverGelId: ID!): Boolean!
  createJsonPayload(content: Json!, ttl: Int!): ID!
  deleteAsPath(id: ID!): ID!
  editBgpCommunityList(id: ID!, input: EditBgpCommunityListInput!): EditBgpCommunityListPayload!
  editBirdCustomerBgpSetting(id: ID!, input: EditBirdCustomerBgpSettingInput!): EditBirdCustomerBgpSettingPayload!
  editBirdHackPrefix(id: ID!, input: EditBirdHackPrefixInput!): EditBirdHackPrefixPayload!
  editBirdHackPrefixList(id: ID!, input: EditBirdHackPrefixListInput!): EditBirdHackPrefixListPayload!
  editBirdHackRouterBgpSetting(id: ID!, input: EditBirdHackRouterBgpSettingInput!): EditBirdHackRouterBgpSettingPayload!
  editBirdPrefix(id: ID!, input: EditBirdPrefixInput!): EditBirdPrefixPayload!
  editBirdPrefixList(id: ID!, input: EditBirdPrefixListInput!): EditBirdPrefixListPayload!
  editBirdRouterBgpSetting(id: ID!, input: EditBirdRouterBgpSettingInput!): EditBirdRouterBgpSettingPayload!
  editContract(id: ID!, input: EditContractInput!): EditContractPayload!
  editCrossConnectConnection(id: ID!, input: EditCrossConnectConnectionInput!): EditCrossConnectConnectionPayload!
  editCrossConnectContact(id: ID!, input: EditCrossConnectContactInput!): EditCrossConnectContactPayload!
  editCrossConnectOdf(id: ID!, input: EditCrossConnectOdfInput!): EditCrossConnectOdfPayload!
  editCrossConnectOdfPort(id: ID!, input: EditCrossConnectOdfPortInput!): EditCrossConnectOdfPortPayload!
  editCustomer(id: ID!, input: EditCustomerInput!): EditCustomerPayload!
  editDevicePollingSchedule(id: ID!, input: EditDevicePollingScheduleInput!): EditDevicePollingSchedulePayload!
  editExternalMaintenanceAnnouncement(input: EditExternalMaintenanceAnnouncementInput!, id: ID!): ExternalMaintenanceAnnouncement!
  editNetworkLabel(id: ID!, input: EditNetworkLabelInput!): EditNetworkLabelPayload!
  editUserSettings(input: EditUserSettingsInput!): EditUserSettingsPayload!
  editVirtualPop(id: ID!, input: EditVirtualPopInput!): EditVirtualPopPayload!
  executePortConfigCommands(
    id: ID!
    config: Json!

    "This is used to ensure that the port configuration remains unchanged and that the expected set of commands is generated for the user."
    commands: [String!]!
  ): ExecutePortConfigCommandsPayload!
  finishServerInstallation(id: ID!, targetVlanNumber: Int): ID!

  "Initiates server installation on network and returns Flop Server Installation ID"
  initiateServerInstallation(
    gelId: ID!

    "Either 910 or 911"
    provisioningVlan: Int!
  ): ServerInstallationPayload!

  linkPduToPort(input: LinkPduToPortInput!): LinkPduToPortPayload!
  linkServerToPort(input: LinkServerToPortInput!): LinkServerToPortPayload!
  moveDeviceToJunkyard(id: ID!): ID!

  "Enqueue poll for all device data"
  pollDeviceData(id: ID!): Boolean!

  removeBgpCommunityList(id: ID!): Boolean!
  removeBirdCustomerBgpSetting(id: ID!): Boolean!
  removeBirdHackPrefixList(id: ID!): Boolean!
  removeBirdHackPrefixes(ids: [ID!]!): Boolean!
  removeBirdHackRouterBgpSetting(id: ID!): Boolean!
  removeBirdPrefixList(id: ID!): Boolean!
  removeBirdPrefixes(ids: [ID!]!): Boolean!
  removeBirdRouterBgpSetting(id: ID!): Boolean!
  removeCrossConnectConnection(id: ID!): Boolean!
  removeCrossConnectContact(id: ID!): Boolean!
  removeCrossConnectOdf(id: ID!): Boolean!
  removeNetworkLabel(id: ID!): ID!
  startDDoS(input: StartDdosInput!): Boolean!
  stopDDoS(input: StopDdosInput!): Boolean!
  unacknowledgeMaintenanceAnnouncement(id: ID!): Boolean!
  unlinkPduFromPort(input: UnlinkPduFromPortInput!): UnlinkPduFromPortPayload!
  unlinkServerFromPort(input: UnlinkServerFromPortInput!): UnlinkServerFromPortPayload!
  updateAsPath(name: String!, regex: String!, id: ID!): ID!
  updateAsn(cocoPacketIp: IPAddress, number: ID!, name: String, organizationName: String, type: String): ID!
  updateAsnGroup(asns: [UInt!]!, name: String!, id: ID!): ID!
  updateDwdm(id: ID!, input: UpdateDwdmInput!): UpdateDwdmPayload!
  updateLoadBalancer(id: ID!, input: UpdateLoadBalancerInput!): UpdateLoadBalancerPayload!
  updateMaintenance(impact: String, impactType: MaintenanceImpactType, summary: String!, timeRange: DateTimeRange!, expectedDowntime: Int, kayakoTicketId: ID, id: ID!): ID!
  updateRouter(id: ID!, input: UpdateRouterInput!): UpdateRouterPayload!
  updateServer(id: ID!, input: UpdateServerInput!): UpdateServerPayload!
  updateSwitch(id: ID!, input: UpdateSwitchInput!): UpdateSwitchPayload!
}

type Network {
  autonomousSystem: AutonomousSystem!
  blockCidr: String!
  cidr: String! @deprecated(reason: "Use `prefix`")
  conflictingSubnets: [ConflictingSubnets!]!
  ipAddressesCount: Int!
  ipAddressesFreeCount: Int!
  ipAddressesInArpAndAllocatedCount: Int!
  ipAddressesOnlyAllocatedCount: Int!
  ipAddressesOnlyInArpCount: Int!
  labels: [NetworkLabel!]!
  prefix: String!
  subnets: [Subnet!]!
  topCustomers: [NetworkTopCustomer!]!
  virtualPop: VirtualPop!
}

type NetworkLabel {
  color: String!
  community: String!
  id: ID!
  defaultFilter: NetworkLabelDefaultFilter
  name: String!
}

type NetworkLane implements Component {
  device: Device!
  id: ID!
  modelName: String
  name: String!
  port: NetworkPort
  serialNumber: String
}

type NetworkPort implements Node & Port {
  accessModeVlan: Int
  administrativeMode: AdministrativeMode!
  administrativeStatus: AdministrativeStatus!
  autoRouteListening: Boolean!
  backbone: Backbone
  bundledPorts: [NetworkPort!]!
  connectedIps: [Ip!]!
  connectedMacs: [Mac!]!
  contract: Contract

  "Max 5m ago. If not available, null is returned."
  currentBps: CurrentBps

  description: String!
  device: Device!
  id: ID!
  index: Int!
  isPortChannel: Boolean!
  linkChartTraffic: String!
  linkOverview: String!
  linkRealTime: String!
  lldpNeighbourPort: Port
  lldpStatus: PortLldpStatus
  macTrackings(
    "Max 100"
    first: Int

    orderBy: [MacTrackingsOrdering!]
  ): [MacAddressTracking!]!
  name: String!
  nativeModeVlan: Int
  operationalStatus: OperationalStatus!
  pduLinks: [PduPortLink!]!
  peeringCzCustomer: Customer
  portChannel: NetworkPort
  serverLinks: [ServerPortLink!]!
  speed: Int64!
  staticRoutes: [StaticRoute!]!
  switchportMode: SwitchportMode
  topologyType: TopologyType!
  trunkAllowedVlans: [Int!]
  trunkAllowedVlansString: String
}

type NetworkTopCustomer {
  customer: Customer!
  ipAddressesArpCount: Int!
  ipAddressesCount: Int!
}

type OperatingSystem {
  name: OperatingSystemName!
  nameValue: String!
  version: String
}

type PageInfo {
  endCursor: String!
  hasNextPage: Boolean!
}

type Pdu {
  id: ID!
  gelId: ID!
  name: String!
}

type PduPortLink {
  linkType: PortLinkType!
  pdu: Pdu!
  port: NetworkPort!
}

type PortConnection {
  edges: [PortEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type PortDescriptionChangePayload {
  description: String!
  id: ID!
}

type PortDiscards {
  port: Port!
  statisticsIn: PortDiscardsStatistics
  statisticsOut: PortDiscardsStatistics
}

type PortDiscardsStatistics {
  avg: Float!
  last: DataPoint!
  max: DataPoint!
}

type PortEdge {
  cursor: String!
  node: Port!
}

type PortErrors {
  port: Port!
  statisticsIn: PortErrorsStatistics
  statisticsOut: PortErrorsStatistics
}

type PortErrorsStatistics {
  avg: Float!
  last: DataPoint!
  max: DataPoint!
}

type PortIp {
  customer: Customer
  id: ID!
  ip: String!
  isPrimaryAddress: Boolean!
  port: NetworkPort!
  subnet: String!
}

type PortTraffic {
  port: Port!
  statisticsIn: PortTrafficStatistics
  statisticsOut: PortTrafficStatistics
}

type PortTrafficStatistics {
  avg: Float!
  last: DataPoint!
  max: DataPoint!
  p95: Float!

  "Volume sum in bits. Formula: avg(volume) * count() * interval"
  sum: Float!
}

type PortsDiscards {
  perPort: [PortDiscards!]!
  statisticsIn: PortDiscardsStatistics
  statisticsOut: PortDiscardsStatistics
}

type PortsErrors {
  perPort: [PortErrors!]!
  statisticsIn: PortErrorsStatistics
  statisticsOut: PortErrorsStatistics
}

type PortsTraffic {
  perPort: [PortTraffic!]!
  statisticsIn: PortTrafficStatistics
  statisticsOut: PortTrafficStatistics
}

type Project {
  groups: [ServiceGroup!]!
  id: ID!
  name: String!
  services: [Service!]!
  slug: String!
}

type Psu implements Component {
  administrativeStatusSensor: PsuAdministrativeStatusSensor
  device: Device!
  fan: PsuFan
  id: ID!
  modelName: String
  operationalStatusSensor: PsuOperationalStatusSensor!
  serialNumber: String
}

type PsuAdministrativeStatusSensor implements Component {
  device: Device!
  id: ID!
  modelName: String
  serialNumber: String
}

type PsuFan implements Component {
  device: Device!
  id: ID!
  loadSensor: PsuFanLoadSensor
  modelName: String
  serialNumber: String
  speedSensor: PsuFanSpeedSensor
}

type PsuFanLoadSensor implements Component {
  device: Device!
  id: ID!
  modelName: String
  serialNumber: String
}

type PsuFanSpeedSensor implements Component {
  device: Device!
  id: ID!
  modelName: String
  serialNumber: String
}

type PsuOperationalStatusSensor implements Component & Sensor {
  device: Device!
  id: ID!
  lastValue: DataPoint
  modelName: String
  serialNumber: String
}

type PsuSlot implements Component {
  device: Device!
  id: ID!
  modelName: String
  psu: Psu
  serialNumber: String
}

type Query {
  asPath(id: ID, name: String): AsPath
  asPaths: [AsPath!]!
  asnGroup(name: String): AsnGroup
  asnGroups: [AsnGroup!]!
  autonomousSystem(filter: AutonomousSystemFilterInput!): AutonomousSystem
  autonomousSystems(first: Int, after: String, filter: AutonomousSystemsFilterInput): AutonomousSystemConnection!
  backbonePorts: [NetworkPort!]!
  bgpCommunityList(name: String!): BgpCommunityList
  bgpCommunityLists: [BgpCommunityList!]!
  bgpSession(filter: BgpSessionFilterInput!): BgpSession
  bgpSessions(filter: BgpSessionsFilterInput): [BgpSession!]!
  birdCustomerBgpSetting(id: ID): BirdCustomerBgpSetting
  birdCustomerBgpSettingPeerInfo(id: ID!): String!
  birdCustomerBgpSettings: [BirdCustomerBgpSetting!]!
  birdHackPrefix(id: ID): BirdHackPrefix
  birdHackPrefixList(id: ID, name: String): BirdHackPrefixList
  birdHackPrefixLists: BirdHackPrefixListConnection!
  birdHackPrefixes(filter: BirdHackPrefixesFilterInput): BirdHackPrefixConnection
  birdHackRouterBgpSetting(id: ID): BirdHackRouterBgpSetting
  birdHackRouterBgpSettings: BirdHackRouterBgpSettingConnection!
  birdPrefix(id: ID): BirdPrefix
  birdPrefixList(id: ID, name: String): BirdPrefixList
  birdPrefixLists: [BirdPrefixList!]!
  birdPrefixes(filter: BirdPrefixesFilterInput): [BirdPrefix!]!
  birdRouterBgpSetting(id: ID): BirdRouterBgpSetting
  birdRouterBgpSettings: [BirdRouterBgpSetting!]!
  cabinets: [Cabinet!]!
  commandStatusLog(after: String, filter: CommandStatusLogFilterInput, first: Int): CommandStatusConnection!
  continents(filter: ContinentsFilterInput): [Continent!]!
  contract(filter: ContractFilterInput!): Contract
  contractPorts: [NetworkPort!]!
  contracts: [Contract!]!
  countries: [Country!]!
  country(isoCode: String): Country
  crossConnectConnection(id: ID): CrossConnectConnection
  crossConnectConnections: [CrossConnectConnection!]!
  crossConnectContact(id: ID): CrossConnectContact
  crossConnectContacts: [CrossConnectContact!]!
  crossConnectOdf(id: ID): CrossConnectOdf
  crossConnectOdfPort(id: ID): CrossConnectOdfPort
  crossConnectOdfPorts(filter: CrossConnectOdfPortFilterInput!): [CrossConnectOdfPort!]!
  crossConnectOdfs(filter: CrossConnectOdfsFilterInput): [CrossConnectOdf!]!
  customer(id: CustomerIdFilterInput): Customer
  customerContractBandwidths(filter: CustomerContractBandwidthsFilterInput!): [CustomerContractBandwidth!]!
  customerTraffic(filter: CustomerTrafficFilterInput!): CustomerTraffic
  customers(first: Int, after: String, filter: CustomersFilterInput): CustomerConnection!
  dataCenters(filter: DataCentersFilterInput): [DataCenter!]!
  device(id: ID, name: String): Device
  deviceComponents(filter: DeviceComponentsFilterInput!): [Component!]!
  deviceEnvironment(filter: DeviceEnvironmentFilterInput!): [MasterComponent!]!
  devicePollingLog(first: Int, after: String, last: Int, before: String, filter: DevicePollingLogFilterInput!): DevicePollingLogConnection!
  devicePollingSchedule(id: ID, dataType: DevicePollingDataType): DevicePollingSchedule
  devicePollingSchedules(filter: DevicePollingSchedulesFilterInput): [DevicePollingSchedule!]!
  deviceVirtualIps(filter: DeviceVirtualIpsInputFilter): [DeviceVirtualIp!]!
  devices(filter: DevicesFilterInput): [Device!]!
  edgePorts(filter: EdgePortsFilterInput!): [NetworkPort!]!
  eventLog(filter: EventLogFilterInput!, first: Int, after: String): EventConnection!
  externalMaintenances(first: Int, after: String, last: Int, before: String, filter: ExternalMaintenanceFilterInput): ExternalMaintenancesConnection!
  jsonPayload(id: ID!): Json!
  lldpRecords(filter: LldpRecordsFilterInput): [LldpRecord!]!
  loadBalancer(id: ID, name: String): LoadBalancer
  maintenance(id: ID!): Maintenance
  maintenances(filter: MaintenancesFilterInput): [Maintenance!]!
  me: UserProfile!
  metricLastValue(name: String!, filter: [MetricAttributeFilter!]): String
  metricsLastValue(input: [MetricFilter!]!): [String]!
  mlag(deviceId: ID!): Mlag
  networkLabels: [NetworkLabel!]!
  networkPath(name: String!): NetworkPath
  networkPaths: [NetworkPath!]!
  networkTopCustomers(network: String!): [NetworkTopCustomer!]!
  networks(asns: [UInt!], labels: [ID!], excludedLabels: [ID!], virtualPopIds: [ID!], bgpCommunities: [String!], deviceName: String): [Network!]!
  pdus(filter: PdusFilterInput): [Pdu!]!
  ping: String!
  port(id: ID, deviceName: String, isDeleted: Boolean, index: Int, name: String): Port
  portConfigCommands(id: ID!, config: Json!): [String!]!
  portIps(filter: PortIpsFilterInput!): [PortIp!]!
  ports(first: Int = 200, after: String, filter: PortsFilterInput, orderBy: [PortsOrdering!]): PortConnection!
  portsDiscards(filter: PortsDiscardsFilterInput!): PortsDiscards!
  portsErrors(filter: PortsErrorsFilterInput!): PortsErrors!
  portsTraffic(filter: PortsTrafficFilterInput!): PortsTraffic!
  project(id: ID, slug: String): Project
  projects: [Project!]!
  server(id: ServerIdFilterInput): Server
  serverInstallation(id: ID): ServerInstallation
  servers(first: Int, after: String, filter: ServersFilterInput): ServerConnection!
  serversTraffic(filter: ServersTrafficFilterInput!): ServersTraffic!
  service(id: ID, slugs: ServiceSlugsFilterInput): Service
  serviceGroup(id: ID, slugs: ServiceGroupSlugsFilterInput): ServiceGroup
  sflowAggregations(
    epoch: Epoch!

    "GraphQL does not support union input types yet. The structure is as follows : `type DataFilter = \\{name: string, operator: 'Eq'|'Neq', values: any\\}`; `type Composite = \\{composite: 'AND'|'OR', values: DataFilter\\}`; and finally `type Filter = \\(Composite|DataFilter\\)[]`;"
    filter: [Any!]!

    resultFilters: [SflowAggregationResultFilter!]
    groupBy: [SflowAttribute!]!
    orderBy: [SflowAggregationResultOrdering!]
    withEnrichedResults: Boolean = true
  ): SflowAggregationsResult!
  subnet(filter: SubnetFilterInput!): Subnet
  subnets: [Subnet!]!
  summonLink(spell: String!): String
  trafficStatistics(filter: TrafficStatisticsFilterInput!): TrafficStatistics
  virtualPop(id: ID, name: String): VirtualPop
  virtualPops: [VirtualPop!]!
}

type Router implements Device {
  arpEnabled: Boolean!
  asn: UInt
  bgpEnabled: Boolean!
  vlans: [DeviceVlan!]!
  cabinet: Cabinet!
  comment: String
  distributor: String
  id: ID!
  invoiceUrl: String
  linkEdit: String!
  linkOverview: String!
  linkSsh: String!
  lldpRecords: [LldpRecord!]!
  mlag: Mlag
  modelName: String
  name: String!
  operatingSystem: OperatingSystem!
  owner: Customer!
  pollingEnabled: Boolean!
  portIps: [PortIp!]!
  ports: [Port!]!
  primaryIp: IPAddress!
  rackUnitSize: Int
  secondaryIp: IPAddress
  serialNumber: String
  serviceEndDate: Date
  uptime: Float!
}

type Server {
  bgpRoutes: [BgpRoute!]!
  bgpSessions: [BgpSession!]!
  birdCustomerBgpSetting: BirdCustomerBgpSetting
  customer: Customer
  gelId: ID!
  id: ID!
  name: String!
  serverLinks: [ServerPortLink!]!
  service: Service
}

type ServerConnection {
  edges: [ServerEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type ServerDevice implements Device {
  cabinet: Cabinet!
  comment: String
  distributor: String
  id: ID!
  invoiceUrl: String
  linkEdit: String!
  linkOverview: String!
  linkSsh: String!
  lldpRecords: [LldpRecord!]!
  mlag: Mlag
  modelName: String
  name: String!
  operatingSystem: OperatingSystem!
  owner: Customer!
  pollingEnabled: Boolean!
  portIps: [PortIp!]!
  ports: [Port!]!
  primaryIp: IPAddress!
  rackUnitSize: Int
  secondaryIp: IPAddress
  serialNumber: String
  serviceEndDate: Date
  uptime: Float!
}

type ServerEdge {
  cursor: String!
  node: Server!
}

type ServerInstallation {
  configuredPorts: [NetworkPort!]!
  id: ID!
}

type ServerInstallationPayload {
  serverInstallation: ServerInstallation!
}

type ServerPortLink {
  linkType: PortLinkType
  mappingType: PortMappingType!
  port: NetworkPort!
  server: Server!
}

type ServerTraffic {
  server: Server!
  statisticsIn: TrafficStatistics
  statisticsOut: TrafficStatistics
}

type ServersTraffic {
  perServer: [ServerTraffic!]!
  statisticsIn: TrafficStatistics
  statisticsOut: TrafficStatistics
}

type Service {
  id: ID!
  group: ServiceGroup
  name: String!
  project: Project!
  slug: String!
}

type ServiceGroup {
  id: ID!
  name: String!
  project: Project!
  services: [Service!]!
  slug: String!
}

type SflowAggregation {
  attributes: [String!]!
  avg: Float!
  latencyAvg: Float
  lossAvg: Float
  max: Float!
  min: Float!
  pctl95: Float!
  pctl99: Float!
  sum: Float!
}

type SflowAggregationsResult {
  aggregations: [SflowAggregation!]!
}

type StaticRoute {
  id: ID!
  device: Device!
  subnet: Cidr!
  nextHop: IPAddress
}

type Subnet {
  blockSize: Int64!
  cidr: String!
  ipAddressesCount: Int!
  ipAddressesInArpAllocatedCount: Int!
  ipAddressesOnlyAllocatedCount: Int!
  ipAddressesOnlyInArpCount: Int!
  ips: [IpInSubnet!]!
  occurrences: [SubnetOccurrence!]!
  prefixLength: Int!

  "Bit flag value of SubnetOccurrenceType: 1 → PortIp, 2 → StaticRoute, 4 → Gel"
  type: Int!
}

type SubnetOccurrence {
  device: Device!
  port: NetworkPort

  "1 → PortIp, 2 → StaticRoute, 4 → Gel"
  type: Int!
}

type Subscription {
  portBitsIn(portId: ID!): DataPoint!
  portBitsOut(portId: ID!): DataPoint!
  portBroadcastPacketsIn(portId: ID!): DataPoint!
  portBroadcastPacketsOut(portId: ID!): DataPoint!
  portDiscardsIn(portId: ID!): DataPoint!
  portDiscardsOut(portId: ID!): DataPoint!
  portErrorsIn(portId: ID!): DataPoint!
  portErrorsOut(portId: ID!): DataPoint!
  portMulticastPacketsIn(portId: ID!): DataPoint!
  portMulticastPacketsOut(portId: ID!): DataPoint!
  portUnicastPacketsIn(portId: ID!): DataPoint!
  portUnicastPacketsOut(portId: ID!): DataPoint!
  portsChanged(portIds: [ID!]!): PortsSubscriptionPayload!
}

type SupervisorModule implements Component {
  device: Device!
  id: ID!
  modelName: String
  serialNumber: String
}

type Switch implements Device {
  arpEnabled: Boolean!
  asn: UInt
  bgpEnabled: Boolean!
  cabinet: Cabinet!
  comment: String
  distributor: String
  id: ID!
  invoiceUrl: String
  linkEdit: String!
  linkOverview: String!
  linkSsh: String!
  lldpRecords: [LldpRecord!]!
  mlag: Mlag
  modelName: String
  name: String!
  operatingSystem: OperatingSystem!
  owner: Customer!
  pollingEnabled: Boolean!
  portIps: [PortIp!]!
  ports: [Port!]!
  primaryIp: IPAddress!
  rackUnitSize: Int
  secondaryIp: IPAddress
  serialNumber: String
  serviceEndDate: Date

  "The content's schema is subject to change"
  sflowConfigurations: Json

  sflowStatus: SflowStatus
  uptime: Float!
  vlans: [DeviceVlan!]!
}

type TrafficStatistics {
  avg: Float!
  current: Float! @deprecated(reason: "Use \"last\"")
  last: DataPoint!
  max: DataPoint!
  p95: Float!

  "Volume sum in bits. Formula: avg(volume) * count() * interval"
  sum: Float!
}

type Transceiver implements Component {
  device: Device!
  id: ID!
  modelName: String
  networkLanes: [NetworkLane!]!
  serialNumber: String
}

type UnlinkPduFromPortPayload {
  networkPort: NetworkPort!
}

type UnlinkServerFromPortPayload {
  port: NetworkPort!
}

type UnmanagedDevice implements Device {
  octetsInOid: String!
  octetsOutOid: String!
  snmpCommunity: String!
  cabinet: Cabinet!
  comment: String
  distributor: String
  id: ID!
  invoiceUrl: String
  linkEdit: String!
  linkOverview: String!
  linkSsh: String!
  lldpRecords: [LldpRecord!]!
  mlag: Mlag
  modelName: String
  name: String!
  operatingSystem: OperatingSystem!
  owner: Customer!
  pollingEnabled: Boolean!
  portIps: [PortIp!]!
  ports: [Port!]!
  primaryIp: IPAddress!
  rackUnitSize: Int
  secondaryIp: IPAddress
  serialNumber: String
  serviceEndDate: Date
  uptime: Float!
}

type UpdateDwdmPayload {
  dwdm: Dwdm!
}

type UpdateLoadBalancerPayload {
  loadBalancer: LoadBalancer!
}

type UpdateRouterPayload {
  router: Router!
}

type UpdateServerPayload {
  server: ServerDevice!
}

type UpdateSwitchPayload {
  switch: Switch!
}

type UserProfile {
  id: ID!
  userName: String!
  settings: UserSettings!
}

type UserSettings {
  hideBackboneOutages: Boolean!
  hidePreviousWeek: Boolean!
  macFormatPreference: MacFormatPreference
}

type VirtualPop {
  city: String!
  continent: Continent!
  dataCenters: [DataCenter!]!
  id: ID!
  name: String!
  seriesColor: String
  snmpProxy: SnmpProxy
  sourceBgpCommunitiesExternal: [String!]!
  sourceBgpCommunitiesOriginated: [String!]!
}

type VxLan implements NetworkPath & Node {
  id: ID!
  name: String!
}

enum AdministrativeMode {
  Routed
  Switchport
}

enum AdministrativeStatus {
  Down
  Testing
  Up
}

enum Bandwidth95PercentileMetric {
  Default
  ShiftToLocalTime
  SumOverPorts
}

enum BgpSessionPriority {
  High
  Low
  Mid
  None
}

enum BgpSessionState {
  Active
  AdministrativelyShutDown
  Connect
  Established
  Idle
  OpenConfirm
  OpenSent
}

enum BirdPrefixApprovalStatus {
  ApprovedByNetworkAdmin
  LoaValidated
  NotApproved
  ValidatingLoa
}

enum CommandState {
  Completed
  Failed
  Pending
}

enum ContractType {
  CDN77
  Cache
  IX
  PNI
  Transit
}

enum CrossConnectConnectionState {
  Active
  CablingDone
  DeinstallPending
  Free
  Ordered
  Precabled
}

enum CrossConnectConnectionType {
  Duplex
  Simplex
}

enum CrossConnectOdfPortConnector {
  E2000
  LC
  Other
  RJ45
  SC
}

enum CrossConnectOdfPortNumberingStrategy {
  AB
  Pair
  Standard
}

enum CrossConnectOdfPortType {
  Duplex
  Simplex
}

enum CustomerIdType {
  DATA_PACKET @deprecated(reason: "Use `DataPacket`")
  DataPacket
  FLOP @deprecated(reason: "Use `Flop`")
  Flop
  GEL @deprecated(reason: "Use `Gel`")
  Gel
  PEERING_CZ @deprecated(reason: "Use `PeeringCz`")
  PeeringCz
}

enum CustomerOrigin {
  DataPacket
  Gel
  PeeringCz
}

enum DevicePollingDataType {
  Arp
  BgpSessions
  DeviceVirtualIps
  FdbLldpRecords
  HardwareOnlyLag
  Inventory
  LldpPortStatuses
  LoadBalancerContext
  LoadBalancerCounters
  MacAddressTableAgingTime
  Mlag
  ModelName
  Name
  OperatingSystem
  PortIps
  Ports
  Sensor
  SensorPhysical
  SerialNumber
  StaticRoutes
  SystemCoolingStatus
  SystemDate
  SystemTemperatureStatus
  Uptime
  Version
  Vlans
  VxlanConfigSanity
}

enum DevicePollingResult {
  NetworkError
  Ok
  StrategyError
  Warning
}

enum DevicePollingTransport {
  Eapi
  Snmp
  Ssh
}

enum DeviceType {
  Dwdm
  LoadBalancer
  Router
  Server
  Switch
  Unmanaged
}

enum Direction {
  Asc
  ASC @deprecated(reason: "Use `Asc`")
  AscNullsFirst
  ASC_NULLS_FIRST @deprecated(reason: "Use `AscNullsFirst`")
  AscNullsLast
  ASC_NULLS_LAST @deprecated(reason: "Use `AscNullsLast`")
  Desc
  DESC @deprecated(reason: "Use `Desc`")
  DescNullsFirst
  DESC_NULLS_FIRST @deprecated(reason: "Use `DescNullsFirst`")
  DescNullsLast
  DESC_NULLS_LAST @deprecated(reason: "Use `DescNullsLast`")
}

enum EventOriginatorType {
  System
  User
}

enum HurricaneDdosStatus {
  Error
  NotResolved
  ResolvedAdvanced
  ResolvedManual
  ResolvedSimple
}

enum IpFamily {
  v4
  v6
}

enum LoadBalancerMatchRuleStatus {
  InHardware
  NotHandled
  PartiallyInHardware
}

enum LoadBalancerNextHopGroupMemberAdminState {
  Enabled
  Shutdown
}

enum LoadBalancerNextHopGroupMemberOperState {
  Down
  Up
}

enum MacFormatPreference {
  SixGroups
  ThreeGroups
}

enum MacTrackingsSort {
  FROM @deprecated(reason: "Use `From`")
  From
  TO @deprecated(reason: "Use `To`")
  To
}

enum MaintenanceAnnouncementType {
  Cancelled
  Finished
  Other
  Outage
  Planned
  Update
}

enum MaintenanceImpactType {
  Interruption
  None
  Other
  Risk
}

enum NetworkLabelDefaultFilter {
  Excluded
  Included
}

enum OperatingSystemName {
  Dcp
  Eos
  Ios
  Linux
  Nos
  Unknown
}

enum OperationalStatus {
  Dormant
  Down
  LowerLayerDown
  NotPresent
  Testing
  Unknown
  Up
}

enum PortLinkType {
  Internal
  Ipmi
  Provisioning
  Uplink
}

enum PortLldpStatus {
  Disabled
  Enabled
  RxOnly
  TxOnly
}

enum PortLldpStatusInput {
  Disabled
  DisabledOrPartiallyDisabled
  Enabled
  RxOnly
  TxOnly
  TxOrRx
}

enum PortMappingType {
  Dynamic
  Static
}

enum PortSort {
  AccessModeVlan
  AdministrativeStatus
  Description
  DeviceIp
  DeviceName
  Index
  LldpStatus
  Name
  OperationalStatus
  Speed
}

enum ServerIdType {
  FLOP @deprecated(reason: "Use `Flop`")
  Flop
  GEL @deprecated(reason: "Use `Gel`")
  Gel
}

enum SflowAggregationResultFilterType {
  CountLte
  MaxBpsGte
  MaxBpsLte
  Pctl95BpsGte
  Pctl95BpsLte
}

enum SflowAggregationResultSort {
  Avg
  LatencyAvg
  LossAvg
  Max
  Min
  Pctl95
  Pctl99
  Sum
}

enum SflowAttribute {
  AsPath
  AsPathNextAs
  BgpCommunities
  BgpNextHopIp
  DataCenter
  Device
  DstAs
  DstContinent
  DstCountry
  DstCustomerOverIp
  DstCustomerOverMac
  DstIp
  DstMac
  DstPort
  DstPrefix
  DstPrefixMatchIp
  DstProject
  DstServerOverIp
  DstService
  DstServiceGroup
  DstSmallestPrefix
  DstVirtualPop
  DstVlan
  InContract
  InIf
  InIfId
  InNetworkPath
  InNetworkPathPricingModel
  InNetworkPathType
  OutContract
  OutIf
  OutIfId
  OutNetworkPath
  OutNetworkPathPricingModel
  OutNetworkPathType
  Protocol
  ProvinceCode
  SrcAs
  SrcContinent
  SrcCountry
  SrcCustomerOverIp
  SrcCustomerOverMac
  SrcIp
  SrcMac
  SrcPort
  SrcPrefix
  SrcPrefixMatchIp
  SrcProject
  SrcServerOverIp
  SrcService
  SrcServiceGroup
  SrcSmallestPrefix
  SrcVirtualPop
  SrcVlan
  Tags
  VirtualPop
}

enum SflowStatus {
  Error
  Ok
  Warning
}

enum SnmpProxy {
  Chi
  Prg
  Sao
  Sgp
}

enum SwitchportMode {
  Access
  Dot1QTunnel
  Down
  DynamicAuto
  Trunk
  Tunnel
}

enum TopologyType {
  Edge
  Infrastructure
  Unknown
}

scalar Any

scalar BooleanOrString

scalar Cidr

"A date string with format `Y-m-d`, e.g. `2011-05-23`."
scalar Date

"A datetime string with format `Y-m-d\\TH:i:s.uP`, e.g. `2020-04-20T16:20:04.000000+04:00`."
scalar DateTimeTz

scalar IPAddress

"Signed 64-bit integer (range < -9223372036854775808, 9223372036854775807 > )"
scalar Int64

"Arbitrary data encoded in JavaScript Object Notation. See https:\/\/www.json.org\/."
scalar Json

scalar MacAddress

"Float or string-represented float. Possible string values: `Infinity`, `-Infinity`"
scalar SpecialFloat

"Unsigned 32-bit integer"
scalar UInt

input AcknowledgeMaintenanceAnnouncementInput {
  comment: String
}

input AddBgpCommunityListInput {
  asPathPattern: String
  name: String!
  communities: [String!]!
}

input AddBirdCustomerBgpSettingInput {
  dataCenterId: ID!
  prefixListId: ID!
  remoteIp: IPAddress!
  serverId: ID!
}

input AddBirdHackPrefixListInput {
  additionalBgpCommunities: [String!]!
  asn: ID!
  customerId: ID!
  dataCenterIds: [ID!]!
  name: String!
  note: String
}

input AddBirdHackPrefixesInput {
  additionalBgpCommunities: [String!]!
  approvalStatus: BirdPrefixApprovalStatus!
  nextHop: IPAddress!
  prefixListId: ID!
  prefixes: [Cidr!]!
}

input AddBirdHackRouterBgpSettingInput {
  routerId: ID!
  ipV4: IPAddress!
  ipV6: IPAddress!
}

input AddBirdPrefixInput {
  approvalStatus: BirdPrefixApprovalStatus!
  kayakoTicketId: String
  prefixListId: ID!
  prefixes: [String!]!
}

input AddBirdPrefixListInput {
  asn: ID!
  customerId: ID!
  name: String!
  note: String
}

input AddBirdRouterBgpSettingInput {
  routerId: ID!
  ipV4: IPAddress!
  ipV6: IPAddress!
}

input AddCrossConnectConnectionInput {
  assetNumber: String
  dataCenterId: ID!
  dataCenterCircuitId: ID
  dataCenterOrderId: ID
  installedAt: Date
  kayakoTicketId: ID
  meetMeRoom: String
  mrcAmount: Float!
  mrcCurrency: String!
  note: String!
  nrcAmount: Float!
  nrcCurrency: String!
  odfPortIds: [ID!]!
  ownerId: ID!
  paidUntil: Date
  portId: ID
  state: CrossConnectConnectionState!
  type: CrossConnectConnectionType!
  zSideId: ID!
  zSideCircuitId: ID
  zSideServiceId: ID
}

input AddCrossConnectContactInput {
  contactEmails: [String!]!
  name: String!
}

input AddCrossConnectOdfInput {
  cabinetId: ID!
  name: String!
  portConnector: CrossConnectOdfPortConnector!
  portCount: Int!
  portNamePrefix: String!
  portNumberingStrategy: CrossConnectOdfPortNumberingStrategy!
  portType: CrossConnectOdfPortType!
}

input AddDataPacketCustomerInput {
  dataPacketId: ID!
  name: String!
}

input AddDevicePollingScheduleInput {
  cronExpression: String!
  dataType: String!
}

input AddDwdmInput {
  cabinet: ID!
  comment: String
  distributor: String
  invoiceUrl: String
  pollingEnabled: Boolean!
  owner: ID!
  primaryIp: String!
  secondaryIp: String
  serviceEndDate: Date
}

input AddLoadBalancerInput {
  arpEnabled: Boolean!
  bgpEnabled: Boolean!
  cabinet: ID!
  comment: String
  distributor: String
  invoiceUrl: String
  pollingEnabled: Boolean!
  owner: ID!
  primaryIp: String!
  secondaryIp: String
  serviceEndDate: Date
  sflowToFlop: Boolean!
}

input AddNetworkLabelInput {
  color: String!
  community: String!
  defaultFilter: NetworkLabelDefaultFilter
  name: String!
}

input AddRouterInput {
  arpEnabled: Boolean!
  bgpEnabled: Boolean!
  cabinet: ID!
  comment: String
  distributor: String
  invoiceUrl: String
  pollingEnabled: Boolean!
  owner: ID!
  primaryIp: String!
  secondaryIp: String
  serviceEndDate: Date
}

input AddServerInput {
  cabinet: ID!
  comment: String
  distributor: String
  invoiceUrl: String
  pollingEnabled: Boolean!
  owner: ID!
  primaryIp: String!
  secondaryIp: String
  serviceEndDate: Date
}

input AddSwitchInput {
  arpEnabled: Boolean!
  bgpEnabled: Boolean!
  cabinet: ID!
  comment: String
  distributor: String
  invoiceUrl: String
  pollingEnabled: Boolean!
  owner: ID!
  primaryIp: String!
  secondaryIp: String
  serviceEndDate: Date
  sflowToFlop: Boolean!
}

input AddVirtualPopInput {
  color: String
  dataCenters: [ID]!
  name: String!
  preferredSnmpProxy: SnmpProxy
  sourceBgpCommunitiesOriginated: [String]!
  sourceBgpCommunitiesExternal: [String]!
}

input AutonomousSystemFilterInput {
  number: ID
}

input AutonomousSystemsFilterInput {
  label: String
  name: String
  numbers: [UInt!]
}

input BgpSessionFilterInput {
  id: ID
}

input BgpSessionsFilterInput {
  deviceName: String
  description: String
  localAs: ID
  localIp: String
  prefixesAccepted: Int
  prefixesReceived: Int
  prefixesSent: Int
  remoteAs: ID
  remoteIp: String
  remoteIps: [IPAddress!]
  state: BgpSessionState
  states: [BgpSessionState!]
}

input BirdHackPrefixesFilterInput {
  prefixes: [Cidr!]
}

input BirdPrefixesFilterInput {
  prefixes: [String!]
}

input CommandStatusLogFilterInput {
  userIdentifier: String
}

input ContinentsFilterInput {
  name: String
  presentCustomers: [ID!]
}

input ContractFilterInput {
  id: ID
  nameAndType: ContractFilterTypeNameInput
}

input ContractFilterTypeNameInput {
  name: String!
  type: ContractType!
}

input CrossConnectOdfPortFilterInput {
  odfId: ID!
}

input CrossConnectOdfsFilterInput {
  dataCenter: ID!
}

input CustomerContractBandwidthsFilterInput {
  customer: ID
  customers: [CustomerIdFilterInput!]

  "Period range filter. Period always starts the first second in a month"
  range: DateTimeRange
}

input CustomerIdFilterInput {
  id: ID!
  type: CustomerIdType!
}

input CustomerTrafficFilterInput {
  customerId: CustomerIdFilterInput!
  from: DateTimeTz!

  "Data aggregation period in seconds"
  interval: Int!
  to: DateTimeTz!
}

input CustomersFilterInput {
  name: String
  ids: [ID!]
  isOwner: Boolean
  origin: CustomerOrigin
}

input DataCentersFilterInput {
  continents: [ID!]
  hasBillableBandwidth: Boolean
  containsOdf: Boolean
  presentCustomers: [ID!]
  siteId: String
}

input DateTimeRange {
  from: DateTimeTz!
  to: DateTimeTz!
}

input DeviceComponentsFilterInput {
  deviceName: String
  modelName: String
  serialNumber: String
  type: String
}

input DeviceEnvironmentFilterInput {
  deviceId: ID!
}

input DevicePollingLogFilterInput {
  deviceId: ID
}

input DevicePollingSchedulesFilterInput {
  deviceId: ID
}

input DeviceVirtualIpsInputFilter {
  deviceId: ID!
}

input DevicesFilterInput {
  dataCenter: ID
  hasArp: Boolean
  hasBgp: Boolean
  hasSflow: Boolean
  ip: String
  isEnabled: Boolean
  modelName: String
  name: String
  operatingSystem: OperatingSystemFilterInput
  owner: ID
  serialNumber: String
  type: DeviceType
}

input EdgePortsFilterInput {
  ip: IPAddress
}

input EditBgpCommunityListInput {
  asPathPattern: String
  name: String!
  communities: [String!]!
}

input EditBirdCustomerBgpSettingInput {
  dataCenterId: ID!
  prefixListId: ID!
  remoteIp: IPAddress!
  serverId: ID!
}

input EditBirdHackPrefixInput {
  additionalBgpCommunities: [String!]!
  approvalStatus: BirdPrefixApprovalStatus!
  nextHop: IPAddress!
  prefixListId: ID!
  prefix: Cidr!
}

input EditBirdHackPrefixListInput {
  additionalBgpCommunities: [String!]!
  asn: ID!
  customerId: ID!
  dataCenterIds: [ID!]!
  name: String!
  note: String
}

input EditBirdHackRouterBgpSettingInput {
  routerId: ID!
  ipV4: IPAddress!
  ipV6: IPAddress!
}

input EditBirdPrefixInput {
  approvalStatus: BirdPrefixApprovalStatus!
  kayakoTicketId: String
  prefixListId: ID!
  prefix: String!
}

input EditBirdPrefixListInput {
  asn: ID!
  customerId: ID!
  name: String!
  note: String
}

input EditBirdRouterBgpSettingInput {
  routerId: ID!
  ipV4: IPAddress!
  ipV6: IPAddress!
}

input EditContractInput {
  applyToLastCommitment: Boolean!
  bandwidth95PercentileMetric: Bandwidth95PercentileMetric!
  billable: Boolean!
  cdr: UInt!
  customer: ID!
  expiresAt: Date
  name: String!
  priceAmount: Float!
  priceCurrency: String!
  seriesColor: String!
}

input EditCrossConnectConnectionInput {
  assetNumber: String
  dataCenterId: ID!
  dataCenterCircuitId: ID
  dataCenterOrderId: ID
  installedAt: Date
  kayakoTicketId: ID
  meetMeRoom: String
  mrcAmount: Float!
  mrcCurrency: String!
  note: String!
  nrcAmount: Float!
  nrcCurrency: String!
  odfPortIds: [ID!]!
  ownerId: ID!
  paidUntil: Date
  portId: ID
  state: CrossConnectConnectionState!
  type: CrossConnectConnectionType!
  zSideId: ID!
  zSideCircuitId: ID
  zSideServiceId: ID
}

input EditCrossConnectContactInput {
  contactEmails: [String!]!
  name: String!
}

input EditCrossConnectOdfInput {
  cabinetId: ID!
  name: String!
  portConnector: CrossConnectOdfPortConnector!
  portCount: Int!
  portNamePrefix: String!
  portNumberingStrategy: CrossConnectOdfPortNumberingStrategy!
  portType: CrossConnectOdfPortType!
}

input EditCrossConnectOdfPortInput {
  connector: CrossConnectOdfPortConnector!
  type: CrossConnectOdfPortType!
}

input EditCustomerInput {
  bgpCommunity: String
}

input EditDevicePollingScheduleInput {
  cronExpression: String!
}

input EditExternalMaintenanceAnnouncementInput {
  circuitIds: [String]
  company: String
  endAt: DateTimeTz
  expectedDowntimeM: Int
  impact: String
  impactType: MaintenanceImpactType
  locations: [String]
  referenceId: String
  serviceIds: [String]
  shortSummary: String
  startAt: DateTimeTz
  summary: String
}

input EditNetworkLabelInput {
  color: String!
  community: String!
  defaultFilter: NetworkLabelDefaultFilter
  name: String!
}

input EditUserSettingsInput {
  hideBackboneOutages: Boolean!
  hidePreviousWeek: Boolean!
  macFormatPreference: MacFormatPreference
}

input EditVirtualPopInput {
  color: String
  dataCenters: [ID]!
  name: String!
  preferredSnmpProxy: SnmpProxy
  sourceBgpCommunitiesOriginated: [String]!
  sourceBgpCommunitiesExternal: [String]!
}

input Epoch {
  "Seconds as unit"
  bucketSize: Int!
  from: DateTimeTz!
  to: DateTimeTz!
}

input EventLogFilterInput {
  aggregateId: ID
  context: String
  entityId: ID
  eventName: String
  originator: String
  originatorType: EventOriginatorType
  payload: String
  recordedAfter: DateTimeTz
}

input ExternalMaintenanceFilterInput {
  active: Boolean
}

input IpsFilterInput {
  ips: [String]
  portIds: [String]
}

input LinkPduToPortInput {
  linkType: PortLinkType!
  pduId: ID!
  portId: ID!
}

input LinkServerToPortInput {
  linkType: PortLinkType!
  portId: ID!
  serverId: ID!
}

input LldpRecordsFilterInput {
  deviceName: String!
}

input MacTrackingsOrdering {
  direction: Direction!
  sort: MacTrackingsSort!
}

input MaintenancesFilterInput {
  endAfter: DateTimeTz
  endBefore: DateTimeTz
}

input MetricAttributeFilter {
  name: String!
  value: BooleanOrString!
}

input MetricFilter {
  filter: [MetricAttributeFilter!]
  name: String!
}

input OperatingSystemFilterInput {
  name: OperatingSystemName!
  version: String
}

input PdusFilterInput {
  name: String
}

input PortIpsFilterInput {
  customer: CustomerIdFilterInput
  device: ID
  ips: [String!]
  ipFamily: IpFamily
}

input PortsDiscardsFilterInput {
  from: DateTimeTz!

  "Data aggregation period in seconds"
  period: Int!
  portIds: [ID!]!
  to: DateTimeTz!
}

input PortsErrorsFilterInput {
  from: DateTimeTz!

  "Data aggregation period in seconds"
  period: Int!
  portIds: [ID!]!
  to: DateTimeTz!
}

input PortsFilterInput {
  accessModeVlan: Int
  administrativeStatus: AdministrativeStatus
  connectedIps: [String!]
  customerName: String
  description: String
  deviceId: ID
  deviceIp: String
  deviceName: String
  ids: [ID!]
  index: Int
  indices: [Int!]
  lldpStatus: PortLldpStatusInput
  name: String
  operationalStatus: OperationalStatus
  serviceName: String
  speed: String
  topologyType: TopologyType
}

input PortsOrdering {
  direction: Direction!
  sort: PortSort!
}

input PortsTrafficFilterInput {
  from: DateTimeTz!

  "Data aggregation period in seconds"
  period: Int!
  portIds: [ID!]!
  to: DateTimeTz!
}

input ServerIdFilterInput {
  id: ID!
  type: ServerIdType!
}

input ServersFilterInput {
  continents: [ID!]
  customers: [ID!]
  dataCenters: [ID!]
  gelIds: [ID!]
  ids: [ID!]
  name: String
  macAddresses: [MacAddress!]
}

input ServersTrafficFilterInput {
  customerId: CustomerIdFilterInput
  from: DateTimeTz!

  "Data aggregation period in seconds"
  period: Int!

  "Provide either FLOP IDs or GEL IDs, do not combine ID types"
  serverIds: [ServerIdFilterInput!]!
  to: DateTimeTz!
}

input ServiceGroupSlugsFilterInput {
  projectSlug: String!
  slug: String!
}

input ServiceSlugsFilterInput {
  projectSlug: String!
  slug: String!
}

input SflowAggregationResultFilter {
  type: SflowAggregationResultFilterType!
  value: Float!
}

input SflowAggregationResultOrdering {
  direction: Direction!
  sort: SflowAggregationResultSort!
}

input StartDdosInput {
  bps: Float!
  hurricaneId: ID!
  hurricaneStatus: HurricaneDdosStatus!
  pps: Float!
  source: String!
  startAt: DateTimeTz!
  target: String!
  type: String!
}

input StopDdosInput {
  bps: Float!
  endAt: DateTimeTz!
  hurricaneId: ID!
  hurricaneStatus: HurricaneDdosStatus!
  pps: Float!
}

input SubnetFilterInput {
  cidr: String
}

input TrafficStatisticsFilterInput {
  customer: ID!
  month: DateTimeTz!
}

input UnlinkPduFromPortInput {
  pduId: ID!
  portId: ID!
}

input UnlinkServerFromPortInput {
  portId: ID!
  serverId: ID!
}

input UpdateDwdmInput {
  cabinet: ID!
  comment: String
  distributor: String
  invoiceUrl: String
  pollingEnabled: Boolean!
  owner: ID!
  primaryIp: String!
  secondaryIp: String
  serviceEndDate: Date
}

input UpdateLoadBalancerInput {
  arpEnabled: Boolean!
  bgpEnabled: Boolean!
  cabinet: ID!
  comment: String
  distributor: String
  invoiceUrl: String
  pollingEnabled: Boolean!
  owner: ID!
  primaryIp: String!
  secondaryIp: String
  serviceEndDate: Date
  sflowToFlop: Boolean!
}

input UpdateRouterInput {
  arpEnabled: Boolean!
  bgpEnabled: Boolean!
  cabinet: ID!
  comment: String
  distributor: String
  invoiceUrl: String
  pollingEnabled: Boolean!
  owner: ID!
  primaryIp: String!
  secondaryIp: String
  serviceEndDate: Date
}

input UpdateServerInput {
  cabinet: ID!
  comment: String
  distributor: String
  invoiceUrl: String
  pollingEnabled: Boolean!
  owner: ID!
  primaryIp: String!
  secondaryIp: String
  serviceEndDate: Date
}

input UpdateSwitchInput {
  arpEnabled: Boolean!
  bgpEnabled: Boolean!
  cabinet: ID!
  comment: String
  distributor: String
  invoiceUrl: String
  pollingEnabled: Boolean!
  owner: ID!
  primaryIp: String!
  secondaryIp: String
  serviceEndDate: Date
  sflowToFlop: Boolean!
}
