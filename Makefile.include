.DEFAULT_GOAL = build

PHP = php$(PHP_VERSION)

ifndef SYMFONY_ENV
SYMFONY_ENV=dev
endif

%:
	@{ \
    	target=$@; \
    	if [ "$${target}" != "$${target#default-}" ]; then \
    	    if [ -e "$${target#default-}" ]; then \
    	        exit 0; \
			fi; \
    	    >&2 echo "make: No rule to make target '$${target#default-}'." ; \
    	    exit 2; \
		fi; \
		if [ "$${target}" = "Makefile" ] || [ "$${target}" = "Makefile.include" ]; then \
		    exit 0; \
		fi; \
		if [ -z "$${TRY_DEFAULT_TARGET+set}" ]; then \
			TRY_DEFAULT_TARGET="$${target}" $(MAKE) -f Makefile.include default-$@; \
		else \
		    unset TRY_DEFAULT_TARGET; \
		    $(MAKE) $@; \
		fi; \
    }

list:
	@make -pRrq | grep -E "^[a-zA-Z0-9-]+:" | grep -v Makefile: | cut -d: -f1 | sed 's/default-//' | sort | uniq

default-build: vendor

default-check: build composer-unused composer-requirements phpcs phpstan psalm test-unit test-integration

default-fix: build
	$(MAKE) phpcs-fix || true

default-check-and-fix: fix check

default-vendor: composer.lock
	composer validate --no-check-all --strict
	composer install $(COMPOSER_ARGS)
	touch vendor

default-composer-unused:
	composer unused $(COMPOSER_UNUSED_ARGS)

default-composer-requirements:
	@{ \
	set -o errexit -o nounset ; \
	if ! output=$$(2>&1 vendor/bin/composer-require-checker); then \
		if echo "$${output}" | grep -q "There were no symbols found, please check your configuration."; then \
			echo "No packages required, check skipped"; \
			exit 0; \
		fi; \
		echo "$${output}"; \
		exit 1; \
	fi; \
	echo "$${output}"; \
	}

default-phpcs:
	vendor/bin/phpcs $(PHPCS_ARGS)

default-phpcs-fix:
	vendor/bin/phpcbf $(PHPCS_ARGS)

default-phpstan:
	@test ! -f phpstan.neon.dist && { echo "Missing phpstan.neon.dist, skipping" ; } || vendor/bin/phpstan analyse $(PHPSTAN_ARGS)

default-psalm:
	@test ! -f psalm.xml.dist && { echo "Missing psalm.xml.dist, skipping" ; } || vendor/bin/psalm $(PSALM_ARGS)

default-test: test-unit test-integration

default-test-unit:
	@{ \
	set -o errexit -o nounset ; \
	hasUnitGroup=$$( \
		find tests -type f -name "*Test.php" -print0 \
			| xargs -0 grep -m 1 -E '\* @group +unit\s' 2>/dev/null \
			| head -n1 \
			|| true \
	) ; \
	phpunitArgs="$(PHPUNIT_ARGS)" ; \
	[ -n "$${hasUnitGroup}" ] && phpunitArgs="$$phpunitArgs --group unit" ; \
	vendor/bin/phpunit $$phpunitArgs ; \
	}

default-test-integration:
	@{ \
	set -o errexit -o nounset ; \
	hasIntegrationGroup=$$( \
		find tests -type f -name "*Test.php" -print0 \
			| xargs -0 grep -m 1 -E '\* @group +integration\s' 2>/dev/null \
			| head -n1 \
			|| true \
	) ; \
	[ -z "$${hasIntegrationGroup}" ] && { echo Project has no integration tests ; exit 0 ; } ; \
	vendor/bin/phpunit $(PHPUNIT_ARGS) --group integration ; \
	}

.PHONY: default-cache-warmup
default-cache-warmup: var/cache/$(SYMFONY_ENV)

.PHONY: default-cache-warmup-all
default-cache-warmup-all: var/cache/dev var/cache/prod var/cache/test

var/cache/dev: vendor $(shell find src/ -name \*.php -o -name \*.yaml -o -name \*.xml -type f)
	$(PHP) bin/console cache:warmup -n --env=dev

var/cache/prod: vendor $(shell find src/ -name \*.php -o -name \*.yaml -o -name \*.xml -type f)
	$(PHP) bin/console cache:warmup -n --env=prod

var/cache/test: vendor $(shell find src/ -name \*.php -o -name \*.yaml -o -name \*.xml -type f)
	$(PHP) bin/console cache:warmup -n --env=test

default-clean: clean-cache clean-vendor clean-makefile

default-clean-cache:
	rm -rf var/cache/*

default-clean-vendor:
	rm -rf vendor

default-clean-makefile:
	rm -f Makefile.include

.PHONY: list default-build default-check default-fix default-check-and-fix default-composer-unused
.PHONY: default-composer-requirements default-phpcs default-phpcs-fix default-phpstan default-psalm default-test
.PHONY: default-test-unit default-test-integration default-clean default-clean-cache default-clean-vendor

# compatibility for next few days:
.PHONY: default-static-analysis
default-static-analysis: phpstan psalm

# compatibility for next few days:
.PHONY: default-cs
default-cs: phpcs
