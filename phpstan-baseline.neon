parameters:
	ignoreErrors:
		-
			message: '#^Parameter \#1 \$keys of function array_combine expects array\<int\|string\>, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: src/Core/Application/OpenApi/PathGenerator.php

		-
			message: '#^Parameter \#2 \$values of function array_combine expects array, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: src/Core/Application/OpenApi/PathGenerator.php

		-
			message: '#^Property Cdn77\\Api\\Core\\Application\\OpenApi\\PathGenerator\:\:\$compiledRoutes \(array\<string, array\<int, mixed\>\>\) does not accept mixed\.$#'
			identifier: assign.propertyType
			count: 1
			path: src/Core/Application/OpenApi/PathGenerator.php

		-
			message: '#^Method Cdn77\\Api\\CoreLibrary\\JMS\\EnumHandler\:\:deserialize\(\) should return \(T of UnitEnum\)\|null but returns mixed\.$#'
			identifier: return.type
			count: 1
			path: src/CoreLibrary/JMS/EnumHandler.php

		-
			message: '#^Return type \(Generator\<mixed, array\<string, int\|string\>, mixed, mixed\>\) of method Cdn77\\Api\\CoreLibrary\\JMS\\EnumHandler\:\:getSubscribingMethods\(\) should be compatible with return type \(array\) of method JMS\\Serializer\\Handler\\SubscribingHandlerInterface\:\:getSubscribingMethods\(\)$#'
			identifier: method.childReturnType
			count: 1
			path: src/CoreLibrary/JMS/EnumHandler.php

		-
			message: '#^Variable method call on object\.$#'
			identifier: method.dynamicName
			count: 2
			path: src/GraphQL/Application/Runtime/DefaultFieldResolver.php

		-
			message: '#^Variable property access on object\.$#'
			identifier: property.dynamicName
			count: 2
			path: src/GraphQL/Application/Runtime/DefaultFieldResolver.php

		-
			message: '#^Parameter \#1 \$hasSslValid of static method Cdn77\\Api\\Origin\\Application\\Payload\\OriginSslCertificateSchema\:\:fromBool\(\) expects bool, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: src/Origin/Application/Payload/OriginSslCertificateSchema.php

		-
			message: '#^Parameter \#1 \$payment of static method Cdn77\\Api\\Payment\\Application\\Payload\\SuccessfulPaymentSchema\:\:fromPaygatePayment\(\) expects Paygate\\Types\\Payment\\Payment, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: src/Payment/Application/Payload/SuccessfulPaymentSchema.php

		-
			message: '#^Argument of an invalid type mixed supplied for foreach, only iterables are supported\.$#'
			identifier: foreach.nonIterable
			count: 2
			path: src/Service/PushZone/Api/Endpoint/GetNewStatsDetailEndpoint.php

		-
			message: '#^Parameter \#1 \$zoneId of class Cdn77\\Api\\Service\\PushZone\\Api\\Endpoint\\Payload\\GetNewStatsDetailResponsePayloadStatistic constructor expects string, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: src/Service/PushZone/Api/Endpoint/GetNewStatsDetailEndpoint.php

		-
			message: '#^Parameter \#2 \$datetime of static method DateTimeImmutable\:\:createFromFormat\(\) expects string, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: src/Service/PushZone/Api/Endpoint/GetNewStatsDetailEndpoint.php

		-
			message: '#^Parameter \#2 \$string of function explode expects string, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: src/Service/PushZone/Api/Endpoint/GetNewStatsDetailEndpoint.php

		-
			message: '#^Parameter \#1 \$timestamps of class Cdn77\\Api\\Service\\PushZone\\Api\\Endpoint\\Payload\\GetNewStatsListPayload constructor expects array\<string\>, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: src/Service/PushZone/Api/Endpoint/GetNewStatsListEndpoint.php

		-
			message: '#^Parameter \#1 \$storageUsage of static method Cdn77\\Api\\StoragePlan\\Application\\Payload\\UsageSchema\:\:fromTotalStorageUsage\(\) expects Cdn77\\Api\\StoragePlan\\Domain\\Value\\StorageUsage, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: src/StoragePlan/Application/Payload/UsageSchema.php

		-
			message: '#^Variable method call on \$this\(Cdn77\\Api\\Tests\\TestCase\)\.$#'
			identifier: method.dynamicName
			count: 1
			path: tests/TestCase.php

		-
			message: '#^Variable static method call on Cdn77\\Api\\Tests\\TestCase\.$#'
			identifier: staticMethod.dynamicName
			count: 1
			path: tests/TestCase.php
