parameters:
    level: max
    doctrine:
        objectManagerLoader: tests/doctrine-orm-object-manager-bootstrap.php
    symfony:
        containerXmlPath: var/cache/test/Cdn77_Api_KernelTestDebugContainer.xml
    exceptions:
        check:
            tooWideThrowType: true
            missingCheckedExceptionInThrows: true
        uncheckedExceptionClasses:
            - 'Webmozart\Assert\InvalidArgumentException'
            - 'Safe\Exceptions\PcreException'
            - 'Cdn77\Api\Core\Domain\Exception\Absurd'
            - 'TypeError'
            - 'ValueError'
            - 'DateMalformedStringException'
            - 'Psl\Regex\Exception\InvalidPatternException'
            - 'Psl\Regex\Exception\RuntimeException'
    paths:
        - %currentWorkingDirectory%/bin/console
        - %currentWorkingDirectory%/migrations
        - %currentWorkingDirectory%/phpstan
        - %currentWorkingDirectory%/public
        - %currentWorkingDirectory%/src
        - %currentWorkingDirectory%/tests
        - %currentWorkingDirectory%/bootstrap.php
        - %currentWorkingDirectory%/composer-dependency-analyser.php
        - %currentWorkingDirectory%/rector.php
        - %currentWorkingDirectory%/sailor.php
    tmpDir: %currentWorkingDirectory%/var/phpstan
    cognitive_complexity:
        class: 50
        function: 10
    checkMissingCallableSignature: true
    checkImplicitMixed: true
    cdn77:
        messaging:
            buses:
                - message: "Cdn77\\Api\\Core\\Domain\\Messaging\\Command"
                  messageBus: "Cdn77\\Api\\Core\\Domain\\Messaging\\CommandBus"
                  handlerMethodName: "handle"
                - message: "Cdn77\\Api\\Core\\Domain\\Messaging\\Query"
                  messageBus: "Cdn77\\Api\\Core\\Domain\\Messaging\\QueryBus"
                  handlerMethodName: "handle"
    ignoreErrors:
        # Safe\DateTimeImmutable - would create overhead
        - '~Class DateTimeImmutable is unsafe to use. Its methods can return FALSE instead of throwing an exception\.~'
        # Bugs
        - '~Call to static method Webmozart\\Assert\\Assert::allMinLength\(\) with array\<string\> and int will always evaluate to true\.~'
        - '~Call to static method Webmozart\\Assert\\Assert::allMaxLength\(\) with array\<string\> and int will always evaluate to true\.~'
        - '#^Access to an undefined property GraphQL\\Type\\Definition\\NamedType&GraphQL\\Type\\Definition\\Type\:\:\$name\.$#'
        - '~^Parameter #2 \$next \(callable\(TCommand\)\: mixed\) of method Cdn77\\Api\\CoreLibrary\\Tactician\\Middleware\\\w+\:\:execute\(\) should be contravariant with parameter \$next \(callable\(\)\: mixed\) of method League\\Tactician\\Middleware\:\:execute\(\)$~'
        # Symfony errors
        -
            message: "#^Method Cdn77\\\\Api\\\\Tests\\\\Utils\\\\NoopPropertyAccessor\\:\\:\\w+\\(\\) has parameter \\$\\w+ with no value type specified in iterable type (array|Symfony\\\\Component\\\\PropertyAccess\\\\PropertyPathInterface)\\.$#"
            count: 3
            path: tests/Utils/NoopPropertyAccessor.php

        # This might work only with some advanced TypeScript-level generics which PHP does not have
        - '~Parameter #1 \$resolver of method SimPod\\GraphQLUtils\\Builder\\FieldBuilder::setResolver\(\) expects callable\(mixed, array<string, mixed>, mixed, GraphQL\\Type\\Definition\\ResolveInfo\): mixed, Closure\(.+?(, array, mixed, GraphQL\\Type\\Definition\\ResolveInfo)?\): .+? given~'

        # Throws
        - "#^Method Cdn77\\\\Api\\\\Core\\\\Infrastructure\\\\Ceph\\\\S3ClientApi\\:\\:(put|get)BucketPolicy\\(\\) throws checked exception Exception but it's missing from the PHPDoc @throws tag\\.$#"

        # Intentional Catch-all
        -
            message: '#^Dead catch \- Throwable is never thrown in the try block\.$#'
            count: 1
            path: %currentWorkingDirectory%/src/Core/Application/Response/ControllerCommandHandler.php
        -
            message: '#^Dead catch \- Throwable is never thrown in the try block\.$#'
            count: 1
            path: %currentWorkingDirectory%/src/Cdn/Application/Mapping/CdnResponseFactory.php

        # BC Break
        - '~^Method Prometheus\\RegistryInterface::getMetricFamilySamples\(\) invoked with 1 parameter, 0 required\.~'

        # Cognitive complexity
        - "#^Cognitive complexity for \"Cdn77\\\\Api\\\\Tests\\\\.+\\:\\:kernelTearDown\\(\\)\" is 19, keep it under 10$#"

        # Do not need to have @throws in PHPUnit's methods that are never called
        - "~Method Cdn77\\\\Api\\\\Tests\\\\.+?Test(Case(Base)?)?::(test.*?|setUp(BeforeClass)?|tearDown)\\(\\) throws checked exception .+? but it's missing from the PHPDoc @throws tag~"

        # https://github.com/azjezz/psl/issues/440
        - '#Parameter \#2 \$mapper of function Cdn77\\Functions\\(mapFromIterable|setFromIterable|mappedSetsFromIterable) expects callable(.+?): Ds\\Pair<(((.+?&K|K&.+?|K), .+?)|(.+?&V.*?)|V)>, .+? given#'

        # only written incorrectly reported on phpstan-doctrine embeddables
        - '#^Property Cdn77\\Api\\Core\\Domain\\Entity\\Customer\\Credentials\:\:\$slackChannel is never read, only written.#'

        # trait issue
        -
            message: '~^Method Cdn77\\Api\\CoreLibrary\\Tactician\\Middleware\\AuthorizationMiddleware::execute\(\) throws checked exception Symfony\\Component\\Security\\Core\\Exception\\AccessDeniedException but it''s missing from the PHPDoc @throws tag\.~'
            count: 1
            path: %currentWorkingDirectory%/src/CoreLibrary/Tactician/Middleware/AuthorizationMiddleware.php
        -
            message: '~^Method Cdn77\\Api\\CoreLibrary\\Tactician\\Middleware\\AuthorizationMiddleware::execute\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\Customer\\CustomerNotFound but it''s missing from the PHPDoc @throws tag\.~'
            count: 1
            path: %currentWorkingDirectory%/src/CoreLibrary/Tactician/Middleware/AuthorizationMiddleware.php
        -
            message: '~^Method Cdn77\\Api\\CoreLibrary\\Tactician\\Middleware\\AuthorizationMiddleware::execute\(\) throws checked exception Cdn77\\Api\\Core\\Domain\\Exception\\NotImplemented but it''s missing from the PHPDoc @throws tag\.~'
            count: 1
            path: %currentWorkingDirectory%/src/CoreLibrary/Tactician/Middleware/AuthorizationMiddleware.php

includes:
    - phpstan.deadCodeDetector.neon
    - phpstan.strictRules.neon
    - phpstan-baseline.neon
    - phpstan-baseline.cognitive-complexity.neon
    - phpstan-baseline.throws.neon
    - phpstan-baseline.implicit-mixed.neon
    - phpstan-baseline.2_0.neon
    - valinor-phpstan-configuration-fix.php
    - phpstan-baseline.dead-code.neon
# https://github.com/phpstan/phpstan/issues/11386
    - phpstan-baseline.bug.neon
# https://github.com/phpstan/phpstan/issues/6732#issuecomment-1062029088
#    - vendor/phpstan/phpstan/conf/bleedingEdge.neon
