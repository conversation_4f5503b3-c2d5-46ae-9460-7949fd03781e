<?php

declare(strict_types=1);

namespace Cdn77\Api;

use Dotenv\Dotenv;
use Symfony\Component\ErrorHandler\Debug;
use Webmozart\Assert\Assert;

use function date_default_timezone_set;
use function error_reporting;
use function sprintf;

use const E_ALL;

error_reporting(E_ALL);
date_default_timezone_set('UTC');

require __DIR__ . '/vendor/autoload.php';

$dotenv = Dotenv::createImmutable(__DIR__);
$dotenv->safeLoad();

$env = $_SERVER['SYMFONY_ENV'] ?? '';
$debug = ($_SERVER['SYMFONY_DEBUG'] ?? false) === '1';

Assert::string($env);
Assert::inArray($env, ['prod', 'test', 'dev'], sprintf('Application does not support env "%s"', $env));

if ($debug) {
    Debug::enable();
}

return new Kernel($env, $debug);
