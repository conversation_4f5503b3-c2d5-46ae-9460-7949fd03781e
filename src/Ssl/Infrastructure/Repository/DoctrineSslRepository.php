<?php

declare(strict_types=1);

namespace Cdn77\Api\Ssl\Infrastructure\Repository;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Entity\Ssl\Ssl;
use Cdn77\Api\Core\Infrastructure\EntityManagerConstructor;
use Cdn77\Api\Ssl\Domain\Contract\SslId;
use Cdn77\Api\Ssl\Domain\Enum\SslType;
use Cdn77\Api\Ssl\Domain\Exception\SslNotFound;
use Cdn77\Api\Ssl\Domain\Repository\SslRepository;
use Ds\Map;
use Ds\Set;

final class DoctrineSslRepository implements SslRepository
{
    use EntityManagerConstructor;

    public function add(Ssl $ssl): void
    {
        $this->entityManager->persist($ssl);
    }

    public function get(SslId $sslId, SslType $type): Ssl
    {
        return $this->entityManager->getRepository(Ssl::class)->findOneBy([
            Ssl::FieldId => $sslId->toUid(),
            Ssl::FieldType => $type->value,
            Ssl::FieldDeletedAt => null,
        ]) ?? throw SslNotFound::forIdAndType($sslId, $type);
    }

    public function findAllForCustomer(CustomerId $customerId, SslType $type): Set
    {
        /** @var iterable<Ssl> $ssls */
        $ssls = $this->entityManager->createQueryBuilder()
            ->select('ssl', 'addOn')
            ->from(Ssl::class, 'ssl')
            ->join('ssl.addOn', 'addOn')
            ->join('addOn.customer', 'customer')
            ->where('customer.id = :customerId')
            ->andWhere('ssl.deletedAt IS NULL')
            ->andWhere('ssl.type = :sslType')
            ->setParameter('customerId', $customerId->toInt())
            ->setParameter('sslType', $type->value)
            ->getQuery()
            ->toIterable();

        return new Set($ssls);
    }

    /** @return Map<SslId, CustomerId> */
    public function findAllCertificateUuidsForActiveCustomers(): Map
    {
        $qb = $this->entityManager->createQueryBuilder();
        $qb->select('ssl.id AS uuid', 'customer.id AS customerId')
            ->from(Ssl::class, 'ssl')
            ->join('ssl.addOn', 'addOn')
            ->join('addOn.customer', 'customer')
            ->where('ssl.deletedAt IS NULL')
            ->andWhere('customer.suspendedAt IS NULL');

        /** @var array<array{uuid: string, customerId: int}> $results */
        $results = $qb->getQuery()->getArrayResult();

        /** @var Map<SslId, CustomerId> $map */
        $map = new Map();
        foreach ($results as $row) {
            $map->put(
                SslId::fromString($row['uuid']),
                CustomerId::fromInteger($row['customerId']),
            );
        }

        return $map;
    }

    /** @return Set<Ssl> */
    public function findAllNotDeleted(int|null $limit = null): Set
    {
        $qb = $this->entityManager->createQueryBuilder()
            ->select('ssl', 'addOn', 'customer')
            ->from(Ssl::class, 'ssl')
            ->join('ssl.addOn', 'addOn')
            ->join('addOn.customer', 'customer')
            ->where('ssl.deletedAt IS NULL')
            ->orderBy('ssl.legacyId', 'ASC');

        if ($limit !== null) {
            $qb->setMaxResults($limit);
        }

        /** @var iterable<Ssl> $ssls */
        $ssls = $qb->getQuery()->toIterable();

        return new Set($ssls);
    }
}
