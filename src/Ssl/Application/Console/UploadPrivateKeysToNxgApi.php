<?php

declare(strict_types=1);

namespace Cdn77\Api\Ssl\Application\Console;

use Cdn77\Api\Core\Application\Console\LockAcquirer;
use Cdn77\Api\Core\Application\Console\Value\LockName;
use Cdn77\Api\Core\Console\CronCommand;
use Cdn77\Api\Core\Domain\Entity\Ssl\Ssl;
use Cdn77\Api\Core\Domain\NxgApi\Exception\NxgRequestFailed;
use Cdn77\Api\Core\Domain\NxgApi\NxgApi;
use Cdn77\Api\Ssl\Domain\Repository\SslRepository;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

use function sprintf;

#[AsCommand(
    name: self::Name,
    description: 'Upload all private keys from non-deleted SSL certificates to nxg-api.',
)]
final class UploadPrivateKeysToNxgApi extends Command implements CronCommand
{
    public const string Name = 'ssl:upload-private-keys';

    public function __construct(
        private readonly SslRepository $sslRepository,
        private readonly NxgApi $nxgApi,
        private readonly LoggerInterface $logger,
        private readonly LockAcquirer $lockAcquirer,
    ) {
        parent::__construct();
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('Starting upload of private keys to nxg-api...');

        $this->lockAcquirer->execute(new LockName(self::Name));

        $sslCertificates = $this->sslRepository->findAllNotDeleted();
        $totalCount = $sslCertificates->count();
        $successCount = 0;
        $errorCount = 0;

        $output->writeln(sprintf('Found %d SSL certificates to process.', $totalCount));

        foreach ($sslCertificates as $ssl) {
            /** @var Ssl $ssl */
            try {
                $customerId = $ssl->addOn()->getCustomer()->getId();
                $sslId = $ssl->id();
                $certificate = $ssl->certificate();
                $privateKey = $ssl->privateKey();

                $this->nxgApi->storePrivateKey($customerId, $sslId, $certificate, $privateKey);
                $successCount++;

                $output->writeln(sprintf(
                    'Successfully uploaded private key for SSL ID: %s (Customer ID: %d)',
                    $sslId->toString(),
                    $customerId->toInt()
                ), OutputInterface::VERBOSITY_VERBOSE);
            } catch (NxgRequestFailed $e) {
                $errorCount++;
                $errorMessage = sprintf(
                    'Failed to upload private key for SSL ID: %s (Customer ID: %d) - %s',
                    $sslId->toString(),
                    $customerId->toInt(),
                    $e->getMessage()
                );

                $this->logger->error($errorMessage, ['exception' => $e]);
                $output->writeln($errorMessage, OutputInterface::VERBOSITY_NORMAL);
            } catch (\Throwable $e) {
                $errorCount++;
                $errorMessage = sprintf(
                    'Unexpected error for SSL ID: %s (Customer ID: %d) - %s',
                    $ssl->id()->toString(),
                    $customerId->toInt(),
                    $e->getMessage()
                );

                $this->logger->error($errorMessage, ['exception' => $e]);
                $output->writeln($errorMessage, OutputInterface::VERBOSITY_NORMAL);
            }
        }

        $output->writeln(sprintf(
            'Upload completed. Total: %d, Success: %d, Errors: %d',
            $totalCount,
            $successCount,
            $errorCount
        ));

        return $errorCount > 0 ? Command::FAILURE : Command::SUCCESS;
    }

    public function getName(): string
    {
        return self::Name;
    }
}
