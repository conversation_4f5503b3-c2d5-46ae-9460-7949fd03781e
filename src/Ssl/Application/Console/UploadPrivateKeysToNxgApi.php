<?php

declare(strict_types=1);

namespace Cdn77\Api\Ssl\Application\Console;

use Cdn77\Api\Core\Application\Console\LockAcquirer;
use Cdn77\Api\Core\Application\Console\Value\LockName;
use Cdn77\Api\Core\Console\CronCommand;
use Cdn77\Api\Core\Domain\Entity\Ssl\Ssl;
use Cdn77\Api\Core\Domain\NxgApi\Exception\NxgRequestFailed;
use Cdn77\Api\Core\Domain\NxgApi\NxgApi;
use Cdn77\Api\Ssl\Domain\Repository\SslRepository;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

use function sprintf;

#[AsCommand(
    name: self::Name,
    description: 'Upload all private keys from non-deleted SSL certificates to nxg-api.',
)]
final class UploadPrivateKeysToNxgApi extends Command implements CronCommand
{
    public const string Name = 'ssl:upload-private-keys';
    private const string OptionLimit = 'limit';

    public function __construct(
        private readonly SslRepository $sslRepository,
        private readonly NxgApi $nxgApi,
        private readonly LoggerInterface $logger,
        private readonly LockAcquirer $lockAcquirer,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->addOption(
            self::OptionLimit,
            'l',
            InputOption::VALUE_REQUIRED,
            'Maximum number of SSL certificates to process in this run (must be a positive integer)',
            null
        );
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('Starting upload of private keys to nxg-api...');

        $this->lockAcquirer->execute(new LockName(self::Name));

        $limit = $input->getOption(self::OptionLimit);
        if ($limit !== null) {
            $limit = (int) $limit;
            if ($limit <= 0) {
                $output->writeln('<error>Limit must be a positive integer.</error>');
                return Command::INVALID;
            }
        }

        // Load already uploaded private key UUIDs from nxg-api
        $output->writeln('Loading already uploaded private key UUIDs from nxg-api...');
        try {
            $uploadedPrivateKeys = $this->nxgApi->loadPrivateKeyUuids();
            $output->writeln(sprintf('Found %d already uploaded private keys in nxg-api.', $uploadedPrivateKeys->count()));
        } catch (NxgRequestFailed $e) {
            $output->writeln('<error>Failed to load uploaded private keys from nxg-api: ' . $e->getMessage() . '</error>');
            $this->logger->error('Failed to load uploaded private keys from nxg-api', ['exception' => $e]);
            return Command::FAILURE;
        }

        $sslCertificates = $this->sslRepository->findAllNotDeleted();
        $totalAvailable = $sslCertificates->count();

        if ($limit !== null) {
            $output->writeln(sprintf('Found %d SSL certificates, will process up to %d.', $totalAvailable, $limit));
        } else {
            $output->writeln(sprintf('Found %d SSL certificates to process.', $totalAvailable));
        }

        $successCount = 0;
        $errorCount = 0;
        $skippedCount = 0;
        $processedCount = 0;

        foreach ($sslCertificates as $ssl) {
            /** @var Ssl $ssl */
            $sslId = $ssl->id();

            // Skip if already uploaded
            if ($uploadedPrivateKeys->hasKey($sslId)) {
                $skippedCount++;
                $output->writeln(sprintf(
                    'Skipping SSL ID: %s (already uploaded)',
                    $sslId->toString()
                ), OutputInterface::VERBOSITY_VERBOSE);
                continue;
            }

            // Check if we've reached the processing limit
            if ($limit !== null && $processedCount >= $limit) {
                $output->writeln(sprintf('Reached processing limit of %d certificates.', $limit));
                break;
            }

            try {
                $customerId = $ssl->addOn()->getCustomer()->getId();
                $certificate = $ssl->certificate();
                $privateKey = $ssl->privateKey();

                $this->nxgApi->storePrivateKey($customerId, $sslId, $certificate, $privateKey);
                $successCount++;
                $processedCount++;

                $output->writeln(sprintf(
                    'Successfully uploaded private key for SSL ID: %s (Customer ID: %d)',
                    $sslId->toString(),
                    $customerId->toInt()
                ), OutputInterface::VERBOSITY_VERBOSE);
            } catch (NxgRequestFailed $e) {
                $errorCount++;
                $processedCount++;
                $errorMessage = sprintf(
                    'Failed to upload private key for SSL ID: %s (Customer ID: %d) - %s',
                    $sslId->toString(),
                    $ssl->addOn()->getCustomer()->getId()->toInt(),
                    $e->getMessage()
                );

                $this->logger->error($errorMessage, ['exception' => $e]);
                $output->writeln($errorMessage, OutputInterface::VERBOSITY_NORMAL);
            } catch (\Throwable $e) {
                $errorCount++;
                $processedCount++;
                $errorMessage = sprintf(
                    'Unexpected error for SSL ID: %s (Customer ID: %d) - %s',
                    $sslId->toString(),
                    $ssl->addOn()->getCustomer()->getId()->toInt(),
                    $e->getMessage()
                );

                $this->logger->error($errorMessage, ['exception' => $e]);
                $output->writeln($errorMessage, OutputInterface::VERBOSITY_NORMAL);
            }
        }

        $output->writeln(sprintf(
            'Upload completed. Available: %d, Processed: %d, Success: %d, Skipped: %d, Errors: %d',
            $totalAvailable,
            $processedCount,
            $successCount,
            $skippedCount,
            $errorCount
        ));

        return $errorCount > 0 ? Command::FAILURE : Command::SUCCESS;
    }

    public function getName(): string
    {
        return self::Name;
    }
}
