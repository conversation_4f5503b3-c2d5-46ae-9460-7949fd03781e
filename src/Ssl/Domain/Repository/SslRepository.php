<?php

declare(strict_types=1);

namespace Cdn77\Api\Ssl\Domain\Repository;

use Cdn77\Api\Core\Domain\Entity\Customer\CustomerId;
use Cdn77\Api\Core\Domain\Entity\Ssl\Ssl;
use Cdn77\Api\Ssl\Domain\Contract\SslId;
use Cdn77\Api\Ssl\Domain\Enum\SslType;
use Cdn77\Api\Ssl\Domain\Exception\SslNotFound;
use Ds\Map;
use Ds\Set;

interface SslRepository
{
    public function add(Ssl $ssl): void;

    /** @throws SslNotFound */
    public function get(SslId $sslId, SslType $type): Ssl;

    /** @return Set<Ssl> */
    public function findAllForCustomer(CustomerId $customerId, SslType $type): Set;

    /** @return Map<SslId, CustomerId> */
    public function findAllCertificateUuidsForActiveCustomers(): Map;

    /** @return Set<Ssl> */
    public function findAllNotDeleted(int|null $limit = null): Set;
}
