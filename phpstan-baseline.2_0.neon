parameters:
	ignoreErrors:
		-
			message: '#^Binary operation "\." between ''Bearer '' and mixed results in an error\.$#'
			identifier: binaryOp.invalid
			count: 1
			path: sailor.php

		-
			message: '#^Property Cdn77\\Api\\Cdn\\Application\\Payload\\EnabledDatacentersSchema\:\:\$datacenterIds \(list\<Symfony\\Component\\Uid\\Uuid\>\|null\) is never assigned list\<Symfony\\Component\\Uid\\Uuid\> so it can be removed from the property type\.$#'
			identifier: property.unusedType
			count: 1
			path: src/Cdn/Application/Payload/EnabledDatacentersSchema.php

		-
			message: '#^Parameter \#1 \$logs of class Cdn77\\Api\\Cdn\\Application\\Payload\\Forman\\ListActiveRealTimeLogsSchema constructor expects list\<Cdn77\\Api\\Cdn\\Application\\Payload\\Forman\\ActiveRealTimeLogSchema\>, array\<int, Cdn77\\Api\\Cdn\\Application\\Payload\\Forman\\ActiveRealTimeLogSchema\> given\.$#'
			identifier: argument.type
			count: 1
			path: src/Cdn/Application/Payload/Forman/ListActiveRealTimeLogsSchema.php

		-
			message: '#^Parameter \#1 \$logs of class Cdn77\\Api\\Cdn\\Application\\Payload\\Log\\ListCustomerRealTimeLogSchema constructor expects list\<Cdn77\\Api\\Cdn\\Application\\Payload\\Log\\CustomerRealTimeLogSchema\>, array\<int, Cdn77\\Api\\Cdn\\Application\\Payload\\Log\\CustomerRealTimeLogSchema\> given\.$#'
			identifier: argument.type
			count: 1
			path: src/Cdn/Application/Payload/Log/ListCustomerRealTimeLogSchema.php

		-
			message: '#^Parameter \#2 \$mapper of function Cdn77\\Functions\\mapFromIterable expects callable\(int\<0, max\>, array\{cdn_id\: int, streaming_playlist_bypass\: bool\}\)\: Ds\\Pair\<Cdn77\\Api\\Core\\Domain\\Entity\\Cdn\\CdnId, mixed\>, Closure\(mixed, array\)\: Ds\\Pair\<Cdn77\\Api\\Core\\Domain\\Entity\\Cdn\\CdnId, bool\> given\.$#'
			identifier: argument.type
			count: 1
			path: src/Cdn/Infrastructure/Finder/DbalStreamingPlaylistBypassEnabledStatusFinder.php

		-
			message: '#^Parameter \#1 \$cnames of static method Cdn77\\Api\\Cname\\Application\\Payload\\CnamesSchema\:\:fromCnames\(\) expects list\<Cdn77\\Api\\Core\\Domain\\Entity\\Cname\\Cname\>, list\<mixed\> given\.$#'
			identifier: argument.type
			count: 1
			path: src/Cname/Application/Payload/CnamesSchema.php

		-
			message: '#^Property Cdn77\\Api\\Cname\\Application\\Payload\\NewCnameSchema\:\:\$cname \(string\|null\) is never assigned string so it can be removed from the property type\.$#'
			identifier: property.unusedType
			count: 1
			path: src/Cname/Application/Payload/NewCnameSchema.php

		-
			message: '#^PHPDoc tag @var with type class\-string\<Cdn77\\Api\\Core\\Application\\Controller\\IsAllowedForSource\> is not subtype of type class\-string\<T of Cdn77\\Api\\Core\\Application\\Controller\\IsAllowedForSource\>\.$#'
			identifier: varTag.type
			count: 1
			path: src/Core/Application/Symfony/Authorization/AccessResolver.php

		-
			message: '#^Parameter \#1 \.\.\.\$values of method Ds\\Set\<covariant Cdn77\\Api\\Core\\Domain\\Value\\RequestSource\>\:\:contains\(\) expects never, Cdn77\\Api\\Core\\Domain\\Value\\RequestSource\:\:Crop\|Cdn77\\Api\\Core\\Domain\\Value\\RequestSource\:\:PublicWeb given\.$#'
			identifier: argument.type
			count: 1
			path: src/Core/Application/Symfony/Authorization/AccessResolver.php

		-
			message: '#^PHPDoc tag @var with type Ds\\Set\<Cdn77\\Api\\Cdn\\Domain\\Value\\MaxAge404\> is not subtype of type Ds\\Set\<Cdn77\\Api\\Cdn\\Domain\\Value\\MaxAge\|Cdn77\\Api\\Cdn\\Domain\\Value\\MaxAge404\>\.$#'
			identifier: varTag.type
			count: 1
			path: src/Core/Domain/Resolver/CustomerMaxAgeResolver.php

		-
			message: '#^PHPDoc tag @var with type Ds\\Set\<Cdn77\\Api\\Cdn\\Domain\\Value\\MaxAge\> is not subtype of type Ds\\Set\<Cdn77\\Api\\Cdn\\Domain\\Value\\MaxAge\|Cdn77\\Api\\Cdn\\Domain\\Value\\MaxAge404\>\.$#'
			identifier: varTag.type
			count: 1
			path: src/Core/Domain/Resolver/CustomerMaxAgeResolver.php

		-
			message: '#^PHPDoc tag @var with type Ds\\Map\<Cdn77\\Api\\Stats\\Domain\\Value\\DataSource, Cdn77\\Api\\Core\\Domain\\Stats\\StatsProvider\> is not subtype of type Ds\\Map\<Cdn77\\Api\\Stats\\Domain\\Value\\DataSource, Cdn77\\Api\\Core\\Infrastructure\\Ara\\Provider\\AraStatsProvider\|Cdn77\\Api\\Core\\Infrastructure\\ClickHouse\\Provider\\ClickHouseLiveStreamingStatsProvider\|Cdn77\\Api\\Core\\Infrastructure\\ClickHouse\\Provider\\ClickHouseStatsProvider\>\.$#'
			identifier: varTag.type
			count: 1
			path: src/Core/Domain/Stats/StatsProviderBag.php

		-
			message: '#^PHPDoc tag @var with type Ds\\Map\<Cdn77\\Api\\Core\\Domain\\Value\\Origin\\OriginPriority, Cdn77\\Api\\Core\\Domain\\Dto\\Origin\\CdnOrigin\> is not subtype of type Ds\\Map\<Cdn77\\Api\\Core\\Domain\\Value\\Origin\\OriginPriority, Cdn77\\Api\\Core\\Domain\\Dto\\Origin\\ObjectStorageOrigin\|Cdn77\\Api\\Core\\Domain\\Dto\\Origin\\S3Origin\|Cdn77\\Api\\Core\\Domain\\Dto\\Origin\\UrlOrigin\>\.$#'
			identifier: varTag.type
			count: 1
			path: src/Core/Domain/Value/Origin/CdnOrigins.php

		-
			message: '#^Call to function array_filter\(\) requires parameter \#2 to be passed to avoid loose comparison semantics\.$#'
			identifier: arrayFilter.strict
			count: 2
			path: src/Core/Infrastructure/Pipedrive/AddDealEndpoint.php

		-
			message: '#^Call to function array_filter\(\) requires parameter \#2 to be passed to avoid loose comparison semantics\.$#'
			identifier: arrayFilter.strict
			count: 2
			path: src/Core/Infrastructure/Pipedrive/AddLeadEndpoint.php

		-
			message: '#^Call to function array_filter\(\) requires parameter \#2 to be passed to avoid loose comparison semantics\.$#'
			identifier: arrayFilter.strict
			count: 1
			path: src/Core/Infrastructure/Pipedrive/AddPersonEndpoint.php

		-
			message: '#^Parameter \#3 \$fields of class Cdn77\\Api\\Core\\Infrastructure\\Pipedrive\\SearchDealsEndpoint constructor expects Ds\\Set\<Cdn77\\Api\\Core\\Domain\\Pipedrive\\Enum\\DealSearchField\>, Ds\\Set\<covariant Cdn77\\Api\\Core\\Domain\\Pipedrive\\Enum\\DealSearchField\> given\.$#'
			identifier: argument.type
			count: 1
			path: src/Core/Infrastructure/Pipedrive/HttpPipedriveApi.php

		-
			message: '#^PHPDoc tag @var with type Ds\\Map\<Cdn77\\Api\\Core\\Domain\\Entity\\Cdn\\CdnId, Ds\\Set\<Cdn77\\Api\\Core\\Domain\\Entity\\Cname\\Cname\>\> is not subtype of type Ds\\Map\<Cdn77\\Api\\Core\\Domain\\Entity\\Cdn\\CdnId, Ds\\Set\<\*NEVER\*\>\>\.$#'
			identifier: varTag.type
			count: 1
			path: src/Core/Infrastructure/Repository/Cname/DoctrineCnameRepository.php

		-
			message: '#^Method findTestingUntil always return list, but is marked as array\<Cdn77\\Api\\Core\\Domain\\Entity\\Customer\\Customer\>$#'
			identifier: shipmonk.returnListNotUsed
			count: 1
			path: src/Core/Infrastructure/Repository/DoctrineCustomerRepository.php

		-
			message: '#^Method resolveInteger always return list, but is marked as iterable\<0, Cdn77\\ValueObject\\Identifier\\IntegerIdentifier\>$#'
			identifier: shipmonk.returnListNotUsed
			count: 1
			path: src/CoreLibrary/ValueResolver/IdentifierValueResolver.php

		-
			message: '#^Method resolveString always return list, but is marked as iterable\<0, Cdn77\\Api\\Core\\Domain\\Value\\Identifier\\StringIdentifier\>$#'
			identifier: shipmonk.returnListNotUsed
			count: 1
			path: src/CoreLibrary/ValueResolver/IdentifierValueResolver.php

		-
			message: '#^Method resolve always return list, but is marked as iterable\<Cdn77\\Api\\Core\\Domain\\Value\\IpAddress\|null\>$#'
			identifier: shipmonk.returnListNotUsed
			count: 1
			path: src/CoreLibrary/ValueResolver/IpAddressValueResolver.php

		-
			message: '#^Parameter \#2 \$targetCurrencies of method Cdn77\\Api\\Currency\\Domain\\Api\\CurrencyApi\:\:getExchangeRates\(\) expects Ds\\Set\<Cdn77\\Api\\CoreLibrary\\Money\\CurrencyCode\>, Ds\\Set\<Cdn77\\Api\\CoreLibrary\\Money\\CurrencyCode\:\:BRL\|Cdn77\\Api\\CoreLibrary\\Money\\CurrencyCode\:\:CZK\|Cdn77\\Api\\CoreLibrary\\Money\\CurrencyCode\:\:EUR\|Cdn77\\Api\\CoreLibrary\\Money\\CurrencyCode\:\:GBP\> given\.$#'
			identifier: argument.type
			count: 1
			path: src/Currency/Domain/Command/FetchExchangeRatesHandler.php

		-
			message: '#^Parameter \#1 \$contracts of class Cdn77\\Api\\CustomPlan\\Application\\Payload\\ContractsOverviewSchema constructor expects list\<Cdn77\\Api\\CustomPlan\\Application\\Payload\\Response\\ContractOverviewSchema\>, array\<int, Cdn77\\Api\\CustomPlan\\Application\\Payload\\Response\\ContractOverviewSchema\> given\.$#'
			identifier: argument.type
			count: 1
			path: src/CustomPlan/Application/Payload/ContractsOverviewSchema.php

		-
			message: '#^Using nullsafe property access "\?\-\>volume" on left side of \?\? is unnecessary\. Use \-\> instead\.$#'
			identifier: nullsafe.neverNull
			count: 1
			path: src/CustomPlan/Domain/Resolver/NextContractResolver.php

		-
			message: '#^Parameter \#8 \$streamingFormatTypes of class Cdn77\\Api\\Customer\\Application\\Payload\\ConfigurationSchema constructor expects Ds\\Set\<Cdn77\\Api\\Stats\\Domain\\Value\\StreamingFormatType\>\|null, Ds\\Set\<covariant Cdn77\\Api\\Stats\\Domain\\Value\\StreamingFormatType\>\|null given\.$#'
			identifier: argument.type
			count: 1
			path: src/Customer/Application/Payload/ConfigurationSchema.php

		-
			message: '#^Call to static method Webmozart\\Assert\\Assert\:\:keyExists\(\) with non\-empty\-list\<string\> and 0 will always evaluate to true\.$#'
			identifier: staticMethod.alreadyNarrowedType
			count: 1
			path: src/Customer/Domain/ApiPasswordEncryptor.php

		-
			message: '#^Using nullsafe property access "\?\-\>fullName" on left side of \?\? is unnecessary\. Use \-\> instead\.$#'
			identifier: nullsafe.neverNull
			count: 1
			path: src/Customer/Domain/Command/Pipedrive/AddLeadToPipedriveHandler.php

		-
			message: '#^Parameter \#2 \$searchFields of class Cdn77\\Api\\Core\\Domain\\Pipedrive\\Dto\\SearchLeadsScope constructor expects Ds\\Set\<Cdn77\\Api\\Core\\Domain\\Pipedrive\\Enum\\LeadSearchField\>, Ds\\Set\<Cdn77\\Api\\Core\\Domain\\Pipedrive\\Enum\\LeadSearchField\:\:CustomFields\> given\.$#'
			identifier: argument.type
			count: 1
			path: src/Customer/Domain/Command/Pipedrive/ArchivePipedriveLeadsHandler.php

		-
			message: '#^Parameter \#2 \$searchFields of class Cdn77\\Api\\Core\\Domain\\Pipedrive\\Dto\\SearchLeadsScope constructor expects Ds\\Set\<Cdn77\\Api\\Core\\Domain\\Pipedrive\\Enum\\LeadSearchField\>, Ds\\Set\<Cdn77\\Api\\Core\\Domain\\Pipedrive\\Enum\\LeadSearchField\:\:CustomFields\> given\.$#'
			identifier: argument.type
			count: 1
			path: src/Customer/Domain/Command/Pipedrive/ConvertPipedriveLeadsToWonDealHandler.php

		-
			message: '#^Method Cdn77\\Api\\Customer\\Domain\\Enum\\Rating\:\:important\(\) should return Ds\\Set\<Cdn77\\Api\\Customer\\Domain\\Enum\\Rating\> but returns Ds\\Set\<Cdn77\\Api\\Customer\\Domain\\Enum\\Rating\:\:Top\|Cdn77\\Api\\Customer\\Domain\\Enum\\Rating\:\:Vip\>\.$#'
			identifier: return.type
			count: 1
			path: src/Customer/Domain/Enum/Rating.php

		-
			message: '#^Method handle always return list, but is marked as array\<Cdn77\\Api\\Customer\\Domain\\Dto\\TeamMember\>$#'
			identifier: shipmonk.returnListNotUsed
			count: 1
			path: src/Customer/Domain/Query/FindCustomerTeamMembersHandler.php

		-
			message: '#^Method findAllWithTraffic always return list, but is marked as array\<Cdn77\\Api\\Core\\Domain\\Entity\\Customer\\CustomerTraffic\>$#'
			identifier: shipmonk.returnListNotUsed
			count: 1
			path: src/Customer/Infrastructure/Repository/DoctrineCustomerTrafficRepository.php

		-
			message: '#^PHPDoc tag @var with type list\<array\<string, float\|string\>\> is not subtype of native type Traversable\.$#'
			identifier: varTag.nativeType
			count: 1
			path: src/Datacenter/Infrastructure/Finder/DbalDatacenterDetailFinder.php

		-
			message: '#^Property Cdn77\\Api\\GraphQL\\Domain\\DataResolution\\DataProviderQuery\:\:\$filters \(Ds\\Map\<Cdn77\\Api\\GraphQL\\Domain\\DataResolution\\FieldId, bool\|int\|list\<mixed\>\|object\|string\>\) does not accept Ds\\Map\<mixed, mixed\>\.$#'
			identifier: assign.propertyType
			count: 1
			path: src/GraphQL/Domain/DataResolution/DataProviderQuery.php

		-
			message: '#^Method Cdn77\\Api\\GraphQL\\Domain\\Schema\\Type\\Scalar\\ArrayType\:\:parseLiteral\(\) should return array\<mixed\> but returns mixed\.$#'
			identifier: return.type
			count: 1
			path: src/GraphQL/Domain/Schema/Type/Scalar/ArrayType.php

		-
			message: '#^Using nullsafe property access "\?\-\>name" on left side of \?\? is unnecessary\. Use \-\> instead\.$#'
			identifier: nullsafe.neverNull
			count: 1
			path: src/Inquiry/Domain/Command/SendInquiryHandler.php

		-
			message: '#^PHPDoc tag @var with type float\|null is not subtype of type float\.$#'
			identifier: varTag.type
			count: 1
			path: src/Invoice/Domain/InvoiceLineFactory.php

		-
			message: '#^Parameter \#1 \$key of method Ds\\Map\<covariant Cdn77\\Api\\Core\\Domain\\Messaging\\CommandBuffer\\Value\\Topic,int\>\:\:get\(\) expects never, Cdn77\\Api\\Core\\Domain\\Messaging\\CommandBuffer\\Value\\Topic\:\:Cdn77UpdateCdns\|Cdn77\\Api\\Core\\Domain\\Messaging\\CommandBuffer\\Value\\Topic\:\:Generic\|Cdn77\\Api\\Core\\Domain\\Messaging\\CommandBuffer\\Value\\Topic\:\:Job|Cdn77\\Api\\Core\\Domain\\Messaging\\CommandBuffer\\Value\\Topic\:\:MonthlyPlanRenewal given\.$#'
			identifier: argument.type
			count: 1
			path: src/Monitoring/Overkill/Domain/MetricFactory/CommandBufferUnprocessedCountMetricsFactory.php

		-
			message: '#^Parameter \#1 \$key of method Ds\\Map\<covariant Cdn77\\Api\\Core\\Domain\\Messaging\\CommandBuffer\\Value\\Topic,int\>\:\:get\(\) expects never, Cdn77\\Api\\Core\\Domain\\Messaging\\CommandBuffer\\Value\\Topic\:\:Cdn77UpdateCdns\|Cdn77\\Api\\Core\\Domain\\Messaging\\CommandBuffer\\Value\\Topic\:\:Generic\|Cdn77\\Api\\Core\\Domain\\Messaging\\CommandBuffer\\Value\\Topic\:\:Job|Cdn77\\Api\\Core\\Domain\\Messaging\\CommandBuffer\\Value\\Topic\:\:MonthlyPlanRenewal given\.$#'
			identifier: argument.type
			count: 1
			path: src/Monitoring/Overkill/Domain/MetricFactory/CommandBufferUnprocessedLongestDurationMetricsFactory.php

		-
			message: '#^Parameter \#2 \$mapper of function Cdn77\\Functions\\mapFromIterable expects callable\(int\<0, max\>, array\{aggregation_unit\: string, timestamp\: int\}\)\: Ds\\Pair\<Cdn77\\Api\\Core\\Domain\\Value\\AggregationUnit, mixed\>, Closure\(mixed, array\)\: Ds\\Pair\<Cdn77\\Api\\Core\\Domain\\Value\\AggregationUnit, int\> given\.$#'
			identifier: argument.type
			count: 1
			path: src/Monitoring/Overkill/Infrastructure/Finder/ClickhouseCephObjectStorageEgressTrafficLatestTimestampFinder.php

		-
			message: '#^Parameter \#2 \$mapper of function Cdn77\\Functions\\mapFromIterable expects callable\(int\<0, max\>, array\{topic\: string, count\: int\}\)\: Ds\\Pair\<Cdn77\\Api\\Core\\Domain\\Messaging\\CommandBuffer\\Value\\Topic, mixed\>, Closure\(int, array\)\: Ds\\Pair\<Cdn77\\Api\\Core\\Domain\\Messaging\\CommandBuffer\\Value\\Topic, int\> given\.$#'
			identifier: argument.type
			count: 1
			path: src/Monitoring/Overkill/Infrastructure/Finder/DbalCommandBufferFinderFinder.php

		-
			message: '#^Parameter \#2 \$mapper of function Cdn77\\Functions\\mapFromIterable expects callable\(int\<0, max\>, array\{topic\: string, duration_sec\: int\}\)\: Ds\\Pair\<Cdn77\\Api\\Core\\Domain\\Messaging\\CommandBuffer\\Value\\Topic, mixed\>, Closure\(int, array\)\: Ds\\Pair\<Cdn77\\Api\\Core\\Domain\\Messaging\\CommandBuffer\\Value\\Topic, int\> given\.$#'
			identifier: argument.type
			count: 1
			path: src/Monitoring/Overkill/Infrastructure/Finder/DbalCommandBufferFinderFinder.php

		-
			message: '#^PHPDoc tag @var with type list\<array\<string, int\|string\>\> is not subtype of native type Traversable\.$#'
			identifier: varTag.nativeType
			count: 1
			path: src/Monitoring/Overkill/Infrastructure/Finder/DbalCustomerBandwidthFinder.php

		-
			message: '#^Parameter \#1 \$rgwPricing of class Cdn77\\Api\\ObjectStorage\\Application\\Payload\\Response\\RgwPricingListSchema constructor expects list\<Cdn77\\Api\\ObjectStorage\\Application\\Payload\\Response\\RgwPricingSchema\>, array\<int, Cdn77\\Api\\ObjectStorage\\Application\\Payload\\Response\\RgwPricingSchema\> given\.$#'
			identifier: argument.type
			count: 1
			path: src/ObjectStorage/Application/Payload/Response/RgwPricingListSchema.php

		-
			message: '#^Parameter \#1 \$clientData of static method Cdn77\\Api\\Payment\\Domain\\Dto\\WireTransferDTO\:\:fromPaygateClientData\(\) expects array\{bankId\: string, invoiceNumber\: string, paymentMethod\: string, transactionId\: string\}, non\-empty\-array given\.$#'
			identifier: argument.type
			count: 1
			path: src/Payment/Domain/Command/AddWireTransferPayment.php

		-
			message: '#^Parameter \#2 \$mapper of function Cdn77\\Functions\\mapFromIterable expects callable\(int\<0, max\>, array\{capability\: string, name\: string\}\)\: Ds\\Pair\<mixed, mixed\>, Closure\(mixed, array\)\: Ds\\Pair\<string, string\> given\.$#'
			identifier: argument.type
			count: 1
			path: src/Public/Infrastructure/TLS/HttpTlsTester.php

		-
			message: '#^Parameter \#1 \$rates of class Cdn77\\Api\\Rate\\Application\\Payload\\RatesSchema constructor expects list\<Cdn77\\Api\\Rate\\Application\\Payload\\RateSchema\>, array\<int, Cdn77\\Api\\Rate\\Application\\Payload\\RateSchema\> given\.$#'
			identifier: argument.type
			count: 1
			path: src/Rate/Application/Payload/RatesSchema.php

		-
			message: '#^Method getForCustomer always return list, but is marked as array\<Cdn77\\Api\\Core\\Domain\\Entity\\Rate\\Rate\>$#'
			identifier: shipmonk.returnListNotUsed
			count: 1
			path: src/Rate/Infrastructure/Repository/DoctrineRateRepository.php

		-
			message: '#^Parameter \#1 \$sampleLogs of class Cdn77\\Api\\RawLog\\Application\\Payload\\SampleLogsSchema constructor expects list\<Cdn77\\Api\\RawLog\\Application\\Payload\\SampleLogLineSchema\>, array\<int, Cdn77\\Api\\RawLog\\Application\\Payload\\SampleLogLineSchema\> given\.$#'
			identifier: argument.type
			count: 1
			path: src/RawLog/Application/Payload/SampleLogsSchema.php

		-
			message: '#^Property Cdn77\\Api\\RawLog\\Application\\Payload\\ToggleRawLogStatusSchema\:\:\$cdnId \(int\|null\) is never assigned int so it can be removed from the property type\.$#'
			identifier: property.unusedType
			count: 1
			path: src/RawLog/Application/Payload/ToggleRawLogStatusSchema.php

		-
			message: '#^Method Cdn77\\Api\\Statistics\\Domain\\Command\\CollectAllRemoteServersStatisticsHandler\:\:handle\(\) should return list\<Cdn77\\Api\\Statistics\\Domain\\Exception\\CollectingServerStatisticsFailed\> but returns array\<Cdn77\\Api\\Statistics\\Domain\\Exception\\CollectingServerStatisticsFailed\>\.$#'
			identifier: return.type
			count: 1
			path: src/Statistics/Domain/Command/CollectAllRemoteServersStatisticsHandler.php

		-
			message: '#^Parameter \#1 \$timestamps of static method Cdn77\\Api\\Stats\\Application\\Payload\\MultiSumSchema\:\:fromArray\(\) expects array\<int, array\<string, Cdn77\\Api\\Stats\\Domain\\Value\\Sum\>\>, array\<mixed, array\<mixed, mixed\>\> given\.$#'
			identifier: argument.type
			count: 1
			path: src/Stats/Application/Payload/MultiSumSchema.php

		-
			message: '#^Parameter \#1 \$totals of static method Cdn77\\Api\\Stats\\Domain\\BandwidthPercentileCalculator\:\:fromTotals\(\) expects list\<int\>, array\<int, int\> given\.$#'
			identifier: argument.type
			count: 3
			path: src/Stats/Domain/DataTable/TableDataConverter.php

		-
			message: '#^Parameter \#1 \.\.\.\$arg1 of function max expects non\-empty\-array, array\<int, int\> given\.$#'
			identifier: argument.type
			count: 2
			path: src/Stats/Domain/DataTable/TableDataConverter.php

		-
			message: '#^Parameter \#1 \$data of class Cdn77\\Api\\Stats\\Domain\\Dto\\Stats constructor expects array\<int, array\<string, float\|int\|string\>\>, array\<array\<mixed, mixed\>\> given\.$#'
			identifier: argument.type
			count: 1
			path: src/Stats/Domain/Dto/Stats.php

		-
			message: '#^PHPDoc tag @var with type Generator\<mixed, array\<int, Cdn77\\Api\\Core\\Domain\\Entity\\Customer\\Customer\|Cdn77\\Api\\Core\\Domain\\Entity\\TikTok\\ChangeRequest\>, mixed, mixed\> is not subtype of type iterable\<int, Cdn77\\Api\\Core\\Domain\\Entity\\Customer\\Customer\|Cdn77\\Api\\Core\\Domain\\Entity\\TikTok\\ChangeRequest\>\.$#'
			identifier: varTag.type
			count: 1
			path: src/TikTok/Infrastructure/Repository/DoctrineChangeRequestRepository.php

		-
			message: '#^Parameter \#2 \$mapper of function Cdn77\\Functions\\mapFromIterable expects callable\(string, list\<int\>\)\: Ds\\Pair\<Cdn77\\Api\\Cdn\\Domain\\Value\\Parameters\\MaxAgeType, Ds\\Set\<mixed\>\>, Closure\(string, array\)\: Ds\\Pair\<Cdn77\\Api\\Cdn\\Domain\\Value\\Parameters\\MaxAgeType, Ds\\Set\<int\>\> given\.$#'
			identifier: argument.type
			count: 2
			path: tests/Cdn/Domain/Validation/MaxAgeValidatorTest.php
