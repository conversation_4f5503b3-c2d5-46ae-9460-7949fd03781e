<?php

declare(strict_types=1);

use Cdn77\Api\GraphQL\Application\Runtime\Server;
use Cdn77\Api\Kernel;
use Dotenv\Dotenv;
use GraphQL\Executor\ExecutionResult;
use GraphQL\Server\OperationParams;
use Spawnia\Sailor\Client;
use Spawnia\Sailor\Client\Guzzle;
use Spawnia\Sailor\Codegen\DirectoryFinder;
use Spawnia\Sailor\Codegen\Finder;
use Spawnia\Sailor\EndpointConfig;
use Spawnia\Sailor\Response;
use Symfony\Component\DependencyInjection\Exception\ServiceCircularReferenceException;
use Symfony\Component\DependencyInjection\Exception\ServiceNotFoundException;
use Webmozart\Assert\Assert;

use function Cdn77\Functions\absurd;

$dotenv = Dotenv::createImmutable(__DIR__);
$dotenv->safeLoad();

return [
    'flop' => new class extends EndpointConfig {
        public function makeClient(): Client
        {
            $flopUrl = $_ENV['FLOP_URL_GRAPHQL'];
            Assert::string($flopUrl);

            return new Guzzle(
                $flopUrl,
                [
                    'headers' => [
                        'Authorization' => 'Bearer ' . $_ENV['FLOP_AUTH_TOKEN'],
                    ],
                ],
            );
        }

        public function namespace(): string
        {
            return 'Generated\\Sailor\\FlopApi';
        }

        public function targetPath(): string
        {
            return __DIR__ . '/graphql/Generated/Sailor/FlopApi';
        }

        public function schemaPath(): string
        {
            return __DIR__ . '/flop.graphqls';
        }

        public function finder(): Finder
        {
            return new DirectoryFinder(__DIR__ . '/graphql/flop');
        }
    },
    'clap' => new class extends EndpointConfig {
        public function makeClient(): Client
        {
            /** @var Kernel $kernel */
            $kernel = require __DIR__ . '/bootstrap.php';

            $kernel->boot();

            $container = $kernel->getContainer();
            try {
                $server = $container->get(Server::class);
            } catch (ServiceCircularReferenceException | ServiceNotFoundException) {
                absurd();
            }

            return new class ($server) implements Client {
                public function __construct(private readonly Server $server)
                {
                }

                /** @throws Exception */
                public function request(string $query, stdClass|null $variables = null): Response
                {
                    $result = $this->server->executeRequest(
                        OperationParams::create(
                            [
                                'query' => $query,
                                'variables' => (array) $variables,
                            ],
                        ),
                    );
                    Assert::isInstanceOf($result, ExecutionResult::class);

                    return Response::fromExecutionResult($result);
                }
            };
        }

        public function namespace(): string
        {
            return 'Generated\\Sailor\\ClapApi';
        }

        public function targetPath(): string
        {
            return __DIR__ . '/graphql/Generated/Sailor/ClapApi';
        }

        public function schemaPath(): string
        {
            return __DIR__ . '/clap.graphqls';
        }

        public function finder(): Finder
        {
            return new DirectoryFinder(__DIR__ . '/graphql/clap');
        }
    },
];
